(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function s(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function r(o){if(o.ep)return;o.ep=!0;const f=s(o);fetch(o.href,f)}})();function Vf(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var nf={exports:{}},Ns={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X0;function C1(){if(X0)return Ns;X0=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(r,o,f){var d=null;if(f!==void 0&&(d=""+f),o.key!==void 0&&(d=""+o.key),"key"in o){f={};for(var h in o)h!=="key"&&(f[h]=o[h])}else f=o;return o=f.ref,{$$typeof:l,type:r,key:d,ref:o!==void 0?o:null,props:f}}return Ns.Fragment=a,Ns.jsx=s,Ns.jsxs=s,Ns}var Z0;function j1(){return Z0||(Z0=1,nf.exports=C1()),nf.exports}var v=j1(),lf={exports:{}},Ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q0;function D1(){if(Q0)return Ce;Q0=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),S=Symbol.iterator;function x(R){return R===null||typeof R!="object"?null:(R=S&&R[S]||R["@@iterator"],typeof R=="function"?R:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,D={};function C(R,Y,ue){this.props=R,this.context=Y,this.refs=D,this.updater=ue||T}C.prototype.isReactComponent={},C.prototype.setState=function(R,Y){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,Y,"setState")},C.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function w(){}w.prototype=C.prototype;function j(R,Y,ue){this.props=R,this.context=Y,this.refs=D,this.updater=ue||T}var M=j.prototype=new w;M.constructor=j,E(M,C.prototype),M.isPureReactComponent=!0;var G=Array.isArray,H={H:null,A:null,T:null,S:null,V:null},te=Object.prototype.hasOwnProperty;function I(R,Y,ue,ae,pe,je){return ue=je.ref,{$$typeof:l,type:R,key:Y,ref:ue!==void 0?ue:null,props:je}}function K(R,Y){return I(R.type,Y,void 0,void 0,void 0,R.props)}function ne(R){return typeof R=="object"&&R!==null&&R.$$typeof===l}function xe(R){var Y={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(ue){return Y[ue]})}var ce=/\/+/g;function W(R,Y){return typeof R=="object"&&R!==null&&R.key!=null?xe(""+R.key):Y.toString(36)}function re(){}function fe(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(re,re):(R.status="pending",R.then(function(Y){R.status==="pending"&&(R.status="fulfilled",R.value=Y)},function(Y){R.status==="pending"&&(R.status="rejected",R.reason=Y)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function ve(R,Y,ue,ae,pe){var je=typeof R;(je==="undefined"||je==="boolean")&&(R=null);var we=!1;if(R===null)we=!0;else switch(je){case"bigint":case"string":case"number":we=!0;break;case"object":switch(R.$$typeof){case l:case a:we=!0;break;case g:return we=R._init,ve(we(R._payload),Y,ue,ae,pe)}}if(we)return pe=pe(R),we=ae===""?"."+W(R,0):ae,G(pe)?(ue="",we!=null&&(ue=we.replace(ce,"$&/")+"/"),ve(pe,Y,ue,"",function(St){return St})):pe!=null&&(ne(pe)&&(pe=K(pe,ue+(pe.key==null||R&&R.key===pe.key?"":(""+pe.key).replace(ce,"$&/")+"/")+we)),Y.push(pe)),1;we=0;var it=ae===""?".":ae+":";if(G(R))for(var Re=0;Re<R.length;Re++)ae=R[Re],je=it+W(ae,Re),we+=ve(ae,Y,ue,je,pe);else if(Re=x(R),typeof Re=="function")for(R=Re.call(R),Re=0;!(ae=R.next()).done;)ae=ae.value,je=it+W(ae,Re++),we+=ve(ae,Y,ue,je,pe);else if(je==="object"){if(typeof R.then=="function")return ve(fe(R),Y,ue,ae,pe);throw Y=String(R),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return we}function q(R,Y,ue){if(R==null)return R;var ae=[],pe=0;return ve(R,ae,"","",function(je){return Y.call(ue,je,pe++)}),ae}function P(R){if(R._status===-1){var Y=R._result;Y=Y(),Y.then(function(ue){(R._status===0||R._status===-1)&&(R._status=1,R._result=ue)},function(ue){(R._status===0||R._status===-1)&&(R._status=2,R._result=ue)}),R._status===-1&&(R._status=0,R._result=Y)}if(R._status===1)return R._result.default;throw R._result}var le=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function _e(){}return Ce.Children={map:q,forEach:function(R,Y,ue){q(R,function(){Y.apply(this,arguments)},ue)},count:function(R){var Y=0;return q(R,function(){Y++}),Y},toArray:function(R){return q(R,function(Y){return Y})||[]},only:function(R){if(!ne(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},Ce.Component=C,Ce.Fragment=s,Ce.Profiler=o,Ce.PureComponent=j,Ce.StrictMode=r,Ce.Suspense=p,Ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=H,Ce.__COMPILER_RUNTIME={__proto__:null,c:function(R){return H.H.useMemoCache(R)}},Ce.cache=function(R){return function(){return R.apply(null,arguments)}},Ce.cloneElement=function(R,Y,ue){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var ae=E({},R.props),pe=R.key,je=void 0;if(Y!=null)for(we in Y.ref!==void 0&&(je=void 0),Y.key!==void 0&&(pe=""+Y.key),Y)!te.call(Y,we)||we==="key"||we==="__self"||we==="__source"||we==="ref"&&Y.ref===void 0||(ae[we]=Y[we]);var we=arguments.length-2;if(we===1)ae.children=ue;else if(1<we){for(var it=Array(we),Re=0;Re<we;Re++)it[Re]=arguments[Re+2];ae.children=it}return I(R.type,pe,void 0,void 0,je,ae)},Ce.createContext=function(R){return R={$$typeof:d,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:f,_context:R},R},Ce.createElement=function(R,Y,ue){var ae,pe={},je=null;if(Y!=null)for(ae in Y.key!==void 0&&(je=""+Y.key),Y)te.call(Y,ae)&&ae!=="key"&&ae!=="__self"&&ae!=="__source"&&(pe[ae]=Y[ae]);var we=arguments.length-2;if(we===1)pe.children=ue;else if(1<we){for(var it=Array(we),Re=0;Re<we;Re++)it[Re]=arguments[Re+2];pe.children=it}if(R&&R.defaultProps)for(ae in we=R.defaultProps,we)pe[ae]===void 0&&(pe[ae]=we[ae]);return I(R,je,void 0,void 0,null,pe)},Ce.createRef=function(){return{current:null}},Ce.forwardRef=function(R){return{$$typeof:h,render:R}},Ce.isValidElement=ne,Ce.lazy=function(R){return{$$typeof:g,_payload:{_status:-1,_result:R},_init:P}},Ce.memo=function(R,Y){return{$$typeof:m,type:R,compare:Y===void 0?null:Y}},Ce.startTransition=function(R){var Y=H.T,ue={};H.T=ue;try{var ae=R(),pe=H.S;pe!==null&&pe(ue,ae),typeof ae=="object"&&ae!==null&&typeof ae.then=="function"&&ae.then(_e,le)}catch(je){le(je)}finally{H.T=Y}},Ce.unstable_useCacheRefresh=function(){return H.H.useCacheRefresh()},Ce.use=function(R){return H.H.use(R)},Ce.useActionState=function(R,Y,ue){return H.H.useActionState(R,Y,ue)},Ce.useCallback=function(R,Y){return H.H.useCallback(R,Y)},Ce.useContext=function(R){return H.H.useContext(R)},Ce.useDebugValue=function(){},Ce.useDeferredValue=function(R,Y){return H.H.useDeferredValue(R,Y)},Ce.useEffect=function(R,Y,ue){var ae=H.H;if(typeof ue=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ae.useEffect(R,Y)},Ce.useId=function(){return H.H.useId()},Ce.useImperativeHandle=function(R,Y,ue){return H.H.useImperativeHandle(R,Y,ue)},Ce.useInsertionEffect=function(R,Y){return H.H.useInsertionEffect(R,Y)},Ce.useLayoutEffect=function(R,Y){return H.H.useLayoutEffect(R,Y)},Ce.useMemo=function(R,Y){return H.H.useMemo(R,Y)},Ce.useOptimistic=function(R,Y){return H.H.useOptimistic(R,Y)},Ce.useReducer=function(R,Y,ue){return H.H.useReducer(R,Y,ue)},Ce.useRef=function(R){return H.H.useRef(R)},Ce.useState=function(R){return H.H.useState(R)},Ce.useSyncExternalStore=function(R,Y,ue){return H.H.useSyncExternalStore(R,Y,ue)},Ce.useTransition=function(){return H.H.useTransition()},Ce.version="19.1.0",Ce}var K0;function Ff(){return K0||(K0=1,lf.exports=D1()),lf.exports}var A=Ff();const Pe=Vf(A);var af={exports:{}},Us={},sf={exports:{}},rf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var J0;function N1(){return J0||(J0=1,function(l){function a(q,P){var le=q.length;q.push(P);e:for(;0<le;){var _e=le-1>>>1,R=q[_e];if(0<o(R,P))q[_e]=P,q[le]=R,le=_e;else break e}}function s(q){return q.length===0?null:q[0]}function r(q){if(q.length===0)return null;var P=q[0],le=q.pop();if(le!==P){q[0]=le;e:for(var _e=0,R=q.length,Y=R>>>1;_e<Y;){var ue=2*(_e+1)-1,ae=q[ue],pe=ue+1,je=q[pe];if(0>o(ae,le))pe<R&&0>o(je,ae)?(q[_e]=je,q[pe]=le,_e=pe):(q[_e]=ae,q[ue]=le,_e=ue);else if(pe<R&&0>o(je,le))q[_e]=je,q[pe]=le,_e=pe;else break e}}return P}function o(q,P){var le=q.sortIndex-P.sortIndex;return le!==0?le:q.id-P.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var p=[],m=[],g=1,S=null,x=3,T=!1,E=!1,D=!1,C=!1,w=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,M=typeof setImmediate<"u"?setImmediate:null;function G(q){for(var P=s(m);P!==null;){if(P.callback===null)r(m);else if(P.startTime<=q)r(m),P.sortIndex=P.expirationTime,a(p,P);else break;P=s(m)}}function H(q){if(D=!1,G(q),!E)if(s(p)!==null)E=!0,te||(te=!0,W());else{var P=s(m);P!==null&&ve(H,P.startTime-q)}}var te=!1,I=-1,K=5,ne=-1;function xe(){return C?!0:!(l.unstable_now()-ne<K)}function ce(){if(C=!1,te){var q=l.unstable_now();ne=q;var P=!0;try{e:{E=!1,D&&(D=!1,j(I),I=-1),T=!0;var le=x;try{t:{for(G(q),S=s(p);S!==null&&!(S.expirationTime>q&&xe());){var _e=S.callback;if(typeof _e=="function"){S.callback=null,x=S.priorityLevel;var R=_e(S.expirationTime<=q);if(q=l.unstable_now(),typeof R=="function"){S.callback=R,G(q),P=!0;break t}S===s(p)&&r(p),G(q)}else r(p);S=s(p)}if(S!==null)P=!0;else{var Y=s(m);Y!==null&&ve(H,Y.startTime-q),P=!1}}break e}finally{S=null,x=le,T=!1}P=void 0}}finally{P?W():te=!1}}}var W;if(typeof M=="function")W=function(){M(ce)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,fe=re.port2;re.port1.onmessage=ce,W=function(){fe.postMessage(null)}}else W=function(){w(ce,0)};function ve(q,P){I=w(function(){q(l.unstable_now())},P)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(q){q.callback=null},l.unstable_forceFrameRate=function(q){0>q||125<q?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<q?Math.floor(1e3/q):5},l.unstable_getCurrentPriorityLevel=function(){return x},l.unstable_next=function(q){switch(x){case 1:case 2:case 3:var P=3;break;default:P=x}var le=x;x=P;try{return q()}finally{x=le}},l.unstable_requestPaint=function(){C=!0},l.unstable_runWithPriority=function(q,P){switch(q){case 1:case 2:case 3:case 4:case 5:break;default:q=3}var le=x;x=q;try{return P()}finally{x=le}},l.unstable_scheduleCallback=function(q,P,le){var _e=l.unstable_now();switch(typeof le=="object"&&le!==null?(le=le.delay,le=typeof le=="number"&&0<le?_e+le:_e):le=_e,q){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=le+R,q={id:g++,callback:P,priorityLevel:q,startTime:le,expirationTime:R,sortIndex:-1},le>_e?(q.sortIndex=le,a(m,q),s(p)===null&&q===s(m)&&(D?(j(I),I=-1):D=!0,ve(H,le-_e))):(q.sortIndex=R,a(p,q),E||T||(E=!0,te||(te=!0,W()))),q},l.unstable_shouldYield=xe,l.unstable_wrapCallback=function(q){var P=x;return function(){var le=x;x=P;try{return q.apply(this,arguments)}finally{x=le}}}}(rf)),rf}var P0;function U1(){return P0||(P0=1,sf.exports=N1()),sf.exports}var uf={exports:{}},kt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W0;function M1(){if(W0)return kt;W0=1;var l=Ff();function a(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(p,m,g){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:S==null?null:""+S,children:p,containerInfo:m,implementation:g}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return kt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,kt.createPortal=function(p,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return f(p,m,null,g)},kt.flushSync=function(p){var m=d.T,g=r.p;try{if(d.T=null,r.p=2,p)return p()}finally{d.T=m,r.p=g,r.d.f()}},kt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,r.d.C(p,m))},kt.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},kt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin),x=typeof m.integrity=="string"?m.integrity:void 0,T=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?r.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:x,fetchPriority:T}):g==="script"&&r.d.X(p,{crossOrigin:S,integrity:x,fetchPriority:T,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},kt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=h(m.as,m.crossOrigin);r.d.M(p,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&r.d.M(p)},kt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin);r.d.L(p,g,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},kt.preloadModule=function(p,m){if(typeof p=="string")if(m){var g=h(m.as,m.crossOrigin);r.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else r.d.m(p)},kt.requestFormReset=function(p){r.d.r(p)},kt.unstable_batchedUpdates=function(p,m){return p(m)},kt.useFormState=function(p,m,g){return d.H.useFormState(p,m,g)},kt.useFormStatus=function(){return d.H.useHostTransitionStatus()},kt.version="19.1.0",kt}var I0;function L1(){if(I0)return uf.exports;I0=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),uf.exports=M1(),uf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ep;function z1(){if(ep)return Us;ep=1;var l=U1(),a=Ff(),s=L1();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(r(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(r(188));return t!==e?null:e}for(var n=e,i=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(i=u.return,i!==null){n=i;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return h(u),e;if(c===i)return h(u),t;c=c.sibling}throw Error(r(188))}if(n.return!==i.return)n=u,i=c;else{for(var y=!1,b=u.child;b;){if(b===n){y=!0,n=u,i=c;break}if(b===i){y=!0,i=u,n=c;break}b=b.sibling}if(!y){for(b=c.child;b;){if(b===n){y=!0,n=c,i=u;break}if(b===i){y=!0,i=c,n=u;break}b=b.sibling}if(!y)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var g=Object.assign,S=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),w=Symbol.for("react.provider"),j=Symbol.for("react.consumer"),M=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),te=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),ne=Symbol.for("react.activity"),xe=Symbol.for("react.memo_cache_sentinel"),ce=Symbol.iterator;function W(e){return e===null||typeof e!="object"?null:(e=ce&&e[ce]||e["@@iterator"],typeof e=="function"?e:null)}var re=Symbol.for("react.client.reference");function fe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===re?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case E:return"Fragment";case C:return"Profiler";case D:return"StrictMode";case H:return"Suspense";case te:return"SuspenseList";case ne:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case T:return"Portal";case M:return(e.displayName||"Context")+".Provider";case j:return(e._context.displayName||"Context")+".Consumer";case G:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case I:return t=e.displayName||null,t!==null?t:fe(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return fe(e(t))}catch{}}return null}var ve=Array.isArray,q=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,le={pending:!1,data:null,method:null,action:null},_e=[],R=-1;function Y(e){return{current:e}}function ue(e){0>R||(e.current=_e[R],_e[R]=null,R--)}function ae(e,t){R++,_e[R]=e.current,e.current=t}var pe=Y(null),je=Y(null),we=Y(null),it=Y(null);function Re(e,t){switch(ae(we,t),ae(je,e),ae(pe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?S0(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=S0(t),e=x0(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}ue(pe),ae(pe,e)}function St(){ue(pe),ue(je),ue(we)}function At(e){e.memoizedState!==null&&ae(it,e);var t=pe.current,n=x0(t,e.type);t!==n&&(ae(je,e),ae(pe,n))}function jt(e){je.current===e&&(ue(pe),ue(je)),it.current===e&&(ue(it),Rs._currentValue=le)}var pn=Object.prototype.hasOwnProperty,Gt=l.unstable_scheduleCallback,Xn=l.unstable_cancelCallback,Dt=l.unstable_shouldYield,dl=l.unstable_requestPaint,ut=l.unstable_now,jn=l.unstable_getCurrentPriorityLevel,Ye=l.unstable_ImmediatePriority,hl=l.unstable_UserBlockingPriority,ln=l.unstable_NormalPriority,O=l.unstable_LowPriority,k=l.unstable_IdlePriority,$=l.log,oe=l.unstable_setDisableYieldValue,ee=null,J=null;function ie(e){if(typeof $=="function"&&oe(e),J&&typeof J.setStrictMode=="function")try{J.setStrictMode(ee,e)}catch{}}var be=Math.clz32?Math.clz32:Dn,We=Math.log,tt=Math.LN2;function Dn(e){return e>>>=0,e===0?32:31-(We(e)/tt|0)|0}var pt=256,qe=4194304;function Xt(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function an(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var u=0,c=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var b=i&134217727;return b!==0?(i=b&~c,i!==0?u=Xt(i):(y&=b,y!==0?u=Xt(y):n||(n=b&~e,n!==0&&(u=Xt(n))))):(b=i&~c,b!==0?u=Xt(b):y!==0?u=Xt(y):n||(n=i&~e,n!==0&&(u=Xt(n)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:u}function Zt(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Zn(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ml(){var e=pt;return pt<<=1,(pt&4194048)===0&&(pt=256),e}function pl(){var e=qe;return qe<<=1,(qe&62914560)===0&&(qe=4194304),e}function Qn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Nn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function qa(e,t,n,i,u,c){var y=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,_=e.expirationTimes,z=e.hiddenUpdates;for(n=y&~n;0<n;){var X=31-be(n),Q=1<<X;b[X]=0,_[X]=-1;var B=z[X];if(B!==null)for(z[X]=null,X=0;X<B.length;X++){var V=B[X];V!==null&&(V.lane&=-536870913)}n&=~Q}i!==0&&ot(e,i,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(y&~t))}function ot(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-be(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function qt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-be(n),u=1<<i;u&t|e[i]&t&&(e[i]|=t),n&=~u}}function Se(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ne(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Lt(){var e=P.p;return e!==0?e:(e=window.event,e===void 0?32:q0(e.type))}function yl(e,t){var n=P.p;try{return P.p=e,t()}finally{P.p=n}}var se=Math.random().toString(36).slice(2),de="__reactFiber$"+se,ye="__reactProps$"+se,Ie="__reactContainer$"+se,yt="__reactEvents$"+se,yn="__reactListeners$"+se,gn="__reactHandles$"+se,_t="__reactResources$"+se,Tt="__reactMarker$"+se;function $e(e){delete e[de],delete e[ye],delete e[yt],delete e[yn],delete e[gn]}function sn(e){var t=e[de];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ie]||n[de]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=_0(e);e!==null;){if(n=e[de])return n;e=_0(e)}return t}e=n,n=e.parentNode}return null}function kl(e){if(e=e[de]||e[Ie]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ha(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function gl(e){var t=e[_t];return t||(t=e[_t]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ct(e){e[Tt]=!0}var Bi=new Set,Hi={};function Kn(e,t){ze(e,t),ze(e+"Capture",t)}function ze(e,t){for(Hi[e]=t,e=0;e<t.length;e++)Bi.add(t[e])}var Un=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Va={},Bl={};function Qu(e){return pn.call(Bl,e)?!0:pn.call(Va,e)?!1:Un.test(e)?Bl[e]=!0:(Va[e]=!0,!1)}function Hl(e,t,n){if(Qu(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function er(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function vl(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var Ku,hd;function Fa(e){if(Ku===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ku=t&&t[1]||"",hd=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ku+e+hd}var Ju=!1;function Pu(e,t){if(!e||Ju)return"";Ju=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(V){var B=V}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(V){B=V}e.call(Q.prototype)}}else{try{throw Error()}catch(V){B=V}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(V){if(V&&B&&typeof V.stack=="string")return[V.stack,B.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),y=c[0],b=c[1];if(y&&b){var _=y.split(`
`),z=b.split(`
`);for(u=i=0;i<_.length&&!_[i].includes("DetermineComponentFrameRoot");)i++;for(;u<z.length&&!z[u].includes("DetermineComponentFrameRoot");)u++;if(i===_.length||u===z.length)for(i=_.length-1,u=z.length-1;1<=i&&0<=u&&_[i]!==z[u];)u--;for(;1<=i&&0<=u;i--,u--)if(_[i]!==z[u]){if(i!==1||u!==1)do if(i--,u--,0>u||_[i]!==z[u]){var X=`
`+_[i].replace(" at new "," at ");return e.displayName&&X.includes("<anonymous>")&&(X=X.replace("<anonymous>",e.displayName)),X}while(1<=i&&0<=u);break}}}finally{Ju=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Fa(n):""}function wg(e){switch(e.tag){case 26:case 27:case 5:return Fa(e.type);case 16:return Fa("Lazy");case 13:return Fa("Suspense");case 19:return Fa("SuspenseList");case 0:case 15:return Pu(e.type,!1);case 11:return Pu(e.type.render,!1);case 1:return Pu(e.type,!0);case 31:return Fa("Activity");default:return""}}function md(e){try{var t="";do t+=wg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function vn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function pd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ag(e){var t=pd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(y){i=""+y,c.call(this,y)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(y){i=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function tr(e){e._valueTracker||(e._valueTracker=Ag(e))}function yd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=pd(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function nr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var _g=/[\n"\\]/g;function bn(e){return e.replace(_g,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Wu(e,t,n,i,u,c,y,b){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),t!=null?y==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+vn(t)):e.value!==""+vn(t)&&(e.value=""+vn(t)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),t!=null?Iu(e,y,vn(t)):n!=null?Iu(e,y,vn(n)):i!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+vn(b):e.removeAttribute("name")}function gd(e,t,n,i,u,c,y,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+vn(n):"",t=t!=null?""+vn(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}i=i??u,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=b?e.checked:!!i,e.defaultChecked=!!i,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function Iu(e,t,n){t==="number"&&nr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ya(e,t,n,i){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&i&&(e[n].defaultSelected=!0)}else{for(n=""+vn(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function vd(e,t,n){if(t!=null&&(t=""+vn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+vn(n):""}function bd(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(r(92));if(ve(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=vn(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function $a(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Tg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Sd(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Tg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function xd(e,t,n){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var u in t)i=t[u],t.hasOwnProperty(u)&&n[u]!==i&&Sd(e,u,i)}else for(var c in t)t.hasOwnProperty(c)&&Sd(e,c,t[c])}function eo(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Og=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function lr(e){return Og.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var to=null;function no(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ga=null,Xa=null;function Ed(e){var t=kl(e);if(t&&(e=t.stateNode)){var n=e[ye]||null;e:switch(e=t.stateNode,t.type){case"input":if(Wu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+bn(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var u=i[ye]||null;if(!u)throw Error(r(90));Wu(i,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&yd(i)}break e;case"textarea":vd(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ya(e,!!n.multiple,t,!1)}}}var lo=!1;function wd(e,t,n){if(lo)return e(t,n);lo=!0;try{var i=e(t);return i}finally{if(lo=!1,(Ga!==null||Xa!==null)&&(Fr(),Ga&&(t=Ga,e=Xa,Xa=Ga=null,Ed(t),e)))for(t=0;t<e.length;t++)Ed(e[t])}}function qi(e,t){var n=e.stateNode;if(n===null)return null;var i=n[ye]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(r(231,t,typeof n));return n}var bl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ao=!1;if(bl)try{var Vi={};Object.defineProperty(Vi,"passive",{get:function(){ao=!0}}),window.addEventListener("test",Vi,Vi),window.removeEventListener("test",Vi,Vi)}catch{ao=!1}var ql=null,io=null,ar=null;function Ad(){if(ar)return ar;var e,t=io,n=t.length,i,u="value"in ql?ql.value:ql.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var y=n-e;for(i=1;i<=y&&t[n-i]===u[c-i];i++);return ar=u.slice(e,1<i?1-i:void 0)}function ir(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function sr(){return!0}function _d(){return!1}function Qt(e){function t(n,i,u,c,y){this._reactName=n,this._targetInst=u,this.type=i,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?sr:_d,this.isPropagationStopped=_d,this}return g(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=sr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=sr)},persist:function(){},isPersistent:sr}),t}var ma={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},rr=Qt(ma),Fi=g({},ma,{view:0,detail:0}),Cg=Qt(Fi),so,ro,Yi,ur=g({},Fi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:oo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yi&&(Yi&&e.type==="mousemove"?(so=e.screenX-Yi.screenX,ro=e.screenY-Yi.screenY):ro=so=0,Yi=e),so)},movementY:function(e){return"movementY"in e?e.movementY:ro}}),Td=Qt(ur),jg=g({},ur,{dataTransfer:0}),Dg=Qt(jg),Ng=g({},Fi,{relatedTarget:0}),uo=Qt(Ng),Ug=g({},ma,{animationName:0,elapsedTime:0,pseudoElement:0}),Mg=Qt(Ug),Lg=g({},ma,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),zg=Qt(Lg),kg=g({},ma,{data:0}),Rd=Qt(kg),Bg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Hg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qg[e])?!!t[e]:!1}function oo(){return Vg}var Fg=g({},Fi,{key:function(e){if(e.key){var t=Bg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ir(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Hg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:oo,charCode:function(e){return e.type==="keypress"?ir(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ir(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Yg=Qt(Fg),$g=g({},ur,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Od=Qt($g),Gg=g({},Fi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:oo}),Xg=Qt(Gg),Zg=g({},ma,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qg=Qt(Zg),Kg=g({},ur,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Jg=Qt(Kg),Pg=g({},ma,{newState:0,oldState:0}),Wg=Qt(Pg),Ig=[9,13,27,32],co=bl&&"CompositionEvent"in window,$i=null;bl&&"documentMode"in document&&($i=document.documentMode);var ev=bl&&"TextEvent"in window&&!$i,Cd=bl&&(!co||$i&&8<$i&&11>=$i),jd=" ",Dd=!1;function Nd(e,t){switch(e){case"keyup":return Ig.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ud(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Za=!1;function tv(e,t){switch(e){case"compositionend":return Ud(t);case"keypress":return t.which!==32?null:(Dd=!0,jd);case"textInput":return e=t.data,e===jd&&Dd?null:e;default:return null}}function nv(e,t){if(Za)return e==="compositionend"||!co&&Nd(e,t)?(e=Ad(),ar=io=ql=null,Za=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cd&&t.locale!=="ko"?null:t.data;default:return null}}var lv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Md(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!lv[e.type]:t==="textarea"}function Ld(e,t,n,i){Ga?Xa?Xa.push(i):Xa=[i]:Ga=i,t=Qr(t,"onChange"),0<t.length&&(n=new rr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Gi=null,Xi=null;function av(e){p0(e,0)}function or(e){var t=ha(e);if(yd(t))return e}function zd(e,t){if(e==="change")return t}var kd=!1;if(bl){var fo;if(bl){var ho="oninput"in document;if(!ho){var Bd=document.createElement("div");Bd.setAttribute("oninput","return;"),ho=typeof Bd.oninput=="function"}fo=ho}else fo=!1;kd=fo&&(!document.documentMode||9<document.documentMode)}function Hd(){Gi&&(Gi.detachEvent("onpropertychange",qd),Xi=Gi=null)}function qd(e){if(e.propertyName==="value"&&or(Xi)){var t=[];Ld(t,Xi,e,no(e)),wd(av,t)}}function iv(e,t,n){e==="focusin"?(Hd(),Gi=t,Xi=n,Gi.attachEvent("onpropertychange",qd)):e==="focusout"&&Hd()}function sv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return or(Xi)}function rv(e,t){if(e==="click")return or(t)}function uv(e,t){if(e==="input"||e==="change")return or(t)}function ov(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var rn=typeof Object.is=="function"?Object.is:ov;function Zi(e,t){if(rn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var u=n[i];if(!pn.call(t,u)||!rn(e[u],t[u]))return!1}return!0}function Vd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Fd(e,t){var n=Vd(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vd(n)}}function Yd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Yd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function $d(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=nr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=nr(e.document)}return t}function mo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var cv=bl&&"documentMode"in document&&11>=document.documentMode,Qa=null,po=null,Qi=null,yo=!1;function Gd(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;yo||Qa==null||Qa!==nr(i)||(i=Qa,"selectionStart"in i&&mo(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Qi&&Zi(Qi,i)||(Qi=i,i=Qr(po,"onSelect"),0<i.length&&(t=new rr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=Qa)))}function pa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ka={animationend:pa("Animation","AnimationEnd"),animationiteration:pa("Animation","AnimationIteration"),animationstart:pa("Animation","AnimationStart"),transitionrun:pa("Transition","TransitionRun"),transitionstart:pa("Transition","TransitionStart"),transitioncancel:pa("Transition","TransitionCancel"),transitionend:pa("Transition","TransitionEnd")},go={},Xd={};bl&&(Xd=document.createElement("div").style,"AnimationEvent"in window||(delete Ka.animationend.animation,delete Ka.animationiteration.animation,delete Ka.animationstart.animation),"TransitionEvent"in window||delete Ka.transitionend.transition);function ya(e){if(go[e])return go[e];if(!Ka[e])return e;var t=Ka[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Xd)return go[e]=t[n];return e}var Zd=ya("animationend"),Qd=ya("animationiteration"),Kd=ya("animationstart"),fv=ya("transitionrun"),dv=ya("transitionstart"),hv=ya("transitioncancel"),Jd=ya("transitionend"),Pd=new Map,vo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vo.push("scrollEnd");function Mn(e,t){Pd.set(e,t),Kn(t,[e])}var Wd=new WeakMap;function Sn(e,t){if(typeof e=="object"&&e!==null){var n=Wd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:md(t)},Wd.set(e,t),t)}return{value:e,source:t,stack:md(t)}}var xn=[],Ja=0,bo=0;function cr(){for(var e=Ja,t=bo=Ja=0;t<e;){var n=xn[t];xn[t++]=null;var i=xn[t];xn[t++]=null;var u=xn[t];xn[t++]=null;var c=xn[t];if(xn[t++]=null,i!==null&&u!==null){var y=i.pending;y===null?u.next=u:(u.next=y.next,y.next=u),i.pending=u}c!==0&&Id(n,u,c)}}function fr(e,t,n,i){xn[Ja++]=e,xn[Ja++]=t,xn[Ja++]=n,xn[Ja++]=i,bo|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function So(e,t,n,i){return fr(e,t,n,i),dr(e)}function Pa(e,t){return fr(e,null,null,t),dr(e)}function Id(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var u=!1,c=e.return;c!==null;)c.childLanes|=n,i=c.alternate,i!==null&&(i.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-be(n),e=c.hiddenUpdates,i=e[u],i===null?e[u]=[t]:i.push(t),t.lane=n|536870912),c):null}function dr(e){if(50<bs)throw bs=0,Tc=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Wa={};function mv(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function un(e,t,n,i){return new mv(e,t,n,i)}function xo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Sl(e,t){var n=e.alternate;return n===null?(n=un(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function eh(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function hr(e,t,n,i,u,c){var y=0;if(i=e,typeof e=="function")xo(e)&&(y=1);else if(typeof e=="string")y=y1(e,n,pe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ne:return e=un(31,n,t,u),e.elementType=ne,e.lanes=c,e;case E:return ga(n.children,u,c,t);case D:y=8,u|=24;break;case C:return e=un(12,n,t,u|2),e.elementType=C,e.lanes=c,e;case H:return e=un(13,n,t,u),e.elementType=H,e.lanes=c,e;case te:return e=un(19,n,t,u),e.elementType=te,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case w:case M:y=10;break e;case j:y=9;break e;case G:y=11;break e;case I:y=14;break e;case K:y=16,i=null;break e}y=29,n=Error(r(130,e===null?"null":typeof e,"")),i=null}return t=un(y,n,t,u),t.elementType=e,t.type=i,t.lanes=c,t}function ga(e,t,n,i){return e=un(7,e,i,t),e.lanes=n,e}function Eo(e,t,n){return e=un(6,e,null,t),e.lanes=n,e}function wo(e,t,n){return t=un(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ia=[],ei=0,mr=null,pr=0,En=[],wn=0,va=null,xl=1,El="";function ba(e,t){Ia[ei++]=pr,Ia[ei++]=mr,mr=e,pr=t}function th(e,t,n){En[wn++]=xl,En[wn++]=El,En[wn++]=va,va=e;var i=xl;e=El;var u=32-be(i)-1;i&=~(1<<u),n+=1;var c=32-be(t)+u;if(30<c){var y=u-u%5;c=(i&(1<<y)-1).toString(32),i>>=y,u-=y,xl=1<<32-be(t)+u|n<<u|i,El=c+e}else xl=1<<c|n<<u|i,El=e}function Ao(e){e.return!==null&&(ba(e,1),th(e,1,0))}function _o(e){for(;e===mr;)mr=Ia[--ei],Ia[ei]=null,pr=Ia[--ei],Ia[ei]=null;for(;e===va;)va=En[--wn],En[wn]=null,El=En[--wn],En[wn]=null,xl=En[--wn],En[wn]=null}var Vt=null,st=null,He=!1,Sa=null,Jn=!1,To=Error(r(519));function xa(e){var t=Error(r(418,""));throw Pi(Sn(t,e)),To}function nh(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[de]=e,t[ye]=i,n){case"dialog":Le("cancel",t),Le("close",t);break;case"iframe":case"object":case"embed":Le("load",t);break;case"video":case"audio":for(n=0;n<xs.length;n++)Le(xs[n],t);break;case"source":Le("error",t);break;case"img":case"image":case"link":Le("error",t),Le("load",t);break;case"details":Le("toggle",t);break;case"input":Le("invalid",t),gd(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),tr(t);break;case"select":Le("invalid",t);break;case"textarea":Le("invalid",t),bd(t,i.value,i.defaultValue,i.children),tr(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||b0(t.textContent,n)?(i.popover!=null&&(Le("beforetoggle",t),Le("toggle",t)),i.onScroll!=null&&Le("scroll",t),i.onScrollEnd!=null&&Le("scrollend",t),i.onClick!=null&&(t.onclick=Kr),t=!0):t=!1,t||xa(e)}function lh(e){for(Vt=e.return;Vt;)switch(Vt.tag){case 5:case 13:Jn=!1;return;case 27:case 3:Jn=!0;return;default:Vt=Vt.return}}function Ki(e){if(e!==Vt)return!1;if(!He)return lh(e),He=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Fc(e.type,e.memoizedProps)),n=!n),n&&st&&xa(e),lh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){st=zn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}st=null}}else t===27?(t=st,na(e.type)?(e=Xc,Xc=null,st=e):st=t):st=Vt?zn(e.stateNode.nextSibling):null;return!0}function Ji(){st=Vt=null,He=!1}function ah(){var e=Sa;return e!==null&&(Pt===null?Pt=e:Pt.push.apply(Pt,e),Sa=null),e}function Pi(e){Sa===null?Sa=[e]:Sa.push(e)}var Ro=Y(null),Ea=null,wl=null;function Vl(e,t,n){ae(Ro,t._currentValue),t._currentValue=n}function Al(e){e._currentValue=Ro.current,ue(Ro)}function Oo(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function Co(e,t,n,i){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var y=u.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=u;for(var _=0;_<t.length;_++)if(b.context===t[_]){c.lanes|=n,b=c.alternate,b!==null&&(b.lanes|=n),Oo(c.return,n,e),i||(y=null);break e}c=b.next}}else if(u.tag===18){if(y=u.return,y===null)throw Error(r(341));y.lanes|=n,c=y.alternate,c!==null&&(c.lanes|=n),Oo(y,n,e),y=null}else y=u.child;if(y!==null)y.return=u;else for(y=u;y!==null;){if(y===e){y=null;break}if(u=y.sibling,u!==null){u.return=y.return,y=u;break}y=y.return}u=y}}function Wi(e,t,n,i){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var y=u.alternate;if(y===null)throw Error(r(387));if(y=y.memoizedProps,y!==null){var b=u.type;rn(u.pendingProps.value,y.value)||(e!==null?e.push(b):e=[b])}}else if(u===it.current){if(y=u.alternate,y===null)throw Error(r(387));y.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Rs):e=[Rs])}u=u.return}e!==null&&Co(t,e,n,i),t.flags|=262144}function yr(e){for(e=e.firstContext;e!==null;){if(!rn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function wa(e){Ea=e,wl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function zt(e){return ih(Ea,e)}function gr(e,t){return Ea===null&&wa(e),ih(e,t)}function ih(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},wl===null){if(e===null)throw Error(r(308));wl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else wl=wl.next=t;return n}var pv=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},yv=l.unstable_scheduleCallback,gv=l.unstable_NormalPriority,xt={$$typeof:M,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function jo(){return{controller:new pv,data:new Map,refCount:0}}function Ii(e){e.refCount--,e.refCount===0&&yv(gv,function(){e.controller.abort()})}var es=null,Do=0,ti=0,ni=null;function vv(e,t){if(es===null){var n=es=[];Do=0,ti=Uc(),ni={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Do++,t.then(sh,sh),t}function sh(){if(--Do===0&&es!==null){ni!==null&&(ni.status="fulfilled");var e=es;es=null,ti=0,ni=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function bv(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(i.status="rejected",i.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),i}var rh=q.S;q.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&vv(e,t),rh!==null&&rh(e,t)};var Aa=Y(null);function No(){var e=Aa.current;return e!==null?e:et.pooledCache}function vr(e,t){t===null?ae(Aa,Aa.current):ae(Aa,t.pool)}function uh(){var e=No();return e===null?null:{parent:xt._currentValue,pool:e}}var ts=Error(r(460)),oh=Error(r(474)),br=Error(r(542)),Uo={then:function(){}};function ch(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Sr(){}function fh(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Sr,Sr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hh(e),e;default:if(typeof t.status=="string")t.then(Sr,Sr);else{if(e=et,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=i}},function(i){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hh(e),e}throw ns=t,ts}}var ns=null;function dh(){if(ns===null)throw Error(r(459));var e=ns;return ns=null,e}function hh(e){if(e===ts||e===br)throw Error(r(483))}var Fl=!1;function Mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Yl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function $l(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Ve&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,t=dr(e),Id(e,null,n),t}return fr(e,i,t,n),dr(e)}function ls(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,qt(e,n)}}function zo(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?u=c=y:c=c.next=y,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ko=!1;function as(){if(ko){var e=ni;if(e!==null)throw e}}function is(e,t,n,i){ko=!1;var u=e.updateQueue;Fl=!1;var c=u.firstBaseUpdate,y=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var _=b,z=_.next;_.next=null,y===null?c=z:y.next=z,y=_;var X=e.alternate;X!==null&&(X=X.updateQueue,b=X.lastBaseUpdate,b!==y&&(b===null?X.firstBaseUpdate=z:b.next=z,X.lastBaseUpdate=_))}if(c!==null){var Q=u.baseState;y=0,X=z=_=null,b=c;do{var B=b.lane&-536870913,V=B!==b.lane;if(V?(ke&B)===B:(i&B)===B){B!==0&&B===ti&&(ko=!0),X!==null&&(X=X.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var Te=e,Ee=b;B=t;var Ze=n;switch(Ee.tag){case 1:if(Te=Ee.payload,typeof Te=="function"){Q=Te.call(Ze,Q,B);break e}Q=Te;break e;case 3:Te.flags=Te.flags&-65537|128;case 0:if(Te=Ee.payload,B=typeof Te=="function"?Te.call(Ze,Q,B):Te,B==null)break e;Q=g({},Q,B);break e;case 2:Fl=!0}}B=b.callback,B!==null&&(e.flags|=64,V&&(e.flags|=8192),V=u.callbacks,V===null?u.callbacks=[B]:V.push(B))}else V={lane:B,tag:b.tag,payload:b.payload,callback:b.callback,next:null},X===null?(z=X=V,_=Q):X=X.next=V,y|=B;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;V=b,b=V.next,V.next=null,u.lastBaseUpdate=V,u.shared.pending=null}}while(!0);X===null&&(_=Q),u.baseState=_,u.firstBaseUpdate=z,u.lastBaseUpdate=X,c===null&&(u.shared.lanes=0),Wl|=y,e.lanes=y,e.memoizedState=Q}}function mh(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function ph(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)mh(n[e],t)}var li=Y(null),xr=Y(0);function yh(e,t){e=Dl,ae(xr,e),ae(li,t),Dl=e|t.baseLanes}function Bo(){ae(xr,Dl),ae(li,li.current)}function Ho(){Dl=xr.current,ue(li),ue(xr)}var Gl=0,De=null,Ge=null,gt=null,Er=!1,ai=!1,_a=!1,wr=0,ss=0,ii=null,Sv=0;function ft(){throw Error(r(321))}function qo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!rn(e[n],t[n]))return!1;return!0}function Vo(e,t,n,i,u,c){return Gl=c,De=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,q.H=e===null||e.memoizedState===null?Ih:em,_a=!1,c=n(i,u),_a=!1,ai&&(c=vh(t,n,i,u)),gh(e),c}function gh(e){q.H=Cr;var t=Ge!==null&&Ge.next!==null;if(Gl=0,gt=Ge=De=null,Er=!1,ss=0,ii=null,t)throw Error(r(300));e===null||Rt||(e=e.dependencies,e!==null&&yr(e)&&(Rt=!0))}function vh(e,t,n,i){De=e;var u=0;do{if(ai&&(ii=null),ss=0,ai=!1,25<=u)throw Error(r(301));if(u+=1,gt=Ge=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}q.H=Rv,c=t(n,i)}while(ai);return c}function xv(){var e=q.H,t=e.useState()[0];return t=typeof t.then=="function"?rs(t):t,e=e.useState()[0],(Ge!==null?Ge.memoizedState:null)!==e&&(De.flags|=1024),t}function Fo(){var e=wr!==0;return wr=0,e}function Yo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function $o(e){if(Er){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Er=!1}Gl=0,gt=Ge=De=null,ai=!1,ss=wr=0,ii=null}function Kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return gt===null?De.memoizedState=gt=e:gt=gt.next=e,gt}function vt(){if(Ge===null){var e=De.alternate;e=e!==null?e.memoizedState:null}else e=Ge.next;var t=gt===null?De.memoizedState:gt.next;if(t!==null)gt=t,Ge=e;else{if(e===null)throw De.alternate===null?Error(r(467)):Error(r(310));Ge=e,e={memoizedState:Ge.memoizedState,baseState:Ge.baseState,baseQueue:Ge.baseQueue,queue:Ge.queue,next:null},gt===null?De.memoizedState=gt=e:gt=gt.next=e}return gt}function Go(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function rs(e){var t=ss;return ss+=1,ii===null&&(ii=[]),e=fh(ii,e,t),t=De,(gt===null?t.memoizedState:gt.next)===null&&(t=t.alternate,q.H=t===null||t.memoizedState===null?Ih:em),e}function Ar(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return rs(e);if(e.$$typeof===M)return zt(e)}throw Error(r(438,String(e)))}function Xo(e){var t=null,n=De.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=De.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Go(),De.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=xe;return t.index++,n}function _l(e,t){return typeof t=="function"?t(e):t}function _r(e){var t=vt();return Zo(t,Ge,e)}function Zo(e,t,n){var i=e.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=n;var u=e.baseQueue,c=i.pending;if(c!==null){if(u!==null){var y=u.next;u.next=c.next,c.next=y}t.baseQueue=u=c,i.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var b=y=null,_=null,z=t,X=!1;do{var Q=z.lane&-536870913;if(Q!==z.lane?(ke&Q)===Q:(Gl&Q)===Q){var B=z.revertLane;if(B===0)_!==null&&(_=_.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),Q===ti&&(X=!0);else if((Gl&B)===B){z=z.next,B===ti&&(X=!0);continue}else Q={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},_===null?(b=_=Q,y=c):_=_.next=Q,De.lanes|=B,Wl|=B;Q=z.action,_a&&n(c,Q),c=z.hasEagerState?z.eagerState:n(c,Q)}else B={lane:Q,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},_===null?(b=_=B,y=c):_=_.next=B,De.lanes|=Q,Wl|=Q;z=z.next}while(z!==null&&z!==t);if(_===null?y=c:_.next=b,!rn(c,e.memoizedState)&&(Rt=!0,X&&(n=ni,n!==null)))throw n;e.memoizedState=c,e.baseState=y,e.baseQueue=_,i.lastRenderedState=c}return u===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Qo(e){var t=vt(),n=t.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=e;var i=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var y=u=u.next;do c=e(c,y.action),y=y.next;while(y!==u);rn(c,t.memoizedState)||(Rt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,i]}function bh(e,t,n){var i=De,u=vt(),c=He;if(c){if(n===void 0)throw Error(r(407));n=n()}else n=t();var y=!rn((Ge||u).memoizedState,n);y&&(u.memoizedState=n,Rt=!0),u=u.queue;var b=Eh.bind(null,i,u,e);if(us(2048,8,b,[e]),u.getSnapshot!==t||y||gt!==null&&gt.memoizedState.tag&1){if(i.flags|=2048,si(9,Tr(),xh.bind(null,i,u,n,t),null),et===null)throw Error(r(349));c||(Gl&124)!==0||Sh(i,t,n)}return n}function Sh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=De.updateQueue,t===null?(t=Go(),De.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function xh(e,t,n,i){t.value=n,t.getSnapshot=i,wh(t)&&Ah(e)}function Eh(e,t,n){return n(function(){wh(t)&&Ah(e)})}function wh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!rn(e,n)}catch{return!0}}function Ah(e){var t=Pa(e,2);t!==null&&hn(t,e,2)}function Ko(e){var t=Kt();if(typeof e=="function"){var n=e;if(e=n(),_a){ie(!0);try{n()}finally{ie(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:_l,lastRenderedState:e},t}function _h(e,t,n,i){return e.baseState=n,Zo(e,Ge,typeof i=="function"?i:_l)}function Ev(e,t,n,i,u){if(Or(e))throw Error(r(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};q.T!==null?n(!0):c.isTransition=!1,i(c),n=t.pending,n===null?(c.next=t.pending=c,Th(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Th(e,t){var n=t.action,i=t.payload,u=e.state;if(t.isTransition){var c=q.T,y={};q.T=y;try{var b=n(u,i),_=q.S;_!==null&&_(y,b),Rh(e,t,b)}catch(z){Jo(e,t,z)}finally{q.T=c}}else try{c=n(u,i),Rh(e,t,c)}catch(z){Jo(e,t,z)}}function Rh(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Oh(e,t,i)},function(i){return Jo(e,t,i)}):Oh(e,t,n)}function Oh(e,t,n){t.status="fulfilled",t.value=n,Ch(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Th(e,n)))}function Jo(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,Ch(t),t=t.next;while(t!==i)}e.action=null}function Ch(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function jh(e,t){return t}function Dh(e,t){if(He){var n=et.formState;if(n!==null){e:{var i=De;if(He){if(st){t:{for(var u=st,c=Jn;u.nodeType!==8;){if(!c){u=null;break t}if(u=zn(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){st=zn(u.nextSibling),i=u.data==="F!";break e}}xa(i)}i=!1}i&&(t=n[0])}}return n=Kt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:jh,lastRenderedState:t},n.queue=i,n=Jh.bind(null,De,i),i.dispatch=n,i=Ko(!1),c=tc.bind(null,De,!1,i.queue),i=Kt(),u={state:t,dispatch:null,action:e,pending:null},i.queue=u,n=Ev.bind(null,De,u,c,n),u.dispatch=n,i.memoizedState=e,[t,n,!1]}function Nh(e){var t=vt();return Uh(t,Ge,e)}function Uh(e,t,n){if(t=Zo(e,t,jh)[0],e=_r(_l)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=rs(t)}catch(y){throw y===ts?br:y}else i=t;t=vt();var u=t.queue,c=u.dispatch;return n!==t.memoizedState&&(De.flags|=2048,si(9,Tr(),wv.bind(null,u,n),null)),[i,c,e]}function wv(e,t){e.action=t}function Mh(e){var t=vt(),n=Ge;if(n!==null)return Uh(t,n,e);vt(),t=t.memoizedState,n=vt();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function si(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=De.updateQueue,t===null&&(t=Go(),De.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function Tr(){return{destroy:void 0,resource:void 0}}function Lh(){return vt().memoizedState}function Rr(e,t,n,i){var u=Kt();i=i===void 0?null:i,De.flags|=e,u.memoizedState=si(1|t,Tr(),n,i)}function us(e,t,n,i){var u=vt();i=i===void 0?null:i;var c=u.memoizedState.inst;Ge!==null&&i!==null&&qo(i,Ge.memoizedState.deps)?u.memoizedState=si(t,c,n,i):(De.flags|=e,u.memoizedState=si(1|t,c,n,i))}function zh(e,t){Rr(8390656,8,e,t)}function kh(e,t){us(2048,8,e,t)}function Bh(e,t){return us(4,2,e,t)}function Hh(e,t){return us(4,4,e,t)}function qh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vh(e,t,n){n=n!=null?n.concat([e]):null,us(4,4,qh.bind(null,t,e),n)}function Po(){}function Fh(e,t){var n=vt();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&qo(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function Yh(e,t){var n=vt();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&qo(t,i[1]))return i[0];if(i=e(),_a){ie(!0);try{e()}finally{ie(!1)}}return n.memoizedState=[i,t],i}function Wo(e,t,n){return n===void 0||(Gl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Xm(),De.lanes|=e,Wl|=e,n)}function $h(e,t,n,i){return rn(n,t)?n:li.current!==null?(e=Wo(e,n,i),rn(e,t)||(Rt=!0),e):(Gl&42)===0?(Rt=!0,e.memoizedState=n):(e=Xm(),De.lanes|=e,Wl|=e,t)}function Gh(e,t,n,i,u){var c=P.p;P.p=c!==0&&8>c?c:8;var y=q.T,b={};q.T=b,tc(e,!1,t,n);try{var _=u(),z=q.S;if(z!==null&&z(b,_),_!==null&&typeof _=="object"&&typeof _.then=="function"){var X=bv(_,i);os(e,t,X,dn(e))}else os(e,t,i,dn(e))}catch(Q){os(e,t,{then:function(){},status:"rejected",reason:Q},dn())}finally{P.p=c,q.T=y}}function Av(){}function Io(e,t,n,i){if(e.tag!==5)throw Error(r(476));var u=Xh(e).queue;Gh(e,u,t,le,n===null?Av:function(){return Zh(e),n(i)})}function Xh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:le,baseState:le,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:_l,lastRenderedState:le},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:_l,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Zh(e){var t=Xh(e).next.queue;os(e,t,{},dn())}function ec(){return zt(Rs)}function Qh(){return vt().memoizedState}function Kh(){return vt().memoizedState}function _v(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=dn();e=Yl(n);var i=$l(t,e,n);i!==null&&(hn(i,t,n),ls(i,t,n)),t={cache:jo()},e.payload=t;return}t=t.return}}function Tv(e,t,n){var i=dn();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Or(e)?Ph(t,n):(n=So(e,t,n,i),n!==null&&(hn(n,e,i),Wh(n,t,i)))}function Jh(e,t,n){var i=dn();os(e,t,n,i)}function os(e,t,n,i){var u={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Or(e))Ph(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var y=t.lastRenderedState,b=c(y,n);if(u.hasEagerState=!0,u.eagerState=b,rn(b,y))return fr(e,t,u,0),et===null&&cr(),!1}catch{}finally{}if(n=So(e,t,u,i),n!==null)return hn(n,e,i),Wh(n,t,i),!0}return!1}function tc(e,t,n,i){if(i={lane:2,revertLane:Uc(),action:i,hasEagerState:!1,eagerState:null,next:null},Or(e)){if(t)throw Error(r(479))}else t=So(e,n,i,2),t!==null&&hn(t,e,2)}function Or(e){var t=e.alternate;return e===De||t!==null&&t===De}function Ph(e,t){ai=Er=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wh(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,qt(e,n)}}var Cr={readContext:zt,use:Ar,useCallback:ft,useContext:ft,useEffect:ft,useImperativeHandle:ft,useLayoutEffect:ft,useInsertionEffect:ft,useMemo:ft,useReducer:ft,useRef:ft,useState:ft,useDebugValue:ft,useDeferredValue:ft,useTransition:ft,useSyncExternalStore:ft,useId:ft,useHostTransitionStatus:ft,useFormState:ft,useActionState:ft,useOptimistic:ft,useMemoCache:ft,useCacheRefresh:ft},Ih={readContext:zt,use:Ar,useCallback:function(e,t){return Kt().memoizedState=[e,t===void 0?null:t],e},useContext:zt,useEffect:zh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Rr(4194308,4,qh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Rr(4194308,4,e,t)},useInsertionEffect:function(e,t){Rr(4,2,e,t)},useMemo:function(e,t){var n=Kt();t=t===void 0?null:t;var i=e();if(_a){ie(!0);try{e()}finally{ie(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=Kt();if(n!==void 0){var u=n(t);if(_a){ie(!0);try{n(t)}finally{ie(!1)}}}else u=t;return i.memoizedState=i.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},i.queue=e,e=e.dispatch=Tv.bind(null,De,e),[i.memoizedState,e]},useRef:function(e){var t=Kt();return e={current:e},t.memoizedState=e},useState:function(e){e=Ko(e);var t=e.queue,n=Jh.bind(null,De,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Po,useDeferredValue:function(e,t){var n=Kt();return Wo(n,e,t)},useTransition:function(){var e=Ko(!1);return e=Gh.bind(null,De,e.queue,!0,!1),Kt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=De,u=Kt();if(He){if(n===void 0)throw Error(r(407));n=n()}else{if(n=t(),et===null)throw Error(r(349));(ke&124)!==0||Sh(i,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,zh(Eh.bind(null,i,c,e),[e]),i.flags|=2048,si(9,Tr(),xh.bind(null,i,c,n,t),null),n},useId:function(){var e=Kt(),t=et.identifierPrefix;if(He){var n=El,i=xl;n=(i&~(1<<32-be(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=wr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Sv++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ec,useFormState:Dh,useActionState:Dh,useOptimistic:function(e){var t=Kt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=tc.bind(null,De,!0,n),n.dispatch=t,[e,t]},useMemoCache:Xo,useCacheRefresh:function(){return Kt().memoizedState=_v.bind(null,De)}},em={readContext:zt,use:Ar,useCallback:Fh,useContext:zt,useEffect:kh,useImperativeHandle:Vh,useInsertionEffect:Bh,useLayoutEffect:Hh,useMemo:Yh,useReducer:_r,useRef:Lh,useState:function(){return _r(_l)},useDebugValue:Po,useDeferredValue:function(e,t){var n=vt();return $h(n,Ge.memoizedState,e,t)},useTransition:function(){var e=_r(_l)[0],t=vt().memoizedState;return[typeof e=="boolean"?e:rs(e),t]},useSyncExternalStore:bh,useId:Qh,useHostTransitionStatus:ec,useFormState:Nh,useActionState:Nh,useOptimistic:function(e,t){var n=vt();return _h(n,Ge,e,t)},useMemoCache:Xo,useCacheRefresh:Kh},Rv={readContext:zt,use:Ar,useCallback:Fh,useContext:zt,useEffect:kh,useImperativeHandle:Vh,useInsertionEffect:Bh,useLayoutEffect:Hh,useMemo:Yh,useReducer:Qo,useRef:Lh,useState:function(){return Qo(_l)},useDebugValue:Po,useDeferredValue:function(e,t){var n=vt();return Ge===null?Wo(n,e,t):$h(n,Ge.memoizedState,e,t)},useTransition:function(){var e=Qo(_l)[0],t=vt().memoizedState;return[typeof e=="boolean"?e:rs(e),t]},useSyncExternalStore:bh,useId:Qh,useHostTransitionStatus:ec,useFormState:Mh,useActionState:Mh,useOptimistic:function(e,t){var n=vt();return Ge!==null?_h(n,Ge,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Xo,useCacheRefresh:Kh},ri=null,cs=0;function jr(e){var t=cs;return cs+=1,ri===null&&(ri=[]),fh(ri,e,t)}function fs(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Dr(e,t){throw t.$$typeof===S?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function tm(e){var t=e._init;return t(e._payload)}function nm(e){function t(U,N){if(e){var L=U.deletions;L===null?(U.deletions=[N],U.flags|=16):L.push(N)}}function n(U,N){if(!e)return null;for(;N!==null;)t(U,N),N=N.sibling;return null}function i(U){for(var N=new Map;U!==null;)U.key!==null?N.set(U.key,U):N.set(U.index,U),U=U.sibling;return N}function u(U,N){return U=Sl(U,N),U.index=0,U.sibling=null,U}function c(U,N,L){return U.index=L,e?(L=U.alternate,L!==null?(L=L.index,L<N?(U.flags|=67108866,N):L):(U.flags|=67108866,N)):(U.flags|=1048576,N)}function y(U){return e&&U.alternate===null&&(U.flags|=67108866),U}function b(U,N,L,Z){return N===null||N.tag!==6?(N=Eo(L,U.mode,Z),N.return=U,N):(N=u(N,L),N.return=U,N)}function _(U,N,L,Z){var he=L.type;return he===E?X(U,N,L.props.children,Z,L.key):N!==null&&(N.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===K&&tm(he)===N.type)?(N=u(N,L.props),fs(N,L),N.return=U,N):(N=hr(L.type,L.key,L.props,null,U.mode,Z),fs(N,L),N.return=U,N)}function z(U,N,L,Z){return N===null||N.tag!==4||N.stateNode.containerInfo!==L.containerInfo||N.stateNode.implementation!==L.implementation?(N=wo(L,U.mode,Z),N.return=U,N):(N=u(N,L.children||[]),N.return=U,N)}function X(U,N,L,Z,he){return N===null||N.tag!==7?(N=ga(L,U.mode,Z,he),N.return=U,N):(N=u(N,L),N.return=U,N)}function Q(U,N,L){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=Eo(""+N,U.mode,L),N.return=U,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case x:return L=hr(N.type,N.key,N.props,null,U.mode,L),fs(L,N),L.return=U,L;case T:return N=wo(N,U.mode,L),N.return=U,N;case K:var Z=N._init;return N=Z(N._payload),Q(U,N,L)}if(ve(N)||W(N))return N=ga(N,U.mode,L,null),N.return=U,N;if(typeof N.then=="function")return Q(U,jr(N),L);if(N.$$typeof===M)return Q(U,gr(U,N),L);Dr(U,N)}return null}function B(U,N,L,Z){var he=N!==null?N.key:null;if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return he!==null?null:b(U,N,""+L,Z);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case x:return L.key===he?_(U,N,L,Z):null;case T:return L.key===he?z(U,N,L,Z):null;case K:return he=L._init,L=he(L._payload),B(U,N,L,Z)}if(ve(L)||W(L))return he!==null?null:X(U,N,L,Z,null);if(typeof L.then=="function")return B(U,N,jr(L),Z);if(L.$$typeof===M)return B(U,N,gr(U,L),Z);Dr(U,L)}return null}function V(U,N,L,Z,he){if(typeof Z=="string"&&Z!==""||typeof Z=="number"||typeof Z=="bigint")return U=U.get(L)||null,b(N,U,""+Z,he);if(typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case x:return U=U.get(Z.key===null?L:Z.key)||null,_(N,U,Z,he);case T:return U=U.get(Z.key===null?L:Z.key)||null,z(N,U,Z,he);case K:var Ue=Z._init;return Z=Ue(Z._payload),V(U,N,L,Z,he)}if(ve(Z)||W(Z))return U=U.get(L)||null,X(N,U,Z,he,null);if(typeof Z.then=="function")return V(U,N,L,jr(Z),he);if(Z.$$typeof===M)return V(U,N,L,gr(N,Z),he);Dr(N,Z)}return null}function Te(U,N,L,Z){for(var he=null,Ue=null,ge=N,Ae=N=0,Ct=null;ge!==null&&Ae<L.length;Ae++){ge.index>Ae?(Ct=ge,ge=null):Ct=ge.sibling;var Be=B(U,ge,L[Ae],Z);if(Be===null){ge===null&&(ge=Ct);break}e&&ge&&Be.alternate===null&&t(U,ge),N=c(Be,N,Ae),Ue===null?he=Be:Ue.sibling=Be,Ue=Be,ge=Ct}if(Ae===L.length)return n(U,ge),He&&ba(U,Ae),he;if(ge===null){for(;Ae<L.length;Ae++)ge=Q(U,L[Ae],Z),ge!==null&&(N=c(ge,N,Ae),Ue===null?he=ge:Ue.sibling=ge,Ue=ge);return He&&ba(U,Ae),he}for(ge=i(ge);Ae<L.length;Ae++)Ct=V(ge,U,Ae,L[Ae],Z),Ct!==null&&(e&&Ct.alternate!==null&&ge.delete(Ct.key===null?Ae:Ct.key),N=c(Ct,N,Ae),Ue===null?he=Ct:Ue.sibling=Ct,Ue=Ct);return e&&ge.forEach(function(ra){return t(U,ra)}),He&&ba(U,Ae),he}function Ee(U,N,L,Z){if(L==null)throw Error(r(151));for(var he=null,Ue=null,ge=N,Ae=N=0,Ct=null,Be=L.next();ge!==null&&!Be.done;Ae++,Be=L.next()){ge.index>Ae?(Ct=ge,ge=null):Ct=ge.sibling;var ra=B(U,ge,Be.value,Z);if(ra===null){ge===null&&(ge=Ct);break}e&&ge&&ra.alternate===null&&t(U,ge),N=c(ra,N,Ae),Ue===null?he=ra:Ue.sibling=ra,Ue=ra,ge=Ct}if(Be.done)return n(U,ge),He&&ba(U,Ae),he;if(ge===null){for(;!Be.done;Ae++,Be=L.next())Be=Q(U,Be.value,Z),Be!==null&&(N=c(Be,N,Ae),Ue===null?he=Be:Ue.sibling=Be,Ue=Be);return He&&ba(U,Ae),he}for(ge=i(ge);!Be.done;Ae++,Be=L.next())Be=V(ge,U,Ae,Be.value,Z),Be!==null&&(e&&Be.alternate!==null&&ge.delete(Be.key===null?Ae:Be.key),N=c(Be,N,Ae),Ue===null?he=Be:Ue.sibling=Be,Ue=Be);return e&&ge.forEach(function(O1){return t(U,O1)}),He&&ba(U,Ae),he}function Ze(U,N,L,Z){if(typeof L=="object"&&L!==null&&L.type===E&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case x:e:{for(var he=L.key;N!==null;){if(N.key===he){if(he=L.type,he===E){if(N.tag===7){n(U,N.sibling),Z=u(N,L.props.children),Z.return=U,U=Z;break e}}else if(N.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===K&&tm(he)===N.type){n(U,N.sibling),Z=u(N,L.props),fs(Z,L),Z.return=U,U=Z;break e}n(U,N);break}else t(U,N);N=N.sibling}L.type===E?(Z=ga(L.props.children,U.mode,Z,L.key),Z.return=U,U=Z):(Z=hr(L.type,L.key,L.props,null,U.mode,Z),fs(Z,L),Z.return=U,U=Z)}return y(U);case T:e:{for(he=L.key;N!==null;){if(N.key===he)if(N.tag===4&&N.stateNode.containerInfo===L.containerInfo&&N.stateNode.implementation===L.implementation){n(U,N.sibling),Z=u(N,L.children||[]),Z.return=U,U=Z;break e}else{n(U,N);break}else t(U,N);N=N.sibling}Z=wo(L,U.mode,Z),Z.return=U,U=Z}return y(U);case K:return he=L._init,L=he(L._payload),Ze(U,N,L,Z)}if(ve(L))return Te(U,N,L,Z);if(W(L)){if(he=W(L),typeof he!="function")throw Error(r(150));return L=he.call(L),Ee(U,N,L,Z)}if(typeof L.then=="function")return Ze(U,N,jr(L),Z);if(L.$$typeof===M)return Ze(U,N,gr(U,L),Z);Dr(U,L)}return typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint"?(L=""+L,N!==null&&N.tag===6?(n(U,N.sibling),Z=u(N,L),Z.return=U,U=Z):(n(U,N),Z=Eo(L,U.mode,Z),Z.return=U,U=Z),y(U)):n(U,N)}return function(U,N,L,Z){try{cs=0;var he=Ze(U,N,L,Z);return ri=null,he}catch(ge){if(ge===ts||ge===br)throw ge;var Ue=un(29,ge,null,U.mode);return Ue.lanes=Z,Ue.return=U,Ue}finally{}}}var ui=nm(!0),lm=nm(!1),An=Y(null),Pn=null;function Xl(e){var t=e.alternate;ae(Et,Et.current&1),ae(An,e),Pn===null&&(t===null||li.current!==null||t.memoizedState!==null)&&(Pn=e)}function am(e){if(e.tag===22){if(ae(Et,Et.current),ae(An,e),Pn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Pn=e)}}else Zl()}function Zl(){ae(Et,Et.current),ae(An,An.current)}function Tl(e){ue(An),Pn===e&&(Pn=null),ue(Et)}var Et=Y(0);function Nr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Gc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function nc(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:g({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var lc={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=dn(),u=Yl(i);u.payload=t,n!=null&&(u.callback=n),t=$l(e,u,i),t!==null&&(hn(t,e,i),ls(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=dn(),u=Yl(i);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=$l(e,u,i),t!==null&&(hn(t,e,i),ls(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=dn(),i=Yl(n);i.tag=2,t!=null&&(i.callback=t),t=$l(e,i,n),t!==null&&(hn(t,e,n),ls(t,e,n))}};function im(e,t,n,i,u,c,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,y):t.prototype&&t.prototype.isPureReactComponent?!Zi(n,i)||!Zi(u,c):!0}function sm(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&lc.enqueueReplaceState(t,t.state,null)}function Ta(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=g({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var Ur=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function rm(e){Ur(e)}function um(e){console.error(e)}function om(e){Ur(e)}function Mr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function cm(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function ac(e,t,n){return n=Yl(n),n.tag=3,n.payload={element:null},n.callback=function(){Mr(e,t)},n}function fm(e){return e=Yl(e),e.tag=3,e}function dm(e,t,n,i){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var c=i.value;e.payload=function(){return u(c)},e.callback=function(){cm(t,n,i)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){cm(t,n,i),typeof u!="function"&&(Il===null?Il=new Set([this]):Il.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function Ov(e,t,n,i,u){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&Wi(t,n,u,!0),n=An.current,n!==null){switch(n.tag){case 13:return Pn===null?Oc():n.alternate===null&&rt===0&&(rt=3),n.flags&=-257,n.flags|=65536,n.lanes=u,i===Uo?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),jc(e,i,u)),!1;case 22:return n.flags|=65536,i===Uo?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),jc(e,i,u)),!1}throw Error(r(435,n.tag))}return jc(e,i,u),Oc(),!1}if(He)return t=An.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,i!==To&&(e=Error(r(422),{cause:i}),Pi(Sn(e,n)))):(i!==To&&(t=Error(r(423),{cause:i}),Pi(Sn(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,i=Sn(i,n),u=ac(e.stateNode,i,u),zo(e,u),rt!==4&&(rt=2)),!1;var c=Error(r(520),{cause:i});if(c=Sn(c,n),vs===null?vs=[c]:vs.push(c),rt!==4&&(rt=2),t===null)return!0;i=Sn(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=ac(n.stateNode,i,e),zo(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Il===null||!Il.has(c))))return n.flags|=65536,u&=-u,n.lanes|=u,u=fm(u),dm(u,e,n,i),zo(n,u),!1}n=n.return}while(n!==null);return!1}var hm=Error(r(461)),Rt=!1;function Nt(e,t,n,i){t.child=e===null?lm(t,null,n,i):ui(t,e.child,n,i)}function mm(e,t,n,i,u){n=n.render;var c=t.ref;if("ref"in i){var y={};for(var b in i)b!=="ref"&&(y[b]=i[b])}else y=i;return wa(t),i=Vo(e,t,n,y,c,u),b=Fo(),e!==null&&!Rt?(Yo(e,t,u),Rl(e,t,u)):(He&&b&&Ao(t),t.flags|=1,Nt(e,t,i,u),t.child)}function pm(e,t,n,i,u){if(e===null){var c=n.type;return typeof c=="function"&&!xo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,ym(e,t,c,i,u)):(e=hr(n.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!dc(e,u)){var y=c.memoizedProps;if(n=n.compare,n=n!==null?n:Zi,n(y,i)&&e.ref===t.ref)return Rl(e,t,u)}return t.flags|=1,e=Sl(c,i),e.ref=t.ref,e.return=t,t.child=e}function ym(e,t,n,i,u){if(e!==null){var c=e.memoizedProps;if(Zi(c,i)&&e.ref===t.ref)if(Rt=!1,t.pendingProps=i=c,dc(e,u))(e.flags&131072)!==0&&(Rt=!0);else return t.lanes=e.lanes,Rl(e,t,u)}return ic(e,t,n,i,u)}function gm(e,t,n){var i=t.pendingProps,u=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|n:n,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return vm(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&vr(t,c!==null?c.cachePool:null),c!==null?yh(t,c):Bo(),am(t);else return t.lanes=t.childLanes=536870912,vm(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(vr(t,c.cachePool),yh(t,c),Zl(),t.memoizedState=null):(e!==null&&vr(t,null),Bo(),Zl());return Nt(e,t,u,n),t.child}function vm(e,t,n,i){var u=No();return u=u===null?null:{parent:xt._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&vr(t,null),Bo(),am(t),e!==null&&Wi(e,t,i,!0),null}function Lr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function ic(e,t,n,i,u){return wa(t),n=Vo(e,t,n,i,void 0,u),i=Fo(),e!==null&&!Rt?(Yo(e,t,u),Rl(e,t,u)):(He&&i&&Ao(t),t.flags|=1,Nt(e,t,n,u),t.child)}function bm(e,t,n,i,u,c){return wa(t),t.updateQueue=null,n=vh(t,i,n,u),gh(e),i=Fo(),e!==null&&!Rt?(Yo(e,t,c),Rl(e,t,c)):(He&&i&&Ao(t),t.flags|=1,Nt(e,t,n,c),t.child)}function Sm(e,t,n,i,u){if(wa(t),t.stateNode===null){var c=Wa,y=n.contextType;typeof y=="object"&&y!==null&&(c=zt(y)),c=new n(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=lc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},Mo(t),y=n.contextType,c.context=typeof y=="object"&&y!==null?zt(y):Wa,c.state=t.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(nc(t,n,y,i),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&lc.enqueueReplaceState(c,c.state,null),is(t,i,c,u),as(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,_=Ta(n,b);c.props=_;var z=c.context,X=n.contextType;y=Wa,typeof X=="object"&&X!==null&&(y=zt(X));var Q=n.getDerivedStateFromProps;X=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,X||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||z!==y)&&sm(t,c,i,y),Fl=!1;var B=t.memoizedState;c.state=B,is(t,i,c,u),as(),z=t.memoizedState,b||B!==z||Fl?(typeof Q=="function"&&(nc(t,n,Q,i),z=t.memoizedState),(_=Fl||im(t,n,_,i,B,z,y))?(X||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=z),c.props=i,c.state=z,c.context=y,i=_):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,Lo(e,t),y=t.memoizedProps,X=Ta(n,y),c.props=X,Q=t.pendingProps,B=c.context,z=n.contextType,_=Wa,typeof z=="object"&&z!==null&&(_=zt(z)),b=n.getDerivedStateFromProps,(z=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==Q||B!==_)&&sm(t,c,i,_),Fl=!1,B=t.memoizedState,c.state=B,is(t,i,c,u),as();var V=t.memoizedState;y!==Q||B!==V||Fl||e!==null&&e.dependencies!==null&&yr(e.dependencies)?(typeof b=="function"&&(nc(t,n,b,i),V=t.memoizedState),(X=Fl||im(t,n,X,i,B,V,_)||e!==null&&e.dependencies!==null&&yr(e.dependencies))?(z||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,V,_),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,V,_)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=V),c.props=i,c.state=V,c.context=_,i=X):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,Lr(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=ui(t,e.child,null,u),t.child=ui(t,null,n,u)):Nt(e,t,n,u),t.memoizedState=c.state,e=t.child):e=Rl(e,t,u),e}function xm(e,t,n,i){return Ji(),t.flags|=256,Nt(e,t,n,i),t.child}var sc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function rc(e){return{baseLanes:e,cachePool:uh()}}function uc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=_n),e}function Em(e,t,n){var i=t.pendingProps,u=!1,c=(t.flags&128)!==0,y;if((y=c)||(y=e!==null&&e.memoizedState===null?!1:(Et.current&2)!==0),y&&(u=!0,t.flags&=-129),y=(t.flags&32)!==0,t.flags&=-33,e===null){if(He){if(u?Xl(t):Zl(),He){var b=st,_;if(_=b){e:{for(_=b,b=Jn;_.nodeType!==8;){if(!b){b=null;break e}if(_=zn(_.nextSibling),_===null){b=null;break e}}b=_}b!==null?(t.memoizedState={dehydrated:b,treeContext:va!==null?{id:xl,overflow:El}:null,retryLane:536870912,hydrationErrors:null},_=un(18,null,null,0),_.stateNode=b,_.return=t,t.child=_,Vt=t,st=null,_=!0):_=!1}_||xa(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Gc(b)?t.lanes=32:t.lanes=536870912,null;Tl(t)}return b=i.children,i=i.fallback,u?(Zl(),u=t.mode,b=zr({mode:"hidden",children:b},u),i=ga(i,u,n,null),b.return=t,i.return=t,b.sibling=i,t.child=b,u=t.child,u.memoizedState=rc(n),u.childLanes=uc(e,y,n),t.memoizedState=sc,i):(Xl(t),oc(t,b))}if(_=e.memoizedState,_!==null&&(b=_.dehydrated,b!==null)){if(c)t.flags&256?(Xl(t),t.flags&=-257,t=cc(e,t,n)):t.memoizedState!==null?(Zl(),t.child=e.child,t.flags|=128,t=null):(Zl(),u=i.fallback,b=t.mode,i=zr({mode:"visible",children:i.children},b),u=ga(u,b,n,null),u.flags|=2,i.return=t,u.return=t,i.sibling=u,t.child=i,ui(t,e.child,null,n),i=t.child,i.memoizedState=rc(n),i.childLanes=uc(e,y,n),t.memoizedState=sc,t=u);else if(Xl(t),Gc(b)){if(y=b.nextSibling&&b.nextSibling.dataset,y)var z=y.dgst;y=z,i=Error(r(419)),i.stack="",i.digest=y,Pi({value:i,source:null,stack:null}),t=cc(e,t,n)}else if(Rt||Wi(e,t,n,!1),y=(n&e.childLanes)!==0,Rt||y){if(y=et,y!==null&&(i=n&-n,i=(i&42)!==0?1:Se(i),i=(i&(y.suspendedLanes|n))!==0?0:i,i!==0&&i!==_.retryLane))throw _.retryLane=i,Pa(e,i),hn(y,e,i),hm;b.data==="$?"||Oc(),t=cc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=_.treeContext,st=zn(b.nextSibling),Vt=t,He=!0,Sa=null,Jn=!1,e!==null&&(En[wn++]=xl,En[wn++]=El,En[wn++]=va,xl=e.id,El=e.overflow,va=t),t=oc(t,i.children),t.flags|=4096);return t}return u?(Zl(),u=i.fallback,b=t.mode,_=e.child,z=_.sibling,i=Sl(_,{mode:"hidden",children:i.children}),i.subtreeFlags=_.subtreeFlags&65011712,z!==null?u=Sl(z,u):(u=ga(u,b,n,null),u.flags|=2),u.return=t,i.return=t,i.sibling=u,t.child=i,i=u,u=t.child,b=e.child.memoizedState,b===null?b=rc(n):(_=b.cachePool,_!==null?(z=xt._currentValue,_=_.parent!==z?{parent:z,pool:z}:_):_=uh(),b={baseLanes:b.baseLanes|n,cachePool:_}),u.memoizedState=b,u.childLanes=uc(e,y,n),t.memoizedState=sc,i):(Xl(t),n=e.child,e=n.sibling,n=Sl(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(y=t.deletions,y===null?(t.deletions=[e],t.flags|=16):y.push(e)),t.child=n,t.memoizedState=null,n)}function oc(e,t){return t=zr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function zr(e,t){return e=un(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function cc(e,t,n){return ui(t,e.child,null,n),e=oc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function wm(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Oo(e.return,t,n)}function fc(e,t,n,i,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=n,c.tailMode=u)}function Am(e,t,n){var i=t.pendingProps,u=i.revealOrder,c=i.tail;if(Nt(e,t,i.children,n),i=Et.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wm(e,n,t);else if(e.tag===19)wm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(ae(Et,i),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&Nr(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),fc(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Nr(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}fc(t,!0,n,null,c);break;case"together":fc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Rl(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Wl|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Wi(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,n=Sl(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Sl(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function dc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&yr(e)))}function Cv(e,t,n){switch(t.tag){case 3:Re(t,t.stateNode.containerInfo),Vl(t,xt,e.memoizedState.cache),Ji();break;case 27:case 5:At(t);break;case 4:Re(t,t.stateNode.containerInfo);break;case 10:Vl(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Xl(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Em(e,t,n):(Xl(t),e=Rl(e,t,n),e!==null?e.sibling:null);Xl(t);break;case 19:var u=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(Wi(e,t,n,!1),i=(n&t.childLanes)!==0),u){if(i)return Am(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),ae(Et,Et.current),i)break;return null;case 22:case 23:return t.lanes=0,gm(e,t,n);case 24:Vl(t,xt,e.memoizedState.cache)}return Rl(e,t,n)}function _m(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Rt=!0;else{if(!dc(e,n)&&(t.flags&128)===0)return Rt=!1,Cv(e,t,n);Rt=(e.flags&131072)!==0}else Rt=!1,He&&(t.flags&1048576)!==0&&th(t,pr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,u=i._init;if(i=u(i._payload),t.type=i,typeof i=="function")xo(i)?(e=Ta(i,e),t.tag=1,t=Sm(null,t,i,e,n)):(t.tag=0,t=ic(null,t,i,e,n));else{if(i!=null){if(u=i.$$typeof,u===G){t.tag=11,t=mm(null,t,i,e,n);break e}else if(u===I){t.tag=14,t=pm(null,t,i,e,n);break e}}throw t=fe(i)||i,Error(r(306,t,""))}}return t;case 0:return ic(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,u=Ta(i,t.pendingProps),Sm(e,t,i,u,n);case 3:e:{if(Re(t,t.stateNode.containerInfo),e===null)throw Error(r(387));i=t.pendingProps;var c=t.memoizedState;u=c.element,Lo(e,t),is(t,i,null,n);var y=t.memoizedState;if(i=y.cache,Vl(t,xt,i),i!==c.cache&&Co(t,[xt],n,!0),as(),i=y.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:y.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=xm(e,t,i,n);break e}else if(i!==u){u=Sn(Error(r(424)),t),Pi(u),t=xm(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(st=zn(e.firstChild),Vt=t,He=!0,Sa=null,Jn=!0,n=lm(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ji(),i===u){t=Rl(e,t,n);break e}Nt(e,t,i,n)}t=t.child}return t;case 26:return Lr(e,t),e===null?(n=C0(t.type,null,t.pendingProps,null))?t.memoizedState=n:He||(n=t.type,e=t.pendingProps,i=Jr(we.current).createElement(n),i[de]=t,i[ye]=e,Mt(i,n,e),ct(i),t.stateNode=i):t.memoizedState=C0(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return At(t),e===null&&He&&(i=t.stateNode=T0(t.type,t.pendingProps,we.current),Vt=t,Jn=!0,u=st,na(t.type)?(Xc=u,st=zn(i.firstChild)):st=u),Nt(e,t,t.pendingProps.children,n),Lr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&He&&((u=i=st)&&(i=l1(i,t.type,t.pendingProps,Jn),i!==null?(t.stateNode=i,Vt=t,st=zn(i.firstChild),Jn=!1,u=!0):u=!1),u||xa(t)),At(t),u=t.type,c=t.pendingProps,y=e!==null?e.memoizedProps:null,i=c.children,Fc(u,c)?i=null:y!==null&&Fc(u,y)&&(t.flags|=32),t.memoizedState!==null&&(u=Vo(e,t,xv,null,null,n),Rs._currentValue=u),Lr(e,t),Nt(e,t,i,n),t.child;case 6:return e===null&&He&&((e=n=st)&&(n=a1(n,t.pendingProps,Jn),n!==null?(t.stateNode=n,Vt=t,st=null,e=!0):e=!1),e||xa(t)),null;case 13:return Em(e,t,n);case 4:return Re(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=ui(t,null,i,n):Nt(e,t,i,n),t.child;case 11:return mm(e,t,t.type,t.pendingProps,n);case 7:return Nt(e,t,t.pendingProps,n),t.child;case 8:return Nt(e,t,t.pendingProps.children,n),t.child;case 12:return Nt(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,Vl(t,t.type,i.value),Nt(e,t,i.children,n),t.child;case 9:return u=t.type._context,i=t.pendingProps.children,wa(t),u=zt(u),i=i(u),t.flags|=1,Nt(e,t,i,n),t.child;case 14:return pm(e,t,t.type,t.pendingProps,n);case 15:return ym(e,t,t.type,t.pendingProps,n);case 19:return Am(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=zr(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Sl(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return gm(e,t,n);case 24:return wa(t),i=zt(xt),e===null?(u=No(),u===null&&(u=et,c=jo(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=n),u=c),t.memoizedState={parent:i,cache:u},Mo(t),Vl(t,xt,u)):((e.lanes&n)!==0&&(Lo(e,t),is(t,null,null,n),as()),u=e.memoizedState,c=t.memoizedState,u.parent!==i?(u={parent:i,cache:i},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Vl(t,xt,i)):(i=c.cache,Vl(t,xt,i),i!==u.cache&&Co(t,[xt],n,!0))),Nt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Ol(e){e.flags|=4}function Tm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!M0(t)){if(t=An.current,t!==null&&((ke&4194048)===ke?Pn!==null:(ke&62914560)!==ke&&(ke&536870912)===0||t!==Pn))throw ns=Uo,oh;e.flags|=8192}}function kr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?pl():536870912,e.lanes|=t,di|=t)}function ds(e,t){if(!He)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function at(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags&65011712,i|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function jv(e,t,n){var i=t.pendingProps;switch(_o(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return at(t),null;case 1:return at(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),Al(xt),St(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ki(t)?Ol(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ah())),at(t),null;case 26:return n=t.memoizedState,e===null?(Ol(t),n!==null?(at(t),Tm(t,n)):(at(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ol(t),at(t),Tm(t,n)):(at(t),t.flags&=-16777217):(e.memoizedProps!==i&&Ol(t),at(t),t.flags&=-16777217),null;case 27:jt(t),n=we.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(!i){if(t.stateNode===null)throw Error(r(166));return at(t),null}e=pe.current,Ki(t)?nh(t):(e=T0(u,i,n),t.stateNode=e,Ol(t))}return at(t),null;case 5:if(jt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(!i){if(t.stateNode===null)throw Error(r(166));return at(t),null}if(e=pe.current,Ki(t))nh(t);else{switch(u=Jr(we.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?u.createElement("select",{is:i.is}):u.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?u.createElement(n,{is:i.is}):u.createElement(n)}}e[de]=t,e[ye]=i;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Mt(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ol(t)}}return at(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(r(166));if(e=we.current,Ki(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,u=Vt,u!==null)switch(u.tag){case 27:case 5:i=u.memoizedProps}e[de]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||b0(e.nodeValue,n)),e||xa(t)}else e=Jr(e).createTextNode(i),e[de]=t,t.stateNode=e}return at(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=Ki(t),i!==null&&i.dehydrated!==null){if(e===null){if(!u)throw Error(r(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[de]=t}else Ji(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;at(t),u=!1}else u=ah(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Tl(t),t):(Tl(t),null)}if(Tl(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,u=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(u=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==u&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),kr(t,t.updateQueue),at(t),null;case 4:return St(),e===null&&kc(t.stateNode.containerInfo),at(t),null;case 10:return Al(t.type),at(t),null;case 19:if(ue(Et),u=t.memoizedState,u===null)return at(t),null;if(i=(t.flags&128)!==0,c=u.rendering,c===null)if(i)ds(u,!1);else{if(rt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Nr(e),c!==null){for(t.flags|=128,ds(u,!1),e=c.updateQueue,t.updateQueue=e,kr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)eh(n,e),n=n.sibling;return ae(Et,Et.current&1|2),t.child}e=e.sibling}u.tail!==null&&ut()>qr&&(t.flags|=128,i=!0,ds(u,!1),t.lanes=4194304)}else{if(!i)if(e=Nr(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,kr(t,e),ds(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!He)return at(t),null}else 2*ut()-u.renderingStartTime>qr&&n!==536870912&&(t.flags|=128,i=!0,ds(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=ut(),t.sibling=null,e=Et.current,ae(Et,i?e&1|2:e&1),t):(at(t),null);case 22:case 23:return Tl(t),Ho(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(at(t),t.subtreeFlags&6&&(t.flags|=8192)):at(t),n=t.updateQueue,n!==null&&kr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&ue(Aa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Al(xt),at(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function Dv(e,t){switch(_o(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Al(xt),St(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return jt(t),null;case 13:if(Tl(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));Ji()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ue(Et),null;case 4:return St(),null;case 10:return Al(t.type),null;case 22:case 23:return Tl(t),Ho(),e!==null&&ue(Aa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Al(xt),null;case 25:return null;default:return null}}function Rm(e,t){switch(_o(t),t.tag){case 3:Al(xt),St();break;case 26:case 27:case 5:jt(t);break;case 4:St();break;case 13:Tl(t);break;case 19:ue(Et);break;case 10:Al(t.type);break;case 22:case 23:Tl(t),Ho(),e!==null&&ue(Aa);break;case 24:Al(xt)}}function hs(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var u=i.next;n=u;do{if((n.tag&e)===e){i=void 0;var c=n.create,y=n.inst;i=c(),y.destroy=i}n=n.next}while(n!==u)}}catch(b){Je(t,t.return,b)}}function Ql(e,t,n){try{var i=t.updateQueue,u=i!==null?i.lastEffect:null;if(u!==null){var c=u.next;i=c;do{if((i.tag&e)===e){var y=i.inst,b=y.destroy;if(b!==void 0){y.destroy=void 0,u=t;var _=n,z=b;try{z()}catch(X){Je(u,_,X)}}}i=i.next}while(i!==c)}}catch(X){Je(t,t.return,X)}}function Om(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{ph(t,n)}catch(i){Je(e,e.return,i)}}}function Cm(e,t,n){n.props=Ta(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){Je(e,t,i)}}function ms(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(u){Je(e,t,u)}}function Wn(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(u){Je(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){Je(e,t,u)}else n.current=null}function jm(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(u){Je(e,e.return,u)}}function hc(e,t,n){try{var i=e.stateNode;Wv(i,e.type,n,t),i[ye]=t}catch(u){Je(e,e.return,u)}}function Dm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&na(e.type)||e.tag===4}function mc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&na(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pc(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Kr));else if(i!==4&&(i===27&&na(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(pc(e,t,n),e=e.sibling;e!==null;)pc(e,t,n),e=e.sibling}function Br(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&na(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Br(e,t,n),e=e.sibling;e!==null;)Br(e,t,n),e=e.sibling}function Nm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Mt(t,i,n),t[de]=e,t[ye]=n}catch(c){Je(e,e.return,c)}}var Cl=!1,dt=!1,yc=!1,Um=typeof WeakSet=="function"?WeakSet:Set,Ot=null;function Nv(e,t){if(e=e.containerInfo,qc=nu,e=$d(e),mo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var u=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var y=0,b=-1,_=-1,z=0,X=0,Q=e,B=null;t:for(;;){for(var V;Q!==n||u!==0&&Q.nodeType!==3||(b=y+u),Q!==c||i!==0&&Q.nodeType!==3||(_=y+i),Q.nodeType===3&&(y+=Q.nodeValue.length),(V=Q.firstChild)!==null;)B=Q,Q=V;for(;;){if(Q===e)break t;if(B===n&&++z===u&&(b=y),B===c&&++X===i&&(_=y),(V=Q.nextSibling)!==null)break;Q=B,B=Q.parentNode}Q=V}n=b===-1||_===-1?null:{start:b,end:_}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vc={focusedElem:e,selectionRange:n},nu=!1,Ot=t;Ot!==null;)if(t=Ot,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ot=e;else for(;Ot!==null;){switch(t=Ot,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,u=c.memoizedProps,c=c.memoizedState,i=n.stateNode;try{var Te=Ta(n.type,u,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(Te,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(Ee){Je(n,n.return,Ee)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)$c(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":$c(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Ot=e;break}Ot=t.return}}function Mm(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Kl(e,n),i&4&&hs(5,n);break;case 1:if(Kl(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(y){Je(n,n.return,y)}else{var u=Ta(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Je(n,n.return,y)}}i&64&&Om(n),i&512&&ms(n,n.return);break;case 3:if(Kl(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{ph(e,t)}catch(y){Je(n,n.return,y)}}break;case 27:t===null&&i&4&&Nm(n);case 26:case 5:Kl(e,n),t===null&&i&4&&jm(n),i&512&&ms(n,n.return);break;case 12:Kl(e,n);break;case 13:Kl(e,n),i&4&&km(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Vv.bind(null,n),i1(e,n))));break;case 22:if(i=n.memoizedState!==null||Cl,!i){t=t!==null&&t.memoizedState!==null||dt,u=Cl;var c=dt;Cl=i,(dt=t)&&!c?Jl(e,n,(n.subtreeFlags&8772)!==0):Kl(e,n),Cl=u,dt=c}break;case 30:break;default:Kl(e,n)}}function Lm(e){var t=e.alternate;t!==null&&(e.alternate=null,Lm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&$e(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var nt=null,Jt=!1;function jl(e,t,n){for(n=n.child;n!==null;)zm(e,t,n),n=n.sibling}function zm(e,t,n){if(J&&typeof J.onCommitFiberUnmount=="function")try{J.onCommitFiberUnmount(ee,n)}catch{}switch(n.tag){case 26:dt||Wn(n,t),jl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:dt||Wn(n,t);var i=nt,u=Jt;na(n.type)&&(nt=n.stateNode,Jt=!1),jl(e,t,n),ws(n.stateNode),nt=i,Jt=u;break;case 5:dt||Wn(n,t);case 6:if(i=nt,u=Jt,nt=null,jl(e,t,n),nt=i,Jt=u,nt!==null)if(Jt)try{(nt.nodeType===9?nt.body:nt.nodeName==="HTML"?nt.ownerDocument.body:nt).removeChild(n.stateNode)}catch(c){Je(n,t,c)}else try{nt.removeChild(n.stateNode)}catch(c){Je(n,t,c)}break;case 18:nt!==null&&(Jt?(e=nt,A0(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Ds(e)):A0(nt,n.stateNode));break;case 4:i=nt,u=Jt,nt=n.stateNode.containerInfo,Jt=!0,jl(e,t,n),nt=i,Jt=u;break;case 0:case 11:case 14:case 15:dt||Ql(2,n,t),dt||Ql(4,n,t),jl(e,t,n);break;case 1:dt||(Wn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Cm(n,t,i)),jl(e,t,n);break;case 21:jl(e,t,n);break;case 22:dt=(i=dt)||n.memoizedState!==null,jl(e,t,n),dt=i;break;default:jl(e,t,n)}}function km(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ds(e)}catch(n){Je(t,t.return,n)}}function Uv(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Um),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Um),t;default:throw Error(r(435,e.tag))}}function gc(e,t){var n=Uv(e);t.forEach(function(i){var u=Fv.bind(null,e,i);n.has(i)||(n.add(i),i.then(u,u))})}function on(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var u=n[i],c=e,y=t,b=y;e:for(;b!==null;){switch(b.tag){case 27:if(na(b.type)){nt=b.stateNode,Jt=!1;break e}break;case 5:nt=b.stateNode,Jt=!1;break e;case 3:case 4:nt=b.stateNode.containerInfo,Jt=!0;break e}b=b.return}if(nt===null)throw Error(r(160));zm(c,y,u),nt=null,Jt=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Bm(t,e),t=t.sibling}var Ln=null;function Bm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:on(t,e),cn(e),i&4&&(Ql(3,e,e.return),hs(3,e),Ql(5,e,e.return));break;case 1:on(t,e),cn(e),i&512&&(dt||n===null||Wn(n,n.return)),i&64&&Cl&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var u=Ln;if(on(t,e),cn(e),i&512&&(dt||n===null||Wn(n,n.return)),i&4){var c=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(i){case"title":c=u.getElementsByTagName("title")[0],(!c||c[Tt]||c[de]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(i),u.head.insertBefore(c,u.querySelector("head > title"))),Mt(c,i,n),c[de]=e,ct(c),i=c;break e;case"link":var y=N0("link","href",u).get(i+(n.href||""));if(y){for(var b=0;b<y.length;b++)if(c=y[b],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;case"meta":if(y=N0("meta","content",u).get(i+(n.content||""))){for(b=0;b<y.length;b++)if(c=y[b],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;default:throw Error(r(468,i))}c[de]=e,ct(c),i=c}e.stateNode=i}else U0(u,e.type,e.stateNode);else e.stateNode=D0(u,i,e.memoizedProps);else c!==i?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,i===null?U0(u,e.type,e.stateNode):D0(u,i,e.memoizedProps)):i===null&&e.stateNode!==null&&hc(e,e.memoizedProps,n.memoizedProps)}break;case 27:on(t,e),cn(e),i&512&&(dt||n===null||Wn(n,n.return)),n!==null&&i&4&&hc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(on(t,e),cn(e),i&512&&(dt||n===null||Wn(n,n.return)),e.flags&32){u=e.stateNode;try{$a(u,"")}catch(V){Je(e,e.return,V)}}i&4&&e.stateNode!=null&&(u=e.memoizedProps,hc(e,u,n!==null?n.memoizedProps:u)),i&1024&&(yc=!0);break;case 6:if(on(t,e),cn(e),i&4){if(e.stateNode===null)throw Error(r(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(V){Je(e,e.return,V)}}break;case 3:if(Ir=null,u=Ln,Ln=Pr(t.containerInfo),on(t,e),Ln=u,cn(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Ds(t.containerInfo)}catch(V){Je(e,e.return,V)}yc&&(yc=!1,Hm(e));break;case 4:i=Ln,Ln=Pr(e.stateNode.containerInfo),on(t,e),cn(e),Ln=i;break;case 12:on(t,e),cn(e);break;case 13:on(t,e),cn(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(wc=ut()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,gc(e,i)));break;case 22:u=e.memoizedState!==null;var _=n!==null&&n.memoizedState!==null,z=Cl,X=dt;if(Cl=z||u,dt=X||_,on(t,e),dt=X,Cl=z,cn(e),i&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||_||Cl||dt||Ra(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){_=n=t;try{if(c=_.stateNode,u)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{b=_.stateNode;var Q=_.memoizedProps.style,B=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;b.style.display=B==null||typeof B=="boolean"?"":(""+B).trim()}}catch(V){Je(_,_.return,V)}}}else if(t.tag===6){if(n===null){_=t;try{_.stateNode.nodeValue=u?"":_.memoizedProps}catch(V){Je(_,_.return,V)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,gc(e,n))));break;case 19:on(t,e),cn(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,gc(e,i)));break;case 30:break;case 21:break;default:on(t,e),cn(e)}}function cn(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(Dm(i)){n=i;break}i=i.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var u=n.stateNode,c=mc(e);Br(e,c,u);break;case 5:var y=n.stateNode;n.flags&32&&($a(y,""),n.flags&=-33);var b=mc(e);Br(e,b,y);break;case 3:case 4:var _=n.stateNode.containerInfo,z=mc(e);pc(e,z,_);break;default:throw Error(r(161))}}catch(X){Je(e,e.return,X)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Hm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Kl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Mm(e,t.alternate,t),t=t.sibling}function Ra(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ql(4,t,t.return),Ra(t);break;case 1:Wn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Cm(t,t.return,n),Ra(t);break;case 27:ws(t.stateNode);case 26:case 5:Wn(t,t.return),Ra(t);break;case 22:t.memoizedState===null&&Ra(t);break;case 30:Ra(t);break;default:Ra(t)}e=e.sibling}}function Jl(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,u=e,c=t,y=c.flags;switch(c.tag){case 0:case 11:case 15:Jl(u,c,n),hs(4,c);break;case 1:if(Jl(u,c,n),i=c,u=i.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(z){Je(i,i.return,z)}if(i=c,u=i.updateQueue,u!==null){var b=i.stateNode;try{var _=u.shared.hiddenCallbacks;if(_!==null)for(u.shared.hiddenCallbacks=null,u=0;u<_.length;u++)mh(_[u],b)}catch(z){Je(i,i.return,z)}}n&&y&64&&Om(c),ms(c,c.return);break;case 27:Nm(c);case 26:case 5:Jl(u,c,n),n&&i===null&&y&4&&jm(c),ms(c,c.return);break;case 12:Jl(u,c,n);break;case 13:Jl(u,c,n),n&&y&4&&km(u,c);break;case 22:c.memoizedState===null&&Jl(u,c,n),ms(c,c.return);break;case 30:break;default:Jl(u,c,n)}t=t.sibling}}function vc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Ii(n))}function bc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ii(e))}function In(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)qm(e,t,n,i),t=t.sibling}function qm(e,t,n,i){var u=t.flags;switch(t.tag){case 0:case 11:case 15:In(e,t,n,i),u&2048&&hs(9,t);break;case 1:In(e,t,n,i);break;case 3:In(e,t,n,i),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ii(e)));break;case 12:if(u&2048){In(e,t,n,i),e=t.stateNode;try{var c=t.memoizedProps,y=c.id,b=c.onPostCommit;typeof b=="function"&&b(y,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(_){Je(t,t.return,_)}}else In(e,t,n,i);break;case 13:In(e,t,n,i);break;case 23:break;case 22:c=t.stateNode,y=t.alternate,t.memoizedState!==null?c._visibility&2?In(e,t,n,i):ps(e,t):c._visibility&2?In(e,t,n,i):(c._visibility|=2,oi(e,t,n,i,(t.subtreeFlags&10256)!==0)),u&2048&&vc(y,t);break;case 24:In(e,t,n,i),u&2048&&bc(t.alternate,t);break;default:In(e,t,n,i)}}function oi(e,t,n,i,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,y=t,b=n,_=i,z=y.flags;switch(y.tag){case 0:case 11:case 15:oi(c,y,b,_,u),hs(8,y);break;case 23:break;case 22:var X=y.stateNode;y.memoizedState!==null?X._visibility&2?oi(c,y,b,_,u):ps(c,y):(X._visibility|=2,oi(c,y,b,_,u)),u&&z&2048&&vc(y.alternate,y);break;case 24:oi(c,y,b,_,u),u&&z&2048&&bc(y.alternate,y);break;default:oi(c,y,b,_,u)}t=t.sibling}}function ps(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,u=i.flags;switch(i.tag){case 22:ps(n,i),u&2048&&vc(i.alternate,i);break;case 24:ps(n,i),u&2048&&bc(i.alternate,i);break;default:ps(n,i)}t=t.sibling}}var ys=8192;function ci(e){if(e.subtreeFlags&ys)for(e=e.child;e!==null;)Vm(e),e=e.sibling}function Vm(e){switch(e.tag){case 26:ci(e),e.flags&ys&&e.memoizedState!==null&&v1(Ln,e.memoizedState,e.memoizedProps);break;case 5:ci(e);break;case 3:case 4:var t=Ln;Ln=Pr(e.stateNode.containerInfo),ci(e),Ln=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=ys,ys=16777216,ci(e),ys=t):ci(e));break;default:ci(e)}}function Fm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function gs(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Ot=i,$m(i,e)}Fm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ym(e),e=e.sibling}function Ym(e){switch(e.tag){case 0:case 11:case 15:gs(e),e.flags&2048&&Ql(9,e,e.return);break;case 3:gs(e);break;case 12:gs(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Hr(e)):gs(e);break;default:gs(e)}}function Hr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Ot=i,$m(i,e)}Fm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ql(8,t,t.return),Hr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Hr(t));break;default:Hr(t)}e=e.sibling}}function $m(e,t){for(;Ot!==null;){var n=Ot;switch(n.tag){case 0:case 11:case 15:Ql(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Ii(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Ot=i;else e:for(n=e;Ot!==null;){i=Ot;var u=i.sibling,c=i.return;if(Lm(i),i===n){Ot=null;break e}if(u!==null){u.return=c,Ot=u;break e}Ot=c}}}var Mv={getCacheForType:function(e){var t=zt(xt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Lv=typeof WeakMap=="function"?WeakMap:Map,Ve=0,et=null,Me=null,ke=0,Fe=0,fn=null,Pl=!1,fi=!1,Sc=!1,Dl=0,rt=0,Wl=0,Oa=0,xc=0,_n=0,di=0,vs=null,Pt=null,Ec=!1,wc=0,qr=1/0,Vr=null,Il=null,Ut=0,ea=null,hi=null,mi=0,Ac=0,_c=null,Gm=null,bs=0,Tc=null;function dn(){if((Ve&2)!==0&&ke!==0)return ke&-ke;if(q.T!==null){var e=ti;return e!==0?e:Uc()}return Lt()}function Xm(){_n===0&&(_n=(ke&536870912)===0||He?ml():536870912);var e=An.current;return e!==null&&(e.flags|=32),_n}function hn(e,t,n){(e===et&&(Fe===2||Fe===9)||e.cancelPendingCommit!==null)&&(pi(e,0),ta(e,ke,_n,!1)),Nn(e,n),((Ve&2)===0||e!==et)&&(e===et&&((Ve&2)===0&&(Oa|=n),rt===4&&ta(e,ke,_n,!1)),el(e))}function Zm(e,t,n){if((Ve&6)!==0)throw Error(r(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Zt(e,t),u=i?Bv(e,t):Cc(e,t,!0),c=i;do{if(u===0){fi&&!i&&ta(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!zv(n)){u=Cc(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){t=y;e:{var b=e;u=vs;var _=b.current.memoizedState.isDehydrated;if(_&&(pi(b,y).flags|=256),y=Cc(b,y,!1),y!==2){if(Sc&&!_){b.errorRecoveryDisabledLanes|=c,Oa|=c,u=4;break e}c=Pt,Pt=u,c!==null&&(Pt===null?Pt=c:Pt.push.apply(Pt,c))}u=y}if(c=!1,u!==2)continue}}if(u===1){pi(e,0),ta(e,t,0,!0);break}e:{switch(i=e,c=u,c){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:ta(i,t,_n,!Pl);break e;case 2:Pt=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(u=wc+300-ut(),10<u)){if(ta(i,t,_n,!Pl),an(i,0,!0)!==0)break e;i.timeoutHandle=E0(Qm.bind(null,i,n,Pt,Vr,Ec,t,_n,Oa,di,Pl,c,2,-0,0),u);break e}Qm(i,n,Pt,Vr,Ec,t,_n,Oa,di,Pl,c,0,-0,0)}}break}while(!0);el(e)}function Qm(e,t,n,i,u,c,y,b,_,z,X,Q,B,V){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Ts={stylesheets:null,count:0,unsuspend:g1},Vm(t),Q=b1(),Q!==null)){e.cancelPendingCommit=Q(t0.bind(null,e,t,c,n,i,u,y,b,_,X,1,B,V)),ta(e,c,y,!z);return}t0(e,t,c,n,i,u,y,b,_)}function zv(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var u=n[i],c=u.getSnapshot;u=u.value;try{if(!rn(c(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ta(e,t,n,i){t&=~xc,t&=~Oa,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var u=t;0<u;){var c=31-be(u),y=1<<c;i[c]=-1,u&=~y}n!==0&&ot(e,n,t)}function Fr(){return(Ve&6)===0?(Ss(0),!1):!0}function Rc(){if(Me!==null){if(Fe===0)var e=Me.return;else e=Me,wl=Ea=null,$o(e),ri=null,cs=0,e=Me;for(;e!==null;)Rm(e.alternate,e),e=e.return;Me=null}}function pi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,e1(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Rc(),et=e,Me=n=Sl(e.current,null),ke=t,Fe=0,fn=null,Pl=!1,fi=Zt(e,t),Sc=!1,di=_n=xc=Oa=Wl=rt=0,Pt=vs=null,Ec=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var u=31-be(i),c=1<<u;t|=e[u],i&=~c}return Dl=t,cr(),n}function Km(e,t){De=null,q.H=Cr,t===ts||t===br?(t=dh(),Fe=3):t===oh?(t=dh(),Fe=4):Fe=t===hm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,fn=t,Me===null&&(rt=1,Mr(e,Sn(t,e.current)))}function Jm(){var e=q.H;return q.H=Cr,e===null?Cr:e}function Pm(){var e=q.A;return q.A=Mv,e}function Oc(){rt=4,Pl||(ke&4194048)!==ke&&An.current!==null||(fi=!0),(Wl&134217727)===0&&(Oa&134217727)===0||et===null||ta(et,ke,_n,!1)}function Cc(e,t,n){var i=Ve;Ve|=2;var u=Jm(),c=Pm();(et!==e||ke!==t)&&(Vr=null,pi(e,t)),t=!1;var y=rt;e:do try{if(Fe!==0&&Me!==null){var b=Me,_=fn;switch(Fe){case 8:Rc(),y=6;break e;case 3:case 2:case 9:case 6:An.current===null&&(t=!0);var z=Fe;if(Fe=0,fn=null,yi(e,b,_,z),n&&fi){y=0;break e}break;default:z=Fe,Fe=0,fn=null,yi(e,b,_,z)}}kv(),y=rt;break}catch(X){Km(e,X)}while(!0);return t&&e.shellSuspendCounter++,wl=Ea=null,Ve=i,q.H=u,q.A=c,Me===null&&(et=null,ke=0,cr()),y}function kv(){for(;Me!==null;)Wm(Me)}function Bv(e,t){var n=Ve;Ve|=2;var i=Jm(),u=Pm();et!==e||ke!==t?(Vr=null,qr=ut()+500,pi(e,t)):fi=Zt(e,t);e:do try{if(Fe!==0&&Me!==null){t=Me;var c=fn;t:switch(Fe){case 1:Fe=0,fn=null,yi(e,t,c,1);break;case 2:case 9:if(ch(c)){Fe=0,fn=null,Im(t);break}t=function(){Fe!==2&&Fe!==9||et!==e||(Fe=7),el(e)},c.then(t,t);break e;case 3:Fe=7;break e;case 4:Fe=5;break e;case 7:ch(c)?(Fe=0,fn=null,Im(t)):(Fe=0,fn=null,yi(e,t,c,7));break;case 5:var y=null;switch(Me.tag){case 26:y=Me.memoizedState;case 5:case 27:var b=Me;if(!y||M0(y)){Fe=0,fn=null;var _=b.sibling;if(_!==null)Me=_;else{var z=b.return;z!==null?(Me=z,Yr(z)):Me=null}break t}}Fe=0,fn=null,yi(e,t,c,5);break;case 6:Fe=0,fn=null,yi(e,t,c,6);break;case 8:Rc(),rt=6;break e;default:throw Error(r(462))}}Hv();break}catch(X){Km(e,X)}while(!0);return wl=Ea=null,q.H=i,q.A=u,Ve=n,Me!==null?0:(et=null,ke=0,cr(),rt)}function Hv(){for(;Me!==null&&!Dt();)Wm(Me)}function Wm(e){var t=_m(e.alternate,e,Dl);e.memoizedProps=e.pendingProps,t===null?Yr(e):Me=t}function Im(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=bm(n,t,t.pendingProps,t.type,void 0,ke);break;case 11:t=bm(n,t,t.pendingProps,t.type.render,t.ref,ke);break;case 5:$o(t);default:Rm(n,t),t=Me=eh(t,Dl),t=_m(n,t,Dl)}e.memoizedProps=e.pendingProps,t===null?Yr(e):Me=t}function yi(e,t,n,i){wl=Ea=null,$o(t),ri=null,cs=0;var u=t.return;try{if(Ov(e,u,t,n,ke)){rt=1,Mr(e,Sn(n,e.current)),Me=null;return}}catch(c){if(u!==null)throw Me=u,c;rt=1,Mr(e,Sn(n,e.current)),Me=null;return}t.flags&32768?(He||i===1?e=!0:fi||(ke&536870912)!==0?e=!1:(Pl=e=!0,(i===2||i===9||i===3||i===6)&&(i=An.current,i!==null&&i.tag===13&&(i.flags|=16384))),e0(t,e)):Yr(t)}function Yr(e){var t=e;do{if((t.flags&32768)!==0){e0(t,Pl);return}e=t.return;var n=jv(t.alternate,t,Dl);if(n!==null){Me=n;return}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);rt===0&&(rt=5)}function e0(e,t){do{var n=Dv(e.alternate,e);if(n!==null){n.flags&=32767,Me=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Me=e;return}Me=e=n}while(e!==null);rt=6,Me=null}function t0(e,t,n,i,u,c,y,b,_){e.cancelPendingCommit=null;do $r();while(Ut!==0);if((Ve&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(c=t.lanes|t.childLanes,c|=bo,qa(e,n,c,y,b,_),e===et&&(Me=et=null,ke=0),hi=t,ea=e,mi=n,Ac=c,_c=u,Gm=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Yv(ln,function(){return s0(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=q.T,q.T=null,u=P.p,P.p=2,y=Ve,Ve|=4;try{Nv(e,t,n)}finally{Ve=y,P.p=u,q.T=i}}Ut=1,n0(),l0(),a0()}}function n0(){if(Ut===1){Ut=0;var e=ea,t=hi,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=q.T,q.T=null;var i=P.p;P.p=2;var u=Ve;Ve|=4;try{Bm(t,e);var c=Vc,y=$d(e.containerInfo),b=c.focusedElem,_=c.selectionRange;if(y!==b&&b&&b.ownerDocument&&Yd(b.ownerDocument.documentElement,b)){if(_!==null&&mo(b)){var z=_.start,X=_.end;if(X===void 0&&(X=z),"selectionStart"in b)b.selectionStart=z,b.selectionEnd=Math.min(X,b.value.length);else{var Q=b.ownerDocument||document,B=Q&&Q.defaultView||window;if(B.getSelection){var V=B.getSelection(),Te=b.textContent.length,Ee=Math.min(_.start,Te),Ze=_.end===void 0?Ee:Math.min(_.end,Te);!V.extend&&Ee>Ze&&(y=Ze,Ze=Ee,Ee=y);var U=Fd(b,Ee),N=Fd(b,Ze);if(U&&N&&(V.rangeCount!==1||V.anchorNode!==U.node||V.anchorOffset!==U.offset||V.focusNode!==N.node||V.focusOffset!==N.offset)){var L=Q.createRange();L.setStart(U.node,U.offset),V.removeAllRanges(),Ee>Ze?(V.addRange(L),V.extend(N.node,N.offset)):(L.setEnd(N.node,N.offset),V.addRange(L))}}}}for(Q=[],V=b;V=V.parentNode;)V.nodeType===1&&Q.push({element:V,left:V.scrollLeft,top:V.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<Q.length;b++){var Z=Q[b];Z.element.scrollLeft=Z.left,Z.element.scrollTop=Z.top}}nu=!!qc,Vc=qc=null}finally{Ve=u,P.p=i,q.T=n}}e.current=t,Ut=2}}function l0(){if(Ut===2){Ut=0;var e=ea,t=hi,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=q.T,q.T=null;var i=P.p;P.p=2;var u=Ve;Ve|=4;try{Mm(e,t.alternate,t)}finally{Ve=u,P.p=i,q.T=n}}Ut=3}}function a0(){if(Ut===4||Ut===3){Ut=0,dl();var e=ea,t=hi,n=mi,i=Gm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ut=5:(Ut=0,hi=ea=null,i0(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Il=null),Ne(n),t=t.stateNode,J&&typeof J.onCommitFiberRoot=="function")try{J.onCommitFiberRoot(ee,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=q.T,u=P.p,P.p=2,q.T=null;try{for(var c=e.onRecoverableError,y=0;y<i.length;y++){var b=i[y];c(b.value,{componentStack:b.stack})}}finally{q.T=t,P.p=u}}(mi&3)!==0&&$r(),el(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Tc?bs++:(bs=0,Tc=e):bs=0,Ss(0)}}function i0(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ii(t)))}function $r(e){return n0(),l0(),a0(),s0()}function s0(){if(Ut!==5)return!1;var e=ea,t=Ac;Ac=0;var n=Ne(mi),i=q.T,u=P.p;try{P.p=32>n?32:n,q.T=null,n=_c,_c=null;var c=ea,y=mi;if(Ut=0,hi=ea=null,mi=0,(Ve&6)!==0)throw Error(r(331));var b=Ve;if(Ve|=4,Ym(c.current),qm(c,c.current,y,n),Ve=b,Ss(0,!1),J&&typeof J.onPostCommitFiberRoot=="function")try{J.onPostCommitFiberRoot(ee,c)}catch{}return!0}finally{P.p=u,q.T=i,i0(e,t)}}function r0(e,t,n){t=Sn(n,t),t=ac(e.stateNode,t,2),e=$l(e,t,2),e!==null&&(Nn(e,2),el(e))}function Je(e,t,n){if(e.tag===3)r0(e,e,n);else for(;t!==null;){if(t.tag===3){r0(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Il===null||!Il.has(i))){e=Sn(n,e),n=fm(2),i=$l(t,n,2),i!==null&&(dm(n,i,t,e),Nn(i,2),el(i));break}}t=t.return}}function jc(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new Lv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(n)||(Sc=!0,u.add(n),e=qv.bind(null,e,t,n),t.then(e,e))}function qv(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,et===e&&(ke&n)===n&&(rt===4||rt===3&&(ke&62914560)===ke&&300>ut()-wc?(Ve&2)===0&&pi(e,0):xc|=n,di===ke&&(di=0)),el(e)}function u0(e,t){t===0&&(t=pl()),e=Pa(e,t),e!==null&&(Nn(e,t),el(e))}function Vv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),u0(e,n)}function Fv(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(r(314))}i!==null&&i.delete(t),u0(e,n)}function Yv(e,t){return Gt(e,t)}var Gr=null,gi=null,Dc=!1,Xr=!1,Nc=!1,Ca=0;function el(e){e!==gi&&e.next===null&&(gi===null?Gr=gi=e:gi=gi.next=e),Xr=!0,Dc||(Dc=!0,Gv())}function Ss(e,t){if(!Nc&&Xr){Nc=!0;do for(var n=!1,i=Gr;i!==null;){if(e!==0){var u=i.pendingLanes;if(u===0)var c=0;else{var y=i.suspendedLanes,b=i.pingedLanes;c=(1<<31-be(42|e)+1)-1,c&=u&~(y&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,d0(i,c))}else c=ke,c=an(i,i===et?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||Zt(i,c)||(n=!0,d0(i,c));i=i.next}while(n);Nc=!1}}function $v(){o0()}function o0(){Xr=Dc=!1;var e=0;Ca!==0&&(Iv()&&(e=Ca),Ca=0);for(var t=ut(),n=null,i=Gr;i!==null;){var u=i.next,c=c0(i,t);c===0?(i.next=null,n===null?Gr=u:n.next=u,u===null&&(gi=n)):(n=i,(e!==0||(c&3)!==0)&&(Xr=!0)),i=u}Ss(e)}function c0(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var y=31-be(c),b=1<<y,_=u[y];_===-1?((b&n)===0||(b&i)!==0)&&(u[y]=Zn(b,t)):_<=t&&(e.expiredLanes|=b),c&=~b}if(t=et,n=ke,n=an(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Fe===2||Fe===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Xn(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Zt(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Xn(i),Ne(n)){case 2:case 8:n=hl;break;case 32:n=ln;break;case 268435456:n=k;break;default:n=ln}return i=f0.bind(null,e),n=Gt(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Xn(i),e.callbackPriority=2,e.callbackNode=null,2}function f0(e,t){if(Ut!==0&&Ut!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if($r()&&e.callbackNode!==n)return null;var i=ke;return i=an(e,e===et?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(Zm(e,i,t),c0(e,ut()),e.callbackNode!=null&&e.callbackNode===n?f0.bind(null,e):null)}function d0(e,t){if($r())return null;Zm(e,t,!0)}function Gv(){t1(function(){(Ve&6)!==0?Gt(Ye,$v):o0()})}function Uc(){return Ca===0&&(Ca=ml()),Ca}function h0(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:lr(""+e)}function m0(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function Xv(e,t,n,i,u){if(t==="submit"&&n&&n.stateNode===u){var c=h0((u[ye]||null).action),y=i.submitter;y&&(t=(t=y[ye]||null)?h0(t.formAction):y.getAttribute("formAction"),t!==null&&(c=t,y=null));var b=new rr("action","action",null,i,u);e.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ca!==0){var _=y?m0(u,y):new FormData(u);Io(n,{pending:!0,data:_,method:u.method,action:c},null,_)}}else typeof c=="function"&&(b.preventDefault(),_=y?m0(u,y):new FormData(u),Io(n,{pending:!0,data:_,method:u.method,action:c},c,_))},currentTarget:u}]})}}for(var Mc=0;Mc<vo.length;Mc++){var Lc=vo[Mc],Zv=Lc.toLowerCase(),Qv=Lc[0].toUpperCase()+Lc.slice(1);Mn(Zv,"on"+Qv)}Mn(Zd,"onAnimationEnd"),Mn(Qd,"onAnimationIteration"),Mn(Kd,"onAnimationStart"),Mn("dblclick","onDoubleClick"),Mn("focusin","onFocus"),Mn("focusout","onBlur"),Mn(fv,"onTransitionRun"),Mn(dv,"onTransitionStart"),Mn(hv,"onTransitionCancel"),Mn(Jd,"onTransitionEnd"),ze("onMouseEnter",["mouseout","mouseover"]),ze("onMouseLeave",["mouseout","mouseover"]),ze("onPointerEnter",["pointerout","pointerover"]),ze("onPointerLeave",["pointerout","pointerover"]),Kn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Kn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Kn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Kn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Kn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Kn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Kv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(xs));function p0(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],u=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var y=i.length-1;0<=y;y--){var b=i[y],_=b.instance,z=b.currentTarget;if(b=b.listener,_!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=z;try{c(u)}catch(X){Ur(X)}u.currentTarget=null,c=_}else for(y=0;y<i.length;y++){if(b=i[y],_=b.instance,z=b.currentTarget,b=b.listener,_!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=z;try{c(u)}catch(X){Ur(X)}u.currentTarget=null,c=_}}}}function Le(e,t){var n=t[yt];n===void 0&&(n=t[yt]=new Set);var i=e+"__bubble";n.has(i)||(y0(t,e,2,!1),n.add(i))}function zc(e,t,n){var i=0;t&&(i|=4),y0(n,e,i,t)}var Zr="_reactListening"+Math.random().toString(36).slice(2);function kc(e){if(!e[Zr]){e[Zr]=!0,Bi.forEach(function(n){n!=="selectionchange"&&(Kv.has(n)||zc(n,!1,e),zc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Zr]||(t[Zr]=!0,zc("selectionchange",!1,t))}}function y0(e,t,n,i){switch(q0(t)){case 2:var u=E1;break;case 8:u=w1;break;default:u=Pc}n=u.bind(null,t,n,e),u=void 0,!ao||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Bc(e,t,n,i,u){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var y=i.tag;if(y===3||y===4){var b=i.stateNode.containerInfo;if(b===u)break;if(y===4)for(y=i.return;y!==null;){var _=y.tag;if((_===3||_===4)&&y.stateNode.containerInfo===u)return;y=y.return}for(;b!==null;){if(y=sn(b),y===null)return;if(_=y.tag,_===5||_===6||_===26||_===27){i=c=y;continue e}b=b.parentNode}}i=i.return}wd(function(){var z=c,X=no(n),Q=[];e:{var B=Pd.get(e);if(B!==void 0){var V=rr,Te=e;switch(e){case"keypress":if(ir(n)===0)break e;case"keydown":case"keyup":V=Yg;break;case"focusin":Te="focus",V=uo;break;case"focusout":Te="blur",V=uo;break;case"beforeblur":case"afterblur":V=uo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":V=Td;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":V=Dg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":V=Xg;break;case Zd:case Qd:case Kd:V=Mg;break;case Jd:V=Qg;break;case"scroll":case"scrollend":V=Cg;break;case"wheel":V=Jg;break;case"copy":case"cut":case"paste":V=zg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":V=Od;break;case"toggle":case"beforetoggle":V=Wg}var Ee=(t&4)!==0,Ze=!Ee&&(e==="scroll"||e==="scrollend"),U=Ee?B!==null?B+"Capture":null:B;Ee=[];for(var N=z,L;N!==null;){var Z=N;if(L=Z.stateNode,Z=Z.tag,Z!==5&&Z!==26&&Z!==27||L===null||U===null||(Z=qi(N,U),Z!=null&&Ee.push(Es(N,Z,L))),Ze)break;N=N.return}0<Ee.length&&(B=new V(B,Te,null,n,X),Q.push({event:B,listeners:Ee}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",V=e==="mouseout"||e==="pointerout",B&&n!==to&&(Te=n.relatedTarget||n.fromElement)&&(sn(Te)||Te[Ie]))break e;if((V||B)&&(B=X.window===X?X:(B=X.ownerDocument)?B.defaultView||B.parentWindow:window,V?(Te=n.relatedTarget||n.toElement,V=z,Te=Te?sn(Te):null,Te!==null&&(Ze=f(Te),Ee=Te.tag,Te!==Ze||Ee!==5&&Ee!==27&&Ee!==6)&&(Te=null)):(V=null,Te=z),V!==Te)){if(Ee=Td,Z="onMouseLeave",U="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(Ee=Od,Z="onPointerLeave",U="onPointerEnter",N="pointer"),Ze=V==null?B:ha(V),L=Te==null?B:ha(Te),B=new Ee(Z,N+"leave",V,n,X),B.target=Ze,B.relatedTarget=L,Z=null,sn(X)===z&&(Ee=new Ee(U,N+"enter",Te,n,X),Ee.target=L,Ee.relatedTarget=Ze,Z=Ee),Ze=Z,V&&Te)t:{for(Ee=V,U=Te,N=0,L=Ee;L;L=vi(L))N++;for(L=0,Z=U;Z;Z=vi(Z))L++;for(;0<N-L;)Ee=vi(Ee),N--;for(;0<L-N;)U=vi(U),L--;for(;N--;){if(Ee===U||U!==null&&Ee===U.alternate)break t;Ee=vi(Ee),U=vi(U)}Ee=null}else Ee=null;V!==null&&g0(Q,B,V,Ee,!1),Te!==null&&Ze!==null&&g0(Q,Ze,Te,Ee,!0)}}e:{if(B=z?ha(z):window,V=B.nodeName&&B.nodeName.toLowerCase(),V==="select"||V==="input"&&B.type==="file")var he=zd;else if(Md(B))if(kd)he=uv;else{he=sv;var Ue=iv}else V=B.nodeName,!V||V.toLowerCase()!=="input"||B.type!=="checkbox"&&B.type!=="radio"?z&&eo(z.elementType)&&(he=zd):he=rv;if(he&&(he=he(e,z))){Ld(Q,he,n,X);break e}Ue&&Ue(e,B,z),e==="focusout"&&z&&B.type==="number"&&z.memoizedProps.value!=null&&Iu(B,"number",B.value)}switch(Ue=z?ha(z):window,e){case"focusin":(Md(Ue)||Ue.contentEditable==="true")&&(Qa=Ue,po=z,Qi=null);break;case"focusout":Qi=po=Qa=null;break;case"mousedown":yo=!0;break;case"contextmenu":case"mouseup":case"dragend":yo=!1,Gd(Q,n,X);break;case"selectionchange":if(cv)break;case"keydown":case"keyup":Gd(Q,n,X)}var ge;if(co)e:{switch(e){case"compositionstart":var Ae="onCompositionStart";break e;case"compositionend":Ae="onCompositionEnd";break e;case"compositionupdate":Ae="onCompositionUpdate";break e}Ae=void 0}else Za?Nd(e,n)&&(Ae="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Ae="onCompositionStart");Ae&&(Cd&&n.locale!=="ko"&&(Za||Ae!=="onCompositionStart"?Ae==="onCompositionEnd"&&Za&&(ge=Ad()):(ql=X,io="value"in ql?ql.value:ql.textContent,Za=!0)),Ue=Qr(z,Ae),0<Ue.length&&(Ae=new Rd(Ae,e,null,n,X),Q.push({event:Ae,listeners:Ue}),ge?Ae.data=ge:(ge=Ud(n),ge!==null&&(Ae.data=ge)))),(ge=ev?tv(e,n):nv(e,n))&&(Ae=Qr(z,"onBeforeInput"),0<Ae.length&&(Ue=new Rd("onBeforeInput","beforeinput",null,n,X),Q.push({event:Ue,listeners:Ae}),Ue.data=ge)),Xv(Q,e,z,n,X)}p0(Q,t)})}function Es(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",i=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=qi(e,n),u!=null&&i.unshift(Es(e,u,c)),u=qi(e,t),u!=null&&i.push(Es(e,u,c))),e.tag===3)return i;e=e.return}return[]}function vi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function g0(e,t,n,i,u){for(var c=t._reactName,y=[];n!==null&&n!==i;){var b=n,_=b.alternate,z=b.stateNode;if(b=b.tag,_!==null&&_===i)break;b!==5&&b!==26&&b!==27||z===null||(_=z,u?(z=qi(n,c),z!=null&&y.unshift(Es(n,z,_))):u||(z=qi(n,c),z!=null&&y.push(Es(n,z,_)))),n=n.return}y.length!==0&&e.push({event:t,listeners:y})}var Jv=/\r\n?/g,Pv=/\u0000|\uFFFD/g;function v0(e){return(typeof e=="string"?e:""+e).replace(Jv,`
`).replace(Pv,"")}function b0(e,t){return t=v0(t),v0(e)===t}function Kr(){}function Xe(e,t,n,i,u,c){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||$a(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&$a(e,""+i);break;case"className":er(e,"class",i);break;case"tabIndex":er(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":er(e,n,i);break;case"style":xd(e,i,c);break;case"data":if(t!=="object"){er(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=lr(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Xe(e,t,"name",u.name,u,null),Xe(e,t,"formEncType",u.formEncType,u,null),Xe(e,t,"formMethod",u.formMethod,u,null),Xe(e,t,"formTarget",u.formTarget,u,null)):(Xe(e,t,"encType",u.encType,u,null),Xe(e,t,"method",u.method,u,null),Xe(e,t,"target",u.target,u,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=lr(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=Kr);break;case"onScroll":i!=null&&Le("scroll",e);break;case"onScrollEnd":i!=null&&Le("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=lr(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":Le("beforetoggle",e),Le("toggle",e),Hl(e,"popover",i);break;case"xlinkActuate":vl(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":vl(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":vl(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":vl(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":vl(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":vl(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":vl(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":vl(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":vl(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Hl(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Rg.get(n)||n,Hl(e,n,i))}}function Hc(e,t,n,i,u,c){switch(n){case"style":xd(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"children":typeof i=="string"?$a(e,i):(typeof i=="number"||typeof i=="bigint")&&$a(e,""+i);break;case"onScroll":i!=null&&Le("scroll",e);break;case"onScrollEnd":i!=null&&Le("scrollend",e);break;case"onClick":i!=null&&(e.onclick=Kr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Hi.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),c=e[ye]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof i=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,u);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):Hl(e,n,i)}}}function Mt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Le("error",e),Le("load",e);var i=!1,u=!1,c;for(c in n)if(n.hasOwnProperty(c)){var y=n[c];if(y!=null)switch(c){case"src":i=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Xe(e,t,c,y,n,null)}}u&&Xe(e,t,"srcSet",n.srcSet,n,null),i&&Xe(e,t,"src",n.src,n,null);return;case"input":Le("invalid",e);var b=c=y=u=null,_=null,z=null;for(i in n)if(n.hasOwnProperty(i)){var X=n[i];if(X!=null)switch(i){case"name":u=X;break;case"type":y=X;break;case"checked":_=X;break;case"defaultChecked":z=X;break;case"value":c=X;break;case"defaultValue":b=X;break;case"children":case"dangerouslySetInnerHTML":if(X!=null)throw Error(r(137,t));break;default:Xe(e,t,i,X,n,null)}}gd(e,c,b,_,z,y,u,!1),tr(e);return;case"select":Le("invalid",e),i=y=c=null;for(u in n)if(n.hasOwnProperty(u)&&(b=n[u],b!=null))switch(u){case"value":c=b;break;case"defaultValue":y=b;break;case"multiple":i=b;default:Xe(e,t,u,b,n,null)}t=c,n=y,e.multiple=!!i,t!=null?Ya(e,!!i,t,!1):n!=null&&Ya(e,!!i,n,!0);return;case"textarea":Le("invalid",e),c=u=i=null;for(y in n)if(n.hasOwnProperty(y)&&(b=n[y],b!=null))switch(y){case"value":i=b;break;case"defaultValue":u=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(r(91));break;default:Xe(e,t,y,b,n,null)}bd(e,i,u,c),tr(e);return;case"option":for(_ in n)if(n.hasOwnProperty(_)&&(i=n[_],i!=null))switch(_){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Xe(e,t,_,i,n,null)}return;case"dialog":Le("beforetoggle",e),Le("toggle",e),Le("cancel",e),Le("close",e);break;case"iframe":case"object":Le("load",e);break;case"video":case"audio":for(i=0;i<xs.length;i++)Le(xs[i],e);break;case"image":Le("error",e),Le("load",e);break;case"details":Le("toggle",e);break;case"embed":case"source":case"link":Le("error",e),Le("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in n)if(n.hasOwnProperty(z)&&(i=n[z],i!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Xe(e,t,z,i,n,null)}return;default:if(eo(t)){for(X in n)n.hasOwnProperty(X)&&(i=n[X],i!==void 0&&Hc(e,t,X,i,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(i=n[b],i!=null&&Xe(e,t,b,i,n,null))}function Wv(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,y=null,b=null,_=null,z=null,X=null;for(V in n){var Q=n[V];if(n.hasOwnProperty(V)&&Q!=null)switch(V){case"checked":break;case"value":break;case"defaultValue":_=Q;default:i.hasOwnProperty(V)||Xe(e,t,V,null,i,Q)}}for(var B in i){var V=i[B];if(Q=n[B],i.hasOwnProperty(B)&&(V!=null||Q!=null))switch(B){case"type":c=V;break;case"name":u=V;break;case"checked":z=V;break;case"defaultChecked":X=V;break;case"value":y=V;break;case"defaultValue":b=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(r(137,t));break;default:V!==Q&&Xe(e,t,B,V,i,Q)}}Wu(e,y,b,_,z,X,c,u);return;case"select":V=y=b=B=null;for(c in n)if(_=n[c],n.hasOwnProperty(c)&&_!=null)switch(c){case"value":break;case"multiple":V=_;default:i.hasOwnProperty(c)||Xe(e,t,c,null,i,_)}for(u in i)if(c=i[u],_=n[u],i.hasOwnProperty(u)&&(c!=null||_!=null))switch(u){case"value":B=c;break;case"defaultValue":b=c;break;case"multiple":y=c;default:c!==_&&Xe(e,t,u,c,i,_)}t=b,n=y,i=V,B!=null?Ya(e,!!n,B,!1):!!i!=!!n&&(t!=null?Ya(e,!!n,t,!0):Ya(e,!!n,n?[]:"",!1));return;case"textarea":V=B=null;for(b in n)if(u=n[b],n.hasOwnProperty(b)&&u!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Xe(e,t,b,null,i,u)}for(y in i)if(u=i[y],c=n[y],i.hasOwnProperty(y)&&(u!=null||c!=null))switch(y){case"value":B=u;break;case"defaultValue":V=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==c&&Xe(e,t,y,u,i,c)}vd(e,B,V);return;case"option":for(var Te in n)if(B=n[Te],n.hasOwnProperty(Te)&&B!=null&&!i.hasOwnProperty(Te))switch(Te){case"selected":e.selected=!1;break;default:Xe(e,t,Te,null,i,B)}for(_ in i)if(B=i[_],V=n[_],i.hasOwnProperty(_)&&B!==V&&(B!=null||V!=null))switch(_){case"selected":e.selected=B&&typeof B!="function"&&typeof B!="symbol";break;default:Xe(e,t,_,B,i,V)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Ee in n)B=n[Ee],n.hasOwnProperty(Ee)&&B!=null&&!i.hasOwnProperty(Ee)&&Xe(e,t,Ee,null,i,B);for(z in i)if(B=i[z],V=n[z],i.hasOwnProperty(z)&&B!==V&&(B!=null||V!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(r(137,t));break;default:Xe(e,t,z,B,i,V)}return;default:if(eo(t)){for(var Ze in n)B=n[Ze],n.hasOwnProperty(Ze)&&B!==void 0&&!i.hasOwnProperty(Ze)&&Hc(e,t,Ze,void 0,i,B);for(X in i)B=i[X],V=n[X],!i.hasOwnProperty(X)||B===V||B===void 0&&V===void 0||Hc(e,t,X,B,i,V);return}}for(var U in n)B=n[U],n.hasOwnProperty(U)&&B!=null&&!i.hasOwnProperty(U)&&Xe(e,t,U,null,i,B);for(Q in i)B=i[Q],V=n[Q],!i.hasOwnProperty(Q)||B===V||B==null&&V==null||Xe(e,t,Q,B,i,V)}var qc=null,Vc=null;function Jr(e){return e.nodeType===9?e:e.ownerDocument}function S0(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function x0(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Fc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yc=null;function Iv(){var e=window.event;return e&&e.type==="popstate"?e===Yc?!1:(Yc=e,!0):(Yc=null,!1)}var E0=typeof setTimeout=="function"?setTimeout:void 0,e1=typeof clearTimeout=="function"?clearTimeout:void 0,w0=typeof Promise=="function"?Promise:void 0,t1=typeof queueMicrotask=="function"?queueMicrotask:typeof w0<"u"?function(e){return w0.resolve(null).then(e).catch(n1)}:E0;function n1(e){setTimeout(function(){throw e})}function na(e){return e==="head"}function A0(e,t){var n=t,i=0,u=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<i&&8>i){n=i;var y=e.ownerDocument;if(n&1&&ws(y.documentElement),n&2&&ws(y.body),n&4)for(n=y.head,ws(n),y=n.firstChild;y;){var b=y.nextSibling,_=y.nodeName;y[Tt]||_==="SCRIPT"||_==="STYLE"||_==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=b}}if(u===0){e.removeChild(c),Ds(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:i=n.charCodeAt(0)-48;else i=0;n=c}while(n);Ds(t)}function $c(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":$c(n),$e(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function l1(e,t,n,i){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Tt])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=zn(e.nextSibling),e===null)break}return null}function a1(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=zn(e.nextSibling),e===null))return null;return e}function Gc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function i1(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function zn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xc=null;function _0(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function T0(e,t,n){switch(t=Jr(n),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function ws(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);$e(e)}var Tn=new Map,R0=new Set;function Pr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Nl=P.d;P.d={f:s1,r:r1,D:u1,C:o1,L:c1,m:f1,X:h1,S:d1,M:m1};function s1(){var e=Nl.f(),t=Fr();return e||t}function r1(e){var t=kl(e);t!==null&&t.tag===5&&t.type==="form"?Zh(t):Nl.r(e)}var bi=typeof document>"u"?null:document;function O0(e,t,n){var i=bi;if(i&&typeof t=="string"&&t){var u=bn(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),R0.has(u)||(R0.add(u),e={rel:e,crossOrigin:n,href:t},i.querySelector(u)===null&&(t=i.createElement("link"),Mt(t,"link",e),ct(t),i.head.appendChild(t)))}}function u1(e){Nl.D(e),O0("dns-prefetch",e,null)}function o1(e,t){Nl.C(e,t),O0("preconnect",e,t)}function c1(e,t,n){Nl.L(e,t,n);var i=bi;if(i&&e&&t){var u='link[rel="preload"][as="'+bn(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+bn(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+bn(n.imageSizes)+'"]')):u+='[href="'+bn(e)+'"]';var c=u;switch(t){case"style":c=Si(e);break;case"script":c=xi(e)}Tn.has(c)||(e=g({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Tn.set(c,e),i.querySelector(u)!==null||t==="style"&&i.querySelector(As(c))||t==="script"&&i.querySelector(_s(c))||(t=i.createElement("link"),Mt(t,"link",e),ct(t),i.head.appendChild(t)))}}function f1(e,t){Nl.m(e,t);var n=bi;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+bn(i)+'"][href="'+bn(e)+'"]',c=u;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=xi(e)}if(!Tn.has(c)&&(e=g({rel:"modulepreload",href:e},t),Tn.set(c,e),n.querySelector(u)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(_s(c)))return}i=n.createElement("link"),Mt(i,"link",e),ct(i),n.head.appendChild(i)}}}function d1(e,t,n){Nl.S(e,t,n);var i=bi;if(i&&e){var u=gl(i).hoistableStyles,c=Si(e);t=t||"default";var y=u.get(c);if(!y){var b={loading:0,preload:null};if(y=i.querySelector(As(c)))b.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Tn.get(c))&&Zc(e,n);var _=y=i.createElement("link");ct(_),Mt(_,"link",e),_._p=new Promise(function(z,X){_.onload=z,_.onerror=X}),_.addEventListener("load",function(){b.loading|=1}),_.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Wr(y,t,i)}y={type:"stylesheet",instance:y,count:1,state:b},u.set(c,y)}}}function h1(e,t){Nl.X(e,t);var n=bi;if(n&&e){var i=gl(n).hoistableScripts,u=xi(e),c=i.get(u);c||(c=n.querySelector(_s(u)),c||(e=g({src:e,async:!0},t),(t=Tn.get(u))&&Qc(e,t),c=n.createElement("script"),ct(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function m1(e,t){Nl.M(e,t);var n=bi;if(n&&e){var i=gl(n).hoistableScripts,u=xi(e),c=i.get(u);c||(c=n.querySelector(_s(u)),c||(e=g({src:e,async:!0,type:"module"},t),(t=Tn.get(u))&&Qc(e,t),c=n.createElement("script"),ct(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function C0(e,t,n,i){var u=(u=we.current)?Pr(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Si(n.href),n=gl(u).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Si(n.href);var c=gl(u).hoistableStyles,y=c.get(e);if(y||(u=u.ownerDocument||u,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,y),(c=u.querySelector(As(e)))&&!c._p&&(y.instance=c,y.state.loading=5),Tn.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Tn.set(e,n),c||p1(u,e,n,y.state))),t&&i===null)throw Error(r(528,""));return y}if(t&&i!==null)throw Error(r(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=xi(n),n=gl(u).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Si(e){return'href="'+bn(e)+'"'}function As(e){return'link[rel="stylesheet"]['+e+"]"}function j0(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function p1(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),Mt(t,"link",n),ct(t),e.head.appendChild(t))}function xi(e){return'[src="'+bn(e)+'"]'}function _s(e){return"script[async]"+e}function D0(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+bn(n.href)+'"]');if(i)return t.instance=i,ct(i),i;var u=g({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),ct(i),Mt(i,"style",u),Wr(i,n.precedence,e),t.instance=i;case"stylesheet":u=Si(n.href);var c=e.querySelector(As(u));if(c)return t.state.loading|=4,t.instance=c,ct(c),c;i=j0(n),(u=Tn.get(u))&&Zc(i,u),c=(e.ownerDocument||e).createElement("link"),ct(c);var y=c;return y._p=new Promise(function(b,_){y.onload=b,y.onerror=_}),Mt(c,"link",i),t.state.loading|=4,Wr(c,n.precedence,e),t.instance=c;case"script":return c=xi(n.src),(u=e.querySelector(_s(c)))?(t.instance=u,ct(u),u):(i=n,(u=Tn.get(c))&&(i=g({},n),Qc(i,u)),e=e.ownerDocument||e,u=e.createElement("script"),ct(u),Mt(u,"link",i),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,Wr(i,n.precedence,e));return t.instance}function Wr(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=i.length?i[i.length-1]:null,c=u,y=0;y<i.length;y++){var b=i[y];if(b.dataset.precedence===t)c=b;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Zc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ir=null;function N0(e,t,n){if(Ir===null){var i=new Map,u=Ir=new Map;u.set(n,i)}else u=Ir,i=u.get(n),i||(i=new Map,u.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var c=n[u];if(!(c[Tt]||c[de]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(t)||"";y=e+y;var b=i.get(y);b?b.push(c):i.set(y,[c])}}return i}function U0(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function y1(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function M0(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ts=null;function g1(){}function v1(e,t,n){if(Ts===null)throw Error(r(475));var i=Ts;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Si(n.href),c=e.querySelector(As(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=eu.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,ct(c);return}c=e.ownerDocument||e,n=j0(n),(u=Tn.get(u))&&Zc(n,u),c=c.createElement("link"),ct(c);var y=c;y._p=new Promise(function(b,_){y.onload=b,y.onerror=_}),Mt(c,"link",n),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=eu.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function b1(){if(Ts===null)throw Error(r(475));var e=Ts;return e.stylesheets&&e.count===0&&Kc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Kc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function eu(){if(this.count--,this.count===0){if(this.stylesheets)Kc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var tu=null;function Kc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,tu=new Map,t.forEach(S1,e),tu=null,eu.call(e))}function S1(e,t){if(!(t.state.loading&4)){var n=tu.get(e);if(n)var i=n.get(null);else{n=new Map,tu.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var y=u[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),i=y)}i&&n.set(null,i)}u=t.instance,y=u.getAttribute("data-precedence"),c=n.get(y)||i,c===i&&n.set(null,u),n.set(y,u),this.count++,i=eu.bind(this),u.addEventListener("load",i),u.addEventListener("error",i),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var Rs={$$typeof:M,Provider:null,Consumer:null,_currentValue:le,_currentValue2:le,_threadCount:0};function x1(e,t,n,i,u,c,y,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Qn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qn(0),this.hiddenUpdates=Qn(null),this.identifierPrefix=i,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function L0(e,t,n,i,u,c,y,b,_,z,X,Q){return e=new x1(e,t,n,y,b,_,z,Q),t=1,c===!0&&(t|=24),c=un(3,null,null,t),e.current=c,c.stateNode=e,t=jo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:n,cache:t},Mo(c),e}function z0(e){return e?(e=Wa,e):Wa}function k0(e,t,n,i,u,c){u=z0(u),i.context===null?i.context=u:i.pendingContext=u,i=Yl(t),i.payload={element:n},c=c===void 0?null:c,c!==null&&(i.callback=c),n=$l(e,i,t),n!==null&&(hn(n,e,t),ls(n,e,t))}function B0(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Jc(e,t){B0(e,t),(e=e.alternate)&&B0(e,t)}function H0(e){if(e.tag===13){var t=Pa(e,67108864);t!==null&&hn(t,e,67108864),Jc(e,67108864)}}var nu=!0;function E1(e,t,n,i){var u=q.T;q.T=null;var c=P.p;try{P.p=2,Pc(e,t,n,i)}finally{P.p=c,q.T=u}}function w1(e,t,n,i){var u=q.T;q.T=null;var c=P.p;try{P.p=8,Pc(e,t,n,i)}finally{P.p=c,q.T=u}}function Pc(e,t,n,i){if(nu){var u=Wc(i);if(u===null)Bc(e,t,i,lu,n),V0(e,i);else if(_1(u,e,t,n,i))i.stopPropagation();else if(V0(e,i),t&4&&-1<A1.indexOf(e)){for(;u!==null;){var c=kl(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=Xt(c.pendingLanes);if(y!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;y;){var _=1<<31-be(y);b.entanglements[1]|=_,y&=~_}el(c),(Ve&6)===0&&(qr=ut()+500,Ss(0))}}break;case 13:b=Pa(c,2),b!==null&&hn(b,c,2),Fr(),Jc(c,2)}if(c=Wc(i),c===null&&Bc(e,t,i,lu,n),c===u)break;u=c}u!==null&&i.stopPropagation()}else Bc(e,t,i,null,n)}}function Wc(e){return e=no(e),Ic(e)}var lu=null;function Ic(e){if(lu=null,e=sn(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lu=e,null}function q0(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(jn()){case Ye:return 2;case hl:return 8;case ln:case O:return 32;case k:return 268435456;default:return 32}default:return 32}}var ef=!1,la=null,aa=null,ia=null,Os=new Map,Cs=new Map,sa=[],A1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function V0(e,t){switch(e){case"focusin":case"focusout":la=null;break;case"dragenter":case"dragleave":aa=null;break;case"mouseover":case"mouseout":ia=null;break;case"pointerover":case"pointerout":Os.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Cs.delete(t.pointerId)}}function js(e,t,n,i,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:c,targetContainers:[u]},t!==null&&(t=kl(t),t!==null&&H0(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function _1(e,t,n,i,u){switch(t){case"focusin":return la=js(la,e,t,n,i,u),!0;case"dragenter":return aa=js(aa,e,t,n,i,u),!0;case"mouseover":return ia=js(ia,e,t,n,i,u),!0;case"pointerover":var c=u.pointerId;return Os.set(c,js(Os.get(c)||null,e,t,n,i,u)),!0;case"gotpointercapture":return c=u.pointerId,Cs.set(c,js(Cs.get(c)||null,e,t,n,i,u)),!0}return!1}function F0(e){var t=sn(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,yl(e.priority,function(){if(n.tag===13){var i=dn();i=Se(i);var u=Pa(n,i);u!==null&&hn(u,n,i),Jc(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function au(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wc(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);to=i,n.target.dispatchEvent(i),to=null}else return t=kl(n),t!==null&&H0(t),e.blockedOn=n,!1;t.shift()}return!0}function Y0(e,t,n){au(e)&&n.delete(t)}function T1(){ef=!1,la!==null&&au(la)&&(la=null),aa!==null&&au(aa)&&(aa=null),ia!==null&&au(ia)&&(ia=null),Os.forEach(Y0),Cs.forEach(Y0)}function iu(e,t){e.blockedOn===t&&(e.blockedOn=null,ef||(ef=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,T1)))}var su=null;function $0(e){su!==e&&(su=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){su===e&&(su=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],u=e[t+2];if(typeof i!="function"){if(Ic(i||n)===null)continue;break}var c=kl(n);c!==null&&(e.splice(t,3),t-=3,Io(c,{pending:!0,data:u,method:n.method,action:i},i,u))}}))}function Ds(e){function t(_){return iu(_,e)}la!==null&&iu(la,e),aa!==null&&iu(aa,e),ia!==null&&iu(ia,e),Os.forEach(t),Cs.forEach(t);for(var n=0;n<sa.length;n++){var i=sa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<sa.length&&(n=sa[0],n.blockedOn===null);)F0(n),n.blockedOn===null&&sa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var u=n[i],c=n[i+1],y=u[ye]||null;if(typeof c=="function")y||$0(n);else if(y){var b=null;if(c&&c.hasAttribute("formAction")){if(u=c,y=c[ye]||null)b=y.formAction;else if(Ic(u)!==null)continue}else b=y.action;typeof b=="function"?n[i+1]=b:(n.splice(i,3),i-=3),$0(n)}}}function tf(e){this._internalRoot=e}ru.prototype.render=tf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var n=t.current,i=dn();k0(n,i,e,t,null,null)},ru.prototype.unmount=tf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;k0(e.current,2,null,e,null,null),Fr(),t[Ie]=null}};function ru(e){this._internalRoot=e}ru.prototype.unstable_scheduleHydration=function(e){if(e){var t=Lt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sa.length&&t!==0&&t<sa[n].priority;n++);sa.splice(n,0,e),n===0&&F0(e)}};var G0=a.version;if(G0!=="19.1.0")throw Error(r(527,G0,"19.1.0"));P.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var R1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:q,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var uu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!uu.isDisabled&&uu.supportsFiber)try{ee=uu.inject(R1),J=uu}catch{}}return Us.createRoot=function(e,t){if(!o(e))throw Error(r(299));var n=!1,i="",u=rm,c=um,y=om,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(y=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=L0(e,1,!1,null,null,n,i,u,c,y,b,null),e[Ie]=t.current,kc(e),new tf(t)},Us.hydrateRoot=function(e,t,n){if(!o(e))throw Error(r(299));var i=!1,u="",c=rm,y=um,b=om,_=null,z=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(_=n.unstable_transitionCallbacks),n.formState!==void 0&&(z=n.formState)),t=L0(e,1,!0,t,n??null,i,u,c,y,b,_,z),t.context=z0(null),n=t.current,i=dn(),i=Se(i),u=Yl(i),u.callback=null,$l(n,u,i),n=i,t.current.lanes=n,Nn(t,n),el(t),e[Ie]=t.current,kc(e),new ru(t)},Us.version="19.1.0",Us}var tp;function k1(){if(tp)return af.exports;tp=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),af.exports=z1(),af.exports}var B1=k1(),Ms={},np;function H1(){if(np)return Ms;np=1,Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.parse=d,Ms.serialize=m;const l=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,a=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const x=function(){};return x.prototype=Object.create(null),x})();function d(x,T){const E=new f,D=x.length;if(D<2)return E;const C=(T==null?void 0:T.decode)||g;let w=0;do{const j=x.indexOf("=",w);if(j===-1)break;const M=x.indexOf(";",w),G=M===-1?D:M;if(j>G){w=x.lastIndexOf(";",j-1)+1;continue}const H=h(x,w,j),te=p(x,j,H),I=x.slice(H,te);if(E[I]===void 0){let K=h(x,j+1,G),ne=p(x,G,K);const xe=C(x.slice(K,ne));E[I]=xe}w=G+1}while(w<D);return E}function h(x,T,E){do{const D=x.charCodeAt(T);if(D!==32&&D!==9)return T}while(++T<E);return E}function p(x,T,E){for(;T>E;){const D=x.charCodeAt(--T);if(D!==32&&D!==9)return T+1}return E}function m(x,T,E){const D=(E==null?void 0:E.encode)||encodeURIComponent;if(!l.test(x))throw new TypeError(`argument name is invalid: ${x}`);const C=D(T);if(!a.test(C))throw new TypeError(`argument val is invalid: ${T}`);let w=x+"="+C;if(!E)return w;if(E.maxAge!==void 0){if(!Number.isInteger(E.maxAge))throw new TypeError(`option maxAge is invalid: ${E.maxAge}`);w+="; Max-Age="+E.maxAge}if(E.domain){if(!s.test(E.domain))throw new TypeError(`option domain is invalid: ${E.domain}`);w+="; Domain="+E.domain}if(E.path){if(!r.test(E.path))throw new TypeError(`option path is invalid: ${E.path}`);w+="; Path="+E.path}if(E.expires){if(!S(E.expires)||!Number.isFinite(E.expires.valueOf()))throw new TypeError(`option expires is invalid: ${E.expires}`);w+="; Expires="+E.expires.toUTCString()}if(E.httpOnly&&(w+="; HttpOnly"),E.secure&&(w+="; Secure"),E.partitioned&&(w+="; Partitioned"),E.priority)switch(typeof E.priority=="string"?E.priority.toLowerCase():void 0){case"low":w+="; Priority=Low";break;case"medium":w+="; Priority=Medium";break;case"high":w+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${E.priority}`)}if(E.sameSite)switch(typeof E.sameSite=="string"?E.sameSite.toLowerCase():E.sameSite){case!0:case"strict":w+="; SameSite=Strict";break;case"lax":w+="; SameSite=Lax";break;case"none":w+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${E.sameSite}`)}return w}function g(x){if(x.indexOf("%")===-1)return x;try{return decodeURIComponent(x)}catch{return x}}function S(x){return o.call(x)==="[object Date]"}return Ms}H1();var lp="popstate";function q1(l={}){function a(r,o){let{pathname:f,search:d,hash:h}=r.location;return _f("",{pathname:f,search:d,hash:h},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function s(r,o){return typeof o=="string"?o:Fs(o)}return F1(a,s,null,l)}function lt(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}function qn(l,a){if(!l){typeof console<"u"&&console.warn(a);try{throw new Error(a)}catch{}}}function V1(){return Math.random().toString(36).substring(2,10)}function ap(l,a){return{usr:l.state,key:l.key,idx:a}}function _f(l,a,s=null,r){return{pathname:typeof l=="string"?l:l.pathname,search:"",hash:"",...typeof a=="string"?ji(a):a,state:s,key:a&&a.key||r||V1()}}function Fs({pathname:l="/",search:a="",hash:s=""}){return a&&a!=="?"&&(l+=a.charAt(0)==="?"?a:"?"+a),s&&s!=="#"&&(l+=s.charAt(0)==="#"?s:"#"+s),l}function ji(l){let a={};if(l){let s=l.indexOf("#");s>=0&&(a.hash=l.substring(s),l=l.substring(0,s));let r=l.indexOf("?");r>=0&&(a.search=l.substring(r),l=l.substring(0,r)),l&&(a.pathname=l)}return a}function F1(l,a,s,r={}){let{window:o=document.defaultView,v5Compat:f=!1}=r,d=o.history,h="POP",p=null,m=g();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function g(){return(d.state||{idx:null}).idx}function S(){h="POP";let C=g(),w=C==null?null:C-m;m=C,p&&p({action:h,location:D.location,delta:w})}function x(C,w){h="PUSH";let j=_f(D.location,C,w);m=g()+1;let M=ap(j,m),G=D.createHref(j);try{d.pushState(M,"",G)}catch(H){if(H instanceof DOMException&&H.name==="DataCloneError")throw H;o.location.assign(G)}f&&p&&p({action:h,location:D.location,delta:1})}function T(C,w){h="REPLACE";let j=_f(D.location,C,w);m=g();let M=ap(j,m),G=D.createHref(j);d.replaceState(M,"",G),f&&p&&p({action:h,location:D.location,delta:0})}function E(C){let w=o.location.origin!=="null"?o.location.origin:o.location.href,j=typeof C=="string"?C:Fs(C);return j=j.replace(/ $/,"%20"),lt(w,`No window.location.(origin|href) available to create URL for href: ${j}`),new URL(j,w)}let D={get action(){return h},get location(){return l(o,d)},listen(C){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(lp,S),p=C,()=>{o.removeEventListener(lp,S),p=null}},createHref(C){return a(o,C)},createURL:E,encodeLocation(C){let w=E(C);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:x,replace:T,go(C){return d.go(C)}};return D}function ly(l,a,s="/"){return Y1(l,a,s,!1)}function Y1(l,a,s,r){let o=typeof a=="string"?ji(a):a,f=zl(o.pathname||"/",s);if(f==null)return null;let d=ay(l);$1(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=tb(f);h=I1(d[p],m,r)}return h}function ay(l,a=[],s=[],r=""){let o=(f,d,h)=>{let p={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};p.relativePath.startsWith("/")&&(lt(p.relativePath.startsWith(r),`Absolute route path "${p.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(r.length));let m=Ll([r,p.relativePath]),g=s.concat(p);f.children&&f.children.length>0&&(lt(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),ay(f.children,a,g,m)),!(f.path==null&&!f.index)&&a.push({path:m,score:P1(m,f.index),routesMeta:g})};return l.forEach((f,d)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))o(f,d);else for(let p of iy(f.path))o(f,d,p)}),a}function iy(l){let a=l.split("/");if(a.length===0)return[];let[s,...r]=a,o=s.endsWith("?"),f=s.replace(/\?$/,"");if(r.length===0)return o?[f,""]:[f];let d=iy(r.join("/")),h=[];return h.push(...d.map(p=>p===""?f:[f,p].join("/"))),o&&h.push(...d),h.map(p=>l.startsWith("/")&&p===""?"/":p)}function $1(l){l.sort((a,s)=>a.score!==s.score?s.score-a.score:W1(a.routesMeta.map(r=>r.childrenIndex),s.routesMeta.map(r=>r.childrenIndex)))}var G1=/^:[\w-]+$/,X1=3,Z1=2,Q1=1,K1=10,J1=-2,ip=l=>l==="*";function P1(l,a){let s=l.split("/"),r=s.length;return s.some(ip)&&(r+=J1),a&&(r+=Z1),s.filter(o=>!ip(o)).reduce((o,f)=>o+(G1.test(f)?X1:f===""?Q1:K1),r)}function W1(l,a){return l.length===a.length&&l.slice(0,-1).every((r,o)=>r===a[o])?l[l.length-1]-a[a.length-1]:0}function I1(l,a,s=!1){let{routesMeta:r}=l,o={},f="/",d=[];for(let h=0;h<r.length;++h){let p=r[h],m=h===r.length-1,g=f==="/"?a:a.slice(f.length)||"/",S=xu({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},g),x=p.route;if(!S&&m&&s&&!r[r.length-1].route.index&&(S=xu({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},g)),!S)return null;Object.assign(o,S.params),d.push({params:o,pathname:Ll([f,S.pathname]),pathnameBase:ib(Ll([f,S.pathnameBase])),route:x}),S.pathnameBase!=="/"&&(f=Ll([f,S.pathnameBase]))}return d}function xu(l,a){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[s,r]=eb(l.path,l.caseSensitive,l.end),o=a.match(s);if(!o)return null;let f=o[0],d=f.replace(/(.)\/+$/,"$1"),h=o.slice(1);return{params:r.reduce((m,{paramName:g,isOptional:S},x)=>{if(g==="*"){let E=h[x]||"";d=f.slice(0,f.length-E.length).replace(/(.)\/+$/,"$1")}const T=h[x];return S&&!T?m[g]=void 0:m[g]=(T||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:l}}function eb(l,a=!1,s=!0){qn(l==="*"||!l.endsWith("*")||l.endsWith("/*"),`Route path "${l}" will be treated as if it were "${l.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${l.replace(/\*$/,"/*")}".`);let r=[],o="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(r.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(r.push({paramName:"*"}),o+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?o+="\\/*$":l!==""&&l!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,a?void 0:"i"),r]}function tb(l){try{return l.split("/").map(a=>decodeURIComponent(a).replace(/\//g,"%2F")).join("/")}catch(a){return qn(!1,`The URL path "${l}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${a}).`),l}}function zl(l,a){if(a==="/")return l;if(!l.toLowerCase().startsWith(a.toLowerCase()))return null;let s=a.endsWith("/")?a.length-1:a.length,r=l.charAt(s);return r&&r!=="/"?null:l.slice(s)||"/"}function nb(l,a="/"){let{pathname:s,search:r="",hash:o=""}=typeof l=="string"?ji(l):l;return{pathname:s?s.startsWith("/")?s:lb(s,a):a,search:sb(r),hash:rb(o)}}function lb(l,a){let s=a.replace(/\/+$/,"").split("/");return l.split("/").forEach(o=>{o===".."?s.length>1&&s.pop():o!=="."&&s.push(o)}),s.length>1?s.join("/"):"/"}function of(l,a,s,r){return`Cannot include a '${l}' character in a manually specified \`to.${a}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function ab(l){return l.filter((a,s)=>s===0||a.route.path&&a.route.path.length>0)}function Yf(l){let a=ab(l);return a.map((s,r)=>r===a.length-1?s.pathname:s.pathnameBase)}function $f(l,a,s,r=!1){let o;typeof l=="string"?o=ji(l):(o={...l},lt(!o.pathname||!o.pathname.includes("?"),of("?","pathname","search",o)),lt(!o.pathname||!o.pathname.includes("#"),of("#","pathname","hash",o)),lt(!o.search||!o.search.includes("#"),of("#","search","hash",o)));let f=l===""||o.pathname==="",d=f?"/":o.pathname,h;if(d==null)h=s;else{let S=a.length-1;if(!r&&d.startsWith("..")){let x=d.split("/");for(;x[0]==="..";)x.shift(),S-=1;o.pathname=x.join("/")}h=S>=0?a[S]:"/"}let p=nb(o,h),m=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&s.endsWith("/");return!p.pathname.endsWith("/")&&(m||g)&&(p.pathname+="/"),p}var Ll=l=>l.join("/").replace(/\/\/+/g,"/"),ib=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),sb=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,rb=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function ub(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}var sy=["POST","PUT","PATCH","DELETE"];new Set(sy);var ob=["GET",...sy];new Set(ob);var Di=A.createContext(null);Di.displayName="DataRouter";var Mu=A.createContext(null);Mu.displayName="DataRouterState";var ry=A.createContext({isTransitioning:!1});ry.displayName="ViewTransition";var cb=A.createContext(new Map);cb.displayName="Fetchers";var fb=A.createContext(null);fb.displayName="Await";var Yn=A.createContext(null);Yn.displayName="Navigation";var Gs=A.createContext(null);Gs.displayName="Location";var Cn=A.createContext({outlet:null,matches:[],isDataRoute:!1});Cn.displayName="Route";var Gf=A.createContext(null);Gf.displayName="RouteError";function db(l,{relative:a}={}){lt(Ni(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:r}=A.useContext(Yn),{hash:o,pathname:f,search:d}=Xs(l,{relative:a}),h=f;return s!=="/"&&(h=f==="/"?s:Ll([s,f])),r.createHref({pathname:h,search:d,hash:o})}function Ni(){return A.useContext(Gs)!=null}function cl(){return lt(Ni(),"useLocation() may be used only in the context of a <Router> component."),A.useContext(Gs).location}var uy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function oy(l){A.useContext(Yn).static||A.useLayoutEffect(l)}function $n(){let{isDataRoute:l}=A.useContext(Cn);return l?Ob():hb()}function hb(){lt(Ni(),"useNavigate() may be used only in the context of a <Router> component.");let l=A.useContext(Di),{basename:a,navigator:s}=A.useContext(Yn),{matches:r}=A.useContext(Cn),{pathname:o}=cl(),f=JSON.stringify(Yf(r)),d=A.useRef(!1);return oy(()=>{d.current=!0}),A.useCallback((p,m={})=>{if(qn(d.current,uy),!d.current)return;if(typeof p=="number"){s.go(p);return}let g=$f(p,JSON.parse(f),o,m.relative==="path");l==null&&a!=="/"&&(g.pathname=g.pathname==="/"?a:Ll([a,g.pathname])),(m.replace?s.replace:s.push)(g,m.state,m)},[a,s,f,o,l])}var mb=A.createContext(null);function pb(l){let a=A.useContext(Cn).outlet;return a&&A.createElement(mb.Provider,{value:l},a)}function yb(){let{matches:l}=A.useContext(Cn),a=l[l.length-1];return a?a.params:{}}function Xs(l,{relative:a}={}){let{matches:s}=A.useContext(Cn),{pathname:r}=cl(),o=JSON.stringify(Yf(s));return A.useMemo(()=>$f(l,JSON.parse(o),r,a==="path"),[l,o,r,a])}function gb(l,a){return cy(l,a)}function cy(l,a,s,r){var j;lt(Ni(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:f}=A.useContext(Yn),{matches:d}=A.useContext(Cn),h=d[d.length-1],p=h?h.params:{},m=h?h.pathname:"/",g=h?h.pathnameBase:"/",S=h&&h.route;{let M=S&&S.path||"";fy(m,!S||M.endsWith("*")||M.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${M}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${M}"> to <Route path="${M==="/"?"*":`${M}/*`}">.`)}let x=cl(),T;if(a){let M=typeof a=="string"?ji(a):a;lt(g==="/"||((j=M.pathname)==null?void 0:j.startsWith(g)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${M.pathname}" was given in the \`location\` prop.`),T=M}else T=x;let E=T.pathname||"/",D=E;if(g!=="/"){let M=g.replace(/^\//,"").split("/");D="/"+E.replace(/^\//,"").split("/").slice(M.length).join("/")}let C=!f&&s&&s.matches&&s.matches.length>0?s.matches:ly(l,{pathname:D});qn(S||C!=null,`No routes matched location "${T.pathname}${T.search}${T.hash}" `),qn(C==null||C[C.length-1].route.element!==void 0||C[C.length-1].route.Component!==void 0||C[C.length-1].route.lazy!==void 0,`Matched leaf route at location "${T.pathname}${T.search}${T.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=Eb(C&&C.map(M=>Object.assign({},M,{params:Object.assign({},p,M.params),pathname:Ll([g,o.encodeLocation?o.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?g:Ll([g,o.encodeLocation?o.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),d,s,r);return a&&w?A.createElement(Gs.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...T},navigationType:"POP"}},w):w}function vb(){let l=Rb(),a=ub(l)?`${l.status} ${l.statusText}`:l instanceof Error?l.message:JSON.stringify(l),s=l instanceof Error?l.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},f={padding:"2px 4px",backgroundColor:r},d=null;return console.error("Error handled by React Router default ErrorBoundary:",l),d=A.createElement(A.Fragment,null,A.createElement("p",null,"💿 Hey developer 👋"),A.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",A.createElement("code",{style:f},"ErrorBoundary")," or"," ",A.createElement("code",{style:f},"errorElement")," prop on your route.")),A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},a),s?A.createElement("pre",{style:o},s):null,d)}var bb=A.createElement(vb,null),Sb=class extends A.Component{constructor(l){super(l),this.state={location:l.location,revalidation:l.revalidation,error:l.error}}static getDerivedStateFromError(l){return{error:l}}static getDerivedStateFromProps(l,a){return a.location!==l.location||a.revalidation!=="idle"&&l.revalidation==="idle"?{error:l.error,location:l.location,revalidation:l.revalidation}:{error:l.error!==void 0?l.error:a.error,location:a.location,revalidation:l.revalidation||a.revalidation}}componentDidCatch(l,a){console.error("React Router caught the following error during render",l,a)}render(){return this.state.error!==void 0?A.createElement(Cn.Provider,{value:this.props.routeContext},A.createElement(Gf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function xb({routeContext:l,match:a,children:s}){let r=A.useContext(Di);return r&&r.static&&r.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=a.route.id),A.createElement(Cn.Provider,{value:l},s)}function Eb(l,a=[],s=null,r=null){if(l==null){if(!s)return null;if(s.errors)l=s.matches;else if(a.length===0&&!s.initialized&&s.matches.length>0)l=s.matches;else return null}let o=l,f=s==null?void 0:s.errors;if(f!=null){let p=o.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);lt(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let d=!1,h=-1;if(s)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:g,errors:S}=s,x=m.route.loader&&!g.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||x){d=!0,h>=0?o=o.slice(0,h+1):o=[o[0]];break}}}return o.reduceRight((p,m,g)=>{let S,x=!1,T=null,E=null;s&&(S=f&&m.route.id?f[m.route.id]:void 0,T=m.route.errorElement||bb,d&&(h<0&&g===0?(fy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,E=null):h===g&&(x=!0,E=m.route.hydrateFallbackElement||null)));let D=a.concat(o.slice(0,g+1)),C=()=>{let w;return S?w=T:x?w=E:m.route.Component?w=A.createElement(m.route.Component,null):m.route.element?w=m.route.element:w=p,A.createElement(xb,{match:m,routeContext:{outlet:p,matches:D,isDataRoute:s!=null},children:w})};return s&&(m.route.ErrorBoundary||m.route.errorElement||g===0)?A.createElement(Sb,{location:s.location,revalidation:s.revalidation,component:T,error:S,children:C(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):C()},null)}function Xf(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function wb(l){let a=A.useContext(Di);return lt(a,Xf(l)),a}function Ab(l){let a=A.useContext(Mu);return lt(a,Xf(l)),a}function _b(l){let a=A.useContext(Cn);return lt(a,Xf(l)),a}function Zf(l){let a=_b(l),s=a.matches[a.matches.length-1];return lt(s.route.id,`${l} can only be used on routes that contain a unique "id"`),s.route.id}function Tb(){return Zf("useRouteId")}function Rb(){var r;let l=A.useContext(Gf),a=Ab("useRouteError"),s=Zf("useRouteError");return l!==void 0?l:(r=a.errors)==null?void 0:r[s]}function Ob(){let{router:l}=wb("useNavigate"),a=Zf("useNavigate"),s=A.useRef(!1);return oy(()=>{s.current=!0}),A.useCallback(async(o,f={})=>{qn(s.current,uy),s.current&&(typeof o=="number"?l.navigate(o):await l.navigate(o,{fromRouteId:a,...f}))},[l,a])}var sp={};function fy(l,a,s){!a&&!sp[l]&&(sp[l]=!0,qn(!1,s))}A.memo(Cb);function Cb({routes:l,future:a,state:s}){return cy(l,void 0,s,a)}function cf({to:l,replace:a,state:s,relative:r}){lt(Ni(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=A.useContext(Yn);qn(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=A.useContext(Cn),{pathname:d}=cl(),h=$n(),p=$f(l,Yf(f),d,r==="path"),m=JSON.stringify(p);return A.useEffect(()=>{h(JSON.parse(m),{replace:a,state:s,relative:r})},[h,m,r,a,s]),null}function jb(l){return pb(l.context)}function nl(l){lt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Db({basename:l="/",children:a=null,location:s,navigationType:r="POP",navigator:o,static:f=!1}){lt(!Ni(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=l.replace(/^\/*/,"/"),h=A.useMemo(()=>({basename:d,navigator:o,static:f,future:{}}),[d,o,f]);typeof s=="string"&&(s=ji(s));let{pathname:p="/",search:m="",hash:g="",state:S=null,key:x="default"}=s,T=A.useMemo(()=>{let E=zl(p,d);return E==null?null:{location:{pathname:E,search:m,hash:g,state:S,key:x},navigationType:r}},[d,p,m,g,S,x,r]);return qn(T!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${g}" because it does not start with the basename, so the <Router> won't render anything.`),T==null?null:A.createElement(Yn.Provider,{value:h},A.createElement(Gs.Provider,{children:a,value:T}))}function Nb({children:l,location:a}){return gb(Tf(l),a)}function Tf(l,a=[]){let s=[];return A.Children.forEach(l,(r,o)=>{if(!A.isValidElement(r))return;let f=[...a,o];if(r.type===A.Fragment){s.push.apply(s,Tf(r.props.children,f));return}lt(r.type===nl,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),lt(!r.props.index||!r.props.children,"An index route cannot have child routes.");let d={id:r.props.id||f.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(d.children=Tf(r.props.children,f)),s.push(d)}),s}var hu="get",mu="application/x-www-form-urlencoded";function Lu(l){return l!=null&&typeof l.tagName=="string"}function Ub(l){return Lu(l)&&l.tagName.toLowerCase()==="button"}function Mb(l){return Lu(l)&&l.tagName.toLowerCase()==="form"}function Lb(l){return Lu(l)&&l.tagName.toLowerCase()==="input"}function zb(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function kb(l,a){return l.button===0&&(!a||a==="_self")&&!zb(l)}var ou=null;function Bb(){if(ou===null)try{new FormData(document.createElement("form"),0),ou=!1}catch{ou=!0}return ou}var Hb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ff(l){return l!=null&&!Hb.has(l)?(qn(!1,`"${l}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${mu}"`),null):l}function qb(l,a){let s,r,o,f,d;if(Mb(l)){let h=l.getAttribute("action");r=h?zl(h,a):null,s=l.getAttribute("method")||hu,o=ff(l.getAttribute("enctype"))||mu,f=new FormData(l)}else if(Ub(l)||Lb(l)&&(l.type==="submit"||l.type==="image")){let h=l.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=l.getAttribute("formaction")||h.getAttribute("action");if(r=p?zl(p,a):null,s=l.getAttribute("formmethod")||h.getAttribute("method")||hu,o=ff(l.getAttribute("formenctype"))||ff(h.getAttribute("enctype"))||mu,f=new FormData(h,l),!Bb()){let{name:m,type:g,value:S}=l;if(g==="image"){let x=m?`${m}.`:"";f.append(`${x}x`,"0"),f.append(`${x}y`,"0")}else m&&f.append(m,S)}}else{if(Lu(l))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=hu,r=null,o=mu,d=l}return f&&o==="text/plain"&&(d=f,f=void 0),{action:r,method:s.toLowerCase(),encType:o,formData:f,body:d}}function Qf(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}async function Vb(l,a){if(l.id in a)return a[l.id];try{let s=await import(l.module);return a[l.id]=s,s}catch(s){return console.error(`Error loading route module \`${l.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Fb(l){return l==null?!1:l.href==null?l.rel==="preload"&&typeof l.imageSrcSet=="string"&&typeof l.imageSizes=="string":typeof l.rel=="string"&&typeof l.href=="string"}async function Yb(l,a,s){let r=await Promise.all(l.map(async o=>{let f=a.routes[o.route.id];if(f){let d=await Vb(f,s);return d.links?d.links():[]}return[]}));return Zb(r.flat(1).filter(Fb).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function rp(l,a,s,r,o,f){let d=(p,m)=>s[m]?p.route.id!==s[m].route.id:!0,h=(p,m)=>{var g;return s[m].pathname!==p.pathname||((g=s[m].route.path)==null?void 0:g.endsWith("*"))&&s[m].params["*"]!==p.params["*"]};return f==="assets"?a.filter((p,m)=>d(p,m)||h(p,m)):f==="data"?a.filter((p,m)=>{var S;let g=r.routes[p.route.id];if(!g||!g.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let x=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((S=s[0])==null?void 0:S.params)||{},nextUrl:new URL(l,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof x=="boolean")return x}return!0}):[]}function $b(l,a,{includeHydrateFallback:s}={}){return Gb(l.map(r=>{let o=a.routes[r.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),s&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function Gb(l){return[...new Set(l)]}function Xb(l){let a={},s=Object.keys(l).sort();for(let r of s)a[r]=l[r];return a}function Zb(l,a){let s=new Set;return new Set(a),l.reduce((r,o)=>{let f=JSON.stringify(Xb(o));return s.has(f)||(s.add(f),r.push({key:f,link:o})),r},[])}var Qb=new Set([100,101,204,205]);function Kb(l,a){let s=typeof l=="string"?new URL(l,typeof window>"u"?"server://singlefetch/":window.location.origin):l;return s.pathname==="/"?s.pathname="_root.data":a&&zl(s.pathname,a)==="/"?s.pathname=`${a.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function dy(){let l=A.useContext(Di);return Qf(l,"You must render this element inside a <DataRouterContext.Provider> element"),l}function Jb(){let l=A.useContext(Mu);return Qf(l,"You must render this element inside a <DataRouterStateContext.Provider> element"),l}var Kf=A.createContext(void 0);Kf.displayName="FrameworkContext";function hy(){let l=A.useContext(Kf);return Qf(l,"You must render this element inside a <HydratedRouter> element"),l}function Pb(l,a){let s=A.useContext(Kf),[r,o]=A.useState(!1),[f,d]=A.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:g,onTouchStart:S}=a,x=A.useRef(null);A.useEffect(()=>{if(l==="render"&&d(!0),l==="viewport"){let D=w=>{w.forEach(j=>{d(j.isIntersecting)})},C=new IntersectionObserver(D,{threshold:.5});return x.current&&C.observe(x.current),()=>{C.disconnect()}}},[l]),A.useEffect(()=>{if(r){let D=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(D)}}},[r]);let T=()=>{o(!0)},E=()=>{o(!1),d(!1)};return s?l!=="intent"?[f,x,{}]:[f,x,{onFocus:Ls(h,T),onBlur:Ls(p,E),onMouseEnter:Ls(m,T),onMouseLeave:Ls(g,E),onTouchStart:Ls(S,T)}]:[!1,x,{}]}function Ls(l,a){return s=>{l&&l(s),s.defaultPrevented||a(s)}}function Wb({page:l,...a}){let{router:s}=dy(),r=A.useMemo(()=>ly(s.routes,l,s.basename),[s.routes,l,s.basename]);return r?A.createElement(e2,{page:l,matches:r,...a}):null}function Ib(l){let{manifest:a,routeModules:s}=hy(),[r,o]=A.useState([]);return A.useEffect(()=>{let f=!1;return Yb(l,a,s).then(d=>{f||o(d)}),()=>{f=!0}},[l,a,s]),r}function e2({page:l,matches:a,...s}){let r=cl(),{manifest:o,routeModules:f}=hy(),{basename:d}=dy(),{loaderData:h,matches:p}=Jb(),m=A.useMemo(()=>rp(l,a,p,o,r,"data"),[l,a,p,o,r]),g=A.useMemo(()=>rp(l,a,p,o,r,"assets"),[l,a,p,o,r]),S=A.useMemo(()=>{if(l===r.pathname+r.search+r.hash)return[];let E=new Set,D=!1;if(a.forEach(w=>{var M;let j=o.routes[w.route.id];!j||!j.hasLoader||(!m.some(G=>G.route.id===w.route.id)&&w.route.id in h&&((M=f[w.route.id])!=null&&M.shouldRevalidate)||j.hasClientLoader?D=!0:E.add(w.route.id))}),E.size===0)return[];let C=Kb(l,d);return D&&E.size>0&&C.searchParams.set("_routes",a.filter(w=>E.has(w.route.id)).map(w=>w.route.id).join(",")),[C.pathname+C.search]},[d,h,r,o,m,a,l,f]),x=A.useMemo(()=>$b(g,o),[g,o]),T=Ib(g);return A.createElement(A.Fragment,null,S.map(E=>A.createElement("link",{key:E,rel:"prefetch",as:"fetch",href:E,...s})),x.map(E=>A.createElement("link",{key:E,rel:"modulepreload",href:E,...s})),T.map(({key:E,link:D})=>A.createElement("link",{key:E,...D})))}function t2(...l){return a=>{l.forEach(s=>{typeof s=="function"?s(a):s!=null&&(s.current=a)})}}var my=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{my&&(window.__reactRouterVersion="7.5.2")}catch{}function n2({basename:l,children:a,window:s}){let r=A.useRef();r.current==null&&(r.current=q1({window:s,v5Compat:!0}));let o=r.current,[f,d]=A.useState({action:o.action,location:o.location}),h=A.useCallback(p=>{A.startTransition(()=>d(p))},[d]);return A.useLayoutEffect(()=>o.listen(h),[o,h]),A.createElement(Db,{basename:l,children:a,location:f.location,navigationType:f.action,navigator:o})}var py=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yy=A.forwardRef(function({onClick:a,discover:s="render",prefetch:r="none",relative:o,reloadDocument:f,replace:d,state:h,target:p,to:m,preventScrollReset:g,viewTransition:S,...x},T){let{basename:E}=A.useContext(Yn),D=typeof m=="string"&&py.test(m),C,w=!1;if(typeof m=="string"&&D&&(C=m,my))try{let ne=new URL(window.location.href),xe=m.startsWith("//")?new URL(ne.protocol+m):new URL(m),ce=zl(xe.pathname,E);xe.origin===ne.origin&&ce!=null?m=ce+xe.search+xe.hash:w=!0}catch{qn(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let j=db(m,{relative:o}),[M,G,H]=Pb(r,x),te=s2(m,{replace:d,state:h,target:p,preventScrollReset:g,relative:o,viewTransition:S});function I(ne){a&&a(ne),ne.defaultPrevented||te(ne)}let K=A.createElement("a",{...x,...H,href:C||j,onClick:w||f?a:I,ref:t2(T,G),target:p,"data-discover":!D&&s==="render"?"true":void 0});return M&&!D?A.createElement(A.Fragment,null,K,A.createElement(Wb,{page:j})):K});yy.displayName="Link";var l2=A.forwardRef(function({"aria-current":a="page",caseSensitive:s=!1,className:r="",end:o=!1,style:f,to:d,viewTransition:h,children:p,...m},g){let S=Xs(d,{relative:m.relative}),x=cl(),T=A.useContext(Mu),{navigator:E,basename:D}=A.useContext(Yn),C=T!=null&&f2(S)&&h===!0,w=E.encodeLocation?E.encodeLocation(S).pathname:S.pathname,j=x.pathname,M=T&&T.navigation&&T.navigation.location?T.navigation.location.pathname:null;s||(j=j.toLowerCase(),M=M?M.toLowerCase():null,w=w.toLowerCase()),M&&D&&(M=zl(M,D)||M);const G=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let H=j===w||!o&&j.startsWith(w)&&j.charAt(G)==="/",te=M!=null&&(M===w||!o&&M.startsWith(w)&&M.charAt(w.length)==="/"),I={isActive:H,isPending:te,isTransitioning:C},K=H?a:void 0,ne;typeof r=="function"?ne=r(I):ne=[r,H?"active":null,te?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let xe=typeof f=="function"?f(I):f;return A.createElement(yy,{...m,"aria-current":K,className:ne,ref:g,style:xe,to:d,viewTransition:h},typeof p=="function"?p(I):p)});l2.displayName="NavLink";var a2=A.forwardRef(({discover:l="render",fetcherKey:a,navigate:s,reloadDocument:r,replace:o,state:f,method:d=hu,action:h,onSubmit:p,relative:m,preventScrollReset:g,viewTransition:S,...x},T)=>{let E=o2(),D=c2(h,{relative:m}),C=d.toLowerCase()==="get"?"get":"post",w=typeof h=="string"&&py.test(h),j=M=>{if(p&&p(M),M.defaultPrevented)return;M.preventDefault();let G=M.nativeEvent.submitter,H=(G==null?void 0:G.getAttribute("formmethod"))||d;E(G||M.currentTarget,{fetcherKey:a,method:H,navigate:s,replace:o,state:f,relative:m,preventScrollReset:g,viewTransition:S})};return A.createElement("form",{ref:T,method:C,action:D,onSubmit:r?p:j,...x,"data-discover":!w&&l==="render"?"true":void 0})});a2.displayName="Form";function i2(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function gy(l){let a=A.useContext(Di);return lt(a,i2(l)),a}function s2(l,{target:a,replace:s,state:r,preventScrollReset:o,relative:f,viewTransition:d}={}){let h=$n(),p=cl(),m=Xs(l,{relative:f});return A.useCallback(g=>{if(kb(g,a)){g.preventDefault();let S=s!==void 0?s:Fs(p)===Fs(m);h(l,{replace:S,state:r,preventScrollReset:o,relative:f,viewTransition:d})}},[p,h,m,s,r,a,l,o,f,d])}var r2=0,u2=()=>`__${String(++r2)}__`;function o2(){let{router:l}=gy("useSubmit"),{basename:a}=A.useContext(Yn),s=Tb();return A.useCallback(async(r,o={})=>{let{action:f,method:d,encType:h,formData:p,body:m}=qb(r,a);if(o.navigate===!1){let g=o.fetcherKey||u2();await l.fetch(g,s,o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,flushSync:o.flushSync})}else await l.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,replace:o.replace,state:o.state,fromRouteId:s,flushSync:o.flushSync,viewTransition:o.viewTransition})},[l,a,s])}function c2(l,{relative:a}={}){let{basename:s}=A.useContext(Yn),r=A.useContext(Cn);lt(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),f={...Xs(l||".",{relative:a})},d=cl();if(l==null){f.search=d.search;let h=new URLSearchParams(f.search),p=h.getAll("index");if(p.some(g=>g==="")){h.delete("index"),p.filter(S=>S).forEach(S=>h.append("index",S));let g=h.toString();f.search=g?`?${g}`:""}}return(!l||l===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(f.pathname=f.pathname==="/"?s:Ll([s,f.pathname])),Fs(f)}function f2(l,a={}){let s=A.useContext(ry);lt(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=gy("useViewTransitionState"),o=Xs(l,{relative:a.relative});if(!s.isTransitioning)return!1;let f=zl(s.currentLocation.pathname,r)||s.currentLocation.pathname,d=zl(s.nextLocation.pathname,r)||s.nextLocation.pathname;return xu(o.pathname,d)!=null||xu(o.pathname,f)!=null}new TextEncoder;[...Qb];const d2="/assets/illustration1-BZ2qBqb5.png",h2="/assets/illustration2-D_JRMXuN.png",m2="/assets/illustration3-0vw7lghX.png",p2="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAhCAYAAAC803lsAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANlSURBVHgBxVc9T9tAGH7vLqm6VErExGaIkBgDVaWO4RcUtm6EXwAZOwG/oHTrBv0FpL+AZENqoWFDype3jpgNNdjX57XPcDjBoc5HH+l057vz3eP324JGYHl5fR+dEwRBzXVbHs0BueREqbR+TCSqPFZKOeg2aA5Q9gNLQgixZ005xeLi7c3N73OaMUSCxMHjknax7GDg+b6/BhW5NEPkkiSEoLrWtKm1aGB8i6ldKdUp+jXKCMcpF5QSRzj9Az/j/JNe77Jm71Gl0ttNkPgakRA1rfUVRpt4bAWB/0lK+RGEVqEiARU1KAMWFhZxvtzGua+54Z73yfMkJj1D4rDT+XlkH8AeAyJbPETL5D0sjYgEke/nl/L5/JK5b9fel8PlDbJsJYl2+0cL3dKotdXVsjMYKHgZVSBJFz2L/PCZo/Ahd2aohhYlTYDBQJ4xCR7jCx22M9ib7XVk4lADDXai+iDeN0vfpkIEIq+wV8HwXN9/UxRCG+MT28m98Lot69FjM+h2L54QzlFG5PPkBUHoZbABrxAEksdo+ja5VynJxs/e4vZ6FyPVnFki7XarhYPZfkKR4ys/m8v6I7ZzymCSz9nPZDYiRHBlPbItsNirCAkP6kHKqMYq7HYvT2jaRNhjbLeEzovWFx/BhhwzHiuNiYgMBlEk5ijpuucujzudS45D7A1Ql/wFadwYabTSpJGZiC2NIMg/+VJ4CHsD1CQKUQsNugy3PqBpExkljQSYgMcqg83EZcR22pkvdl87isZzSWlYJAzu6P4+9CqMRWqKeLFE7CgaQ6k/leQ+Uy40yLg12ml4kRTfaVIiySiKqS/RSpilh4DEtkOPIdwzCfUg7Y6xqjG1RDU+lKOo1tIVHEZJF0a9c30d2k3VtBchlQgnMNy3b1s/i9vackJTwrOqwaXVKGwzCd3E1JbpGSzu2rjY8C9IlQjbBAjxhXUzVacJgADXx5nwtmAjWQOnGKtucqZE6J7o8qcQYd2Cqu/MSgHpRFA892nKgDdtcLiPyaysvCuPJTILsDehIOdIW2cyUNFZnKmHalWTto9pjkC9WxuSiBDSpTkD0qkI+g/gSI0y4Tj+k0TbmSsRjtIwUuv/Wjd9/1WVM3jm4jkLuFiKpRDln4uHH7q5Eol8QzfxL71nftwe8Bfkx1ujnHc2/QAAAABJRU5ErkJggg==";function y2(){const l=$n();return v.jsxs("main",{className:"landing-page",children:[v.jsxs("section",{className:"left-panel",children:[v.jsxs("h1",{children:["BIZTREND",v.jsx("br",{}),"F",v.jsx("img",{src:p2,alt:"system icon"}),v.jsx("span",{children:"RECAST"})]}),v.jsx("h4",{children:"STAY AHEAD IN THE BUSINESS GAME!"}),v.jsx("br",{}),v.jsx("br",{}),v.jsx("p",{children:"Ready to explore?"}),v.jsx("p",{children:"Select your user type and we'll guide the way!"}),v.jsx("br",{}),v.jsxs("div",{className:"buttons-group extra-style",children:[v.jsx("button",{className:"admin-btn",onClick:()=>l("/login"),children:"Registered User"}),v.jsx("button",{className:"guest-btn",onClick:()=>l("/home"),children:"Guest User"})]})]}),v.jsxs("section",{className:"right-panel",children:[v.jsx("div",{className:"big-circle"}),v.jsx("div",{className:"small-circle-top"}),v.jsx("div",{className:"small-circle-bottom"}),v.jsx("img",{className:"illustration-top",src:m2,alt:"illustration1"}),v.jsx("img",{className:"illustration-middle",src:h2,alt:"illustration2"}),v.jsx("img",{className:"illustration-bottom",src:d2,alt:"illustration3"})]})]})}const g2="/assets/login-DtlAx1r0.png",v2="/assets/Logo-CnD0Q2qt.png";var Zs=l=>l.type==="checkbox",ja=l=>l instanceof Date,$t=l=>l==null;const vy=l=>typeof l=="object";var ht=l=>!$t(l)&&!Array.isArray(l)&&vy(l)&&!ja(l),b2=l=>ht(l)&&l.target?Zs(l.target)?l.target.checked:l.target.value:l,S2=l=>l.substring(0,l.search(/\.\d+(\.|$)/))||l,x2=(l,a)=>l.has(S2(a)),E2=l=>{const a=l.constructor&&l.constructor.prototype;return ht(a)&&a.hasOwnProperty("isPrototypeOf")},Jf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Bt(l){let a;const s=Array.isArray(l),r=typeof FileList<"u"?l instanceof FileList:!1;if(l instanceof Date)a=new Date(l);else if(l instanceof Set)a=new Set(l);else if(!(Jf&&(l instanceof Blob||r))&&(s||ht(l)))if(a=s?[]:{},!s&&!E2(l))a=l;else for(const o in l)l.hasOwnProperty(o)&&(a[o]=Bt(l[o]));else return l;return a}var zu=l=>Array.isArray(l)?l.filter(Boolean):[],bt=l=>l===void 0,me=(l,a,s)=>{if(!a||!ht(l))return s;const r=zu(a.split(/[,[\].]+?/)).reduce((o,f)=>$t(o)?o:o[f],l);return bt(r)||r===l?bt(l[a])?s:l[a]:r},ll=l=>typeof l=="boolean",Pf=l=>/^\w*$/.test(l),by=l=>zu(l.replace(/["|']|\]/g,"").split(/\.|\[/)),Qe=(l,a,s)=>{let r=-1;const o=Pf(a)?[a]:by(a),f=o.length,d=f-1;for(;++r<f;){const h=o[r];let p=s;if(r!==d){const m=l[h];p=ht(m)||Array.isArray(m)?m:isNaN(+o[r+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;l[h]=p,l=l[h]}};const up={BLUR:"blur",FOCUS_OUT:"focusout"},kn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Ul={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};Pe.createContext(null);var w2=(l,a,s,r=!0)=>{const o={defaultValues:a._defaultValues};for(const f in l)Object.defineProperty(o,f,{get:()=>{const d=f;return a._proxyFormState[d]!==kn.all&&(a._proxyFormState[d]=!r||kn.all),l[d]}});return o},Rf=l=>$t(l)||!vy(l);function ua(l,a){if(Rf(l)||Rf(a))return l===a;if(ja(l)&&ja(a))return l.getTime()===a.getTime();const s=Object.keys(l),r=Object.keys(a);if(s.length!==r.length)return!1;for(const o of s){const f=l[o];if(!r.includes(o))return!1;if(o!=="ref"){const d=a[o];if(ja(f)&&ja(d)||ht(f)&&ht(d)||Array.isArray(f)&&Array.isArray(d)?!ua(f,d):f!==d)return!1}}return!0}var sl=l=>typeof l=="string",A2=(l,a,s,r,o)=>sl(l)?(r&&a.watch.add(l),me(s,l,o)):Array.isArray(l)?l.map(f=>(r&&a.watch.add(f),me(s,f))):(r&&(a.watchAll=!0),s),Sy=(l,a,s,r,o)=>a?{...s[l],types:{...s[l]&&s[l].types?s[l].types:{},[r]:o||!0}}:{},qs=l=>Array.isArray(l)?l:[l],op=()=>{let l=[];return{get observers(){return l},next:o=>{for(const f of l)f.next&&f.next(o)},subscribe:o=>(l.push(o),{unsubscribe:()=>{l=l.filter(f=>f!==o)}}),unsubscribe:()=>{l=[]}}},Yt=l=>ht(l)&&!Object.keys(l).length,Wf=l=>l.type==="file",Bn=l=>typeof l=="function",Eu=l=>{if(!Jf)return!1;const a=l?l.ownerDocument:0;return l instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},xy=l=>l.type==="select-multiple",If=l=>l.type==="radio",_2=l=>If(l)||Zs(l),df=l=>Eu(l)&&l.isConnected;function T2(l,a){const s=a.slice(0,-1).length;let r=0;for(;r<s;)l=bt(l)?r++:l[a[r++]];return l}function R2(l){for(const a in l)if(l.hasOwnProperty(a)&&!bt(l[a]))return!1;return!0}function wt(l,a){const s=Array.isArray(a)?a:Pf(a)?[a]:by(a),r=s.length===1?l:T2(l,s),o=s.length-1,f=s[o];return r&&delete r[f],o!==0&&(ht(r)&&Yt(r)||Array.isArray(r)&&R2(r))&&wt(l,s.slice(0,-1)),l}var Ey=l=>{for(const a in l)if(Bn(l[a]))return!0;return!1};function wu(l,a={}){const s=Array.isArray(l);if(ht(l)||s)for(const r in l)Array.isArray(l[r])||ht(l[r])&&!Ey(l[r])?(a[r]=Array.isArray(l[r])?[]:{},wu(l[r],a[r])):$t(l[r])||(a[r]=!0);return a}function wy(l,a,s){const r=Array.isArray(l);if(ht(l)||r)for(const o in l)Array.isArray(l[o])||ht(l[o])&&!Ey(l[o])?bt(a)||Rf(s[o])?s[o]=Array.isArray(l[o])?wu(l[o],[]):{...wu(l[o])}:wy(l[o],$t(a)?{}:a[o],s[o]):s[o]=!ua(l[o],a[o]);return s}var zs=(l,a)=>wy(l,a,wu(a));const cp={value:!1,isValid:!1},fp={value:!0,isValid:!0};var Ay=l=>{if(Array.isArray(l)){if(l.length>1){const a=l.filter(s=>s&&s.checked&&!s.disabled).map(s=>s.value);return{value:a,isValid:!!a.length}}return l[0].checked&&!l[0].disabled?l[0].attributes&&!bt(l[0].attributes.value)?bt(l[0].value)||l[0].value===""?fp:{value:l[0].value,isValid:!0}:fp:cp}return cp},_y=(l,{valueAsNumber:a,valueAsDate:s,setValueAs:r})=>bt(l)?l:a?l===""?NaN:l&&+l:s&&sl(l)?new Date(l):r?r(l):l;const dp={isValid:!1,value:null};var Ty=l=>Array.isArray(l)?l.reduce((a,s)=>s&&s.checked&&!s.disabled?{isValid:!0,value:s.value}:a,dp):dp;function hp(l){const a=l.ref;return Wf(a)?a.files:If(a)?Ty(l.refs).value:xy(a)?[...a.selectedOptions].map(({value:s})=>s):Zs(a)?Ay(l.refs).value:_y(bt(a.value)?l.ref.value:a.value,l)}var O2=(l,a,s,r)=>{const o={};for(const f of l){const d=me(a,f);d&&Qe(o,f,d._f)}return{criteriaMode:s,names:[...l],fields:o,shouldUseNativeValidation:r}},Au=l=>l instanceof RegExp,ks=l=>bt(l)?l:Au(l)?l.source:ht(l)?Au(l.value)?l.value.source:l.value:l,mp=l=>({isOnSubmit:!l||l===kn.onSubmit,isOnBlur:l===kn.onBlur,isOnChange:l===kn.onChange,isOnAll:l===kn.all,isOnTouch:l===kn.onTouched});const pp="AsyncFunction";var C2=l=>!!l&&!!l.validate&&!!(Bn(l.validate)&&l.validate.constructor.name===pp||ht(l.validate)&&Object.values(l.validate).find(a=>a.constructor.name===pp)),j2=l=>l.mount&&(l.required||l.min||l.max||l.maxLength||l.minLength||l.pattern||l.validate),yp=(l,a,s)=>!s&&(a.watchAll||a.watch.has(l)||[...a.watch].some(r=>l.startsWith(r)&&/^\.\w+/.test(l.slice(r.length))));const Vs=(l,a,s,r)=>{for(const o of s||Object.keys(l)){const f=me(l,o);if(f){const{_f:d,...h}=f;if(d){if(d.refs&&d.refs[0]&&a(d.refs[0],o)&&!r)return!0;if(d.ref&&a(d.ref,d.name)&&!r)return!0;if(Vs(h,a))break}else if(ht(h)&&Vs(h,a))break}}};function gp(l,a,s){const r=me(l,s);if(r||Pf(s))return{error:r,name:s};const o=s.split(".");for(;o.length;){const f=o.join("."),d=me(a,f),h=me(l,f);if(d&&!Array.isArray(d)&&s!==f)return{name:s};if(h&&h.type)return{name:f,error:h};o.pop()}return{name:s}}var D2=(l,a,s,r)=>{s(l);const{name:o,...f}=l;return Yt(f)||Object.keys(f).length>=Object.keys(a).length||Object.keys(f).find(d=>a[d]===(!r||kn.all))},N2=(l,a,s)=>!l||!a||l===a||qs(l).some(r=>r&&(s?r===a:r.startsWith(a)||a.startsWith(r))),U2=(l,a,s,r,o)=>o.isOnAll?!1:!s&&o.isOnTouch?!(a||l):(s?r.isOnBlur:o.isOnBlur)?!l:(s?r.isOnChange:o.isOnChange)?l:!0,M2=(l,a)=>!zu(me(l,a)).length&&wt(l,a),L2=(l,a,s)=>{const r=qs(me(l,s));return Qe(r,"root",a[s]),Qe(l,s,r),l},pu=l=>sl(l);function vp(l,a,s="validate"){if(pu(l)||Array.isArray(l)&&l.every(pu)||ll(l)&&!l)return{type:s,message:pu(l)?l:"",ref:a}}var Ei=l=>ht(l)&&!Au(l)?l:{value:l,message:""},bp=async(l,a,s,r,o,f)=>{const{ref:d,refs:h,required:p,maxLength:m,minLength:g,min:S,max:x,pattern:T,validate:E,name:D,valueAsNumber:C,mount:w}=l._f,j=me(s,D);if(!w||a.has(D))return{};const M=h?h[0]:d,G=W=>{o&&M.reportValidity&&(M.setCustomValidity(ll(W)?"":W||""),M.reportValidity())},H={},te=If(d),I=Zs(d),K=te||I,ne=(C||Wf(d))&&bt(d.value)&&bt(j)||Eu(d)&&d.value===""||j===""||Array.isArray(j)&&!j.length,xe=Sy.bind(null,D,r,H),ce=(W,re,fe,ve=Ul.maxLength,q=Ul.minLength)=>{const P=W?re:fe;H[D]={type:W?ve:q,message:P,ref:d,...xe(W?ve:q,P)}};if(f?!Array.isArray(j)||!j.length:p&&(!K&&(ne||$t(j))||ll(j)&&!j||I&&!Ay(h).isValid||te&&!Ty(h).isValid)){const{value:W,message:re}=pu(p)?{value:!!p,message:p}:Ei(p);if(W&&(H[D]={type:Ul.required,message:re,ref:M,...xe(Ul.required,re)},!r))return G(re),H}if(!ne&&(!$t(S)||!$t(x))){let W,re;const fe=Ei(x),ve=Ei(S);if(!$t(j)&&!isNaN(j)){const q=d.valueAsNumber||j&&+j;$t(fe.value)||(W=q>fe.value),$t(ve.value)||(re=q<ve.value)}else{const q=d.valueAsDate||new Date(j),P=R=>new Date(new Date().toDateString()+" "+R),le=d.type=="time",_e=d.type=="week";sl(fe.value)&&j&&(W=le?P(j)>P(fe.value):_e?j>fe.value:q>new Date(fe.value)),sl(ve.value)&&j&&(re=le?P(j)<P(ve.value):_e?j<ve.value:q<new Date(ve.value))}if((W||re)&&(ce(!!W,fe.message,ve.message,Ul.max,Ul.min),!r))return G(H[D].message),H}if((m||g)&&!ne&&(sl(j)||f&&Array.isArray(j))){const W=Ei(m),re=Ei(g),fe=!$t(W.value)&&j.length>+W.value,ve=!$t(re.value)&&j.length<+re.value;if((fe||ve)&&(ce(fe,W.message,re.message),!r))return G(H[D].message),H}if(T&&!ne&&sl(j)){const{value:W,message:re}=Ei(T);if(Au(W)&&!j.match(W)&&(H[D]={type:Ul.pattern,message:re,ref:d,...xe(Ul.pattern,re)},!r))return G(re),H}if(E){if(Bn(E)){const W=await E(j,s),re=vp(W,M);if(re&&(H[D]={...re,...xe(Ul.validate,re.message)},!r))return G(re.message),H}else if(ht(E)){let W={};for(const re in E){if(!Yt(W)&&!r)break;const fe=vp(await E[re](j,s),M,re);fe&&(W={...fe,...xe(re,fe.message)},G(fe.message),r&&(H[D]=W))}if(!Yt(W)&&(H[D]={ref:M,...W},!r))return H}}return G(!0),H};const z2={mode:kn.onSubmit,reValidateMode:kn.onChange,shouldFocusError:!0};function k2(l={}){let a={...z2,...l},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:Bn(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1};const r={};let o=ht(a.defaultValues)||ht(a.values)?Bt(a.values||a.defaultValues)||{}:{},f=a.shouldUnregister?{}:Bt(o),d={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},p,m=0;const g={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...g};const x={array:op(),state:op()},T=mp(a.mode),E=mp(a.reValidateMode),D=a.criteriaMode===kn.all,C=O=>k=>{clearTimeout(m),m=setTimeout(O,k)},w=async O=>{if(!a.disabled&&(g.isValid||S.isValid||O)){const k=a.resolver?Yt((await ne()).errors):await ce(r,!0);k!==s.isValid&&x.state.next({isValid:k})}},j=(O,k)=>{!a.disabled&&(g.isValidating||g.validatingFields||S.isValidating||S.validatingFields)&&((O||Array.from(h.mount)).forEach($=>{$&&(k?Qe(s.validatingFields,$,k):wt(s.validatingFields,$))}),x.state.next({validatingFields:s.validatingFields,isValidating:!Yt(s.validatingFields)}))},M=(O,k=[],$,oe,ee=!0,J=!0)=>{if(oe&&$&&!a.disabled){if(d.action=!0,J&&Array.isArray(me(r,O))){const ie=$(me(r,O),oe.argA,oe.argB);ee&&Qe(r,O,ie)}if(J&&Array.isArray(me(s.errors,O))){const ie=$(me(s.errors,O),oe.argA,oe.argB);ee&&Qe(s.errors,O,ie),M2(s.errors,O)}if((g.touchedFields||S.touchedFields)&&J&&Array.isArray(me(s.touchedFields,O))){const ie=$(me(s.touchedFields,O),oe.argA,oe.argB);ee&&Qe(s.touchedFields,O,ie)}(g.dirtyFields||S.dirtyFields)&&(s.dirtyFields=zs(o,f)),x.state.next({name:O,isDirty:re(O,k),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else Qe(f,O,k)},G=(O,k)=>{Qe(s.errors,O,k),x.state.next({errors:s.errors})},H=O=>{s.errors=O,x.state.next({errors:s.errors,isValid:!1})},te=(O,k,$,oe)=>{const ee=me(r,O);if(ee){const J=me(f,O,bt($)?me(o,O):$);bt(J)||oe&&oe.defaultChecked||k?Qe(f,O,k?J:hp(ee._f)):q(O,J),d.mount&&w()}},I=(O,k,$,oe,ee)=>{let J=!1,ie=!1;const be={name:O};if(!a.disabled){if(!$||oe){(g.isDirty||S.isDirty)&&(ie=s.isDirty,s.isDirty=be.isDirty=re(),J=ie!==be.isDirty);const We=ua(me(o,O),k);ie=!!me(s.dirtyFields,O),We?wt(s.dirtyFields,O):Qe(s.dirtyFields,O,!0),be.dirtyFields=s.dirtyFields,J=J||(g.dirtyFields||S.dirtyFields)&&ie!==!We}if($){const We=me(s.touchedFields,O);We||(Qe(s.touchedFields,O,$),be.touchedFields=s.touchedFields,J=J||(g.touchedFields||S.touchedFields)&&We!==$)}J&&ee&&x.state.next(be)}return J?be:{}},K=(O,k,$,oe)=>{const ee=me(s.errors,O),J=(g.isValid||S.isValid)&&ll(k)&&s.isValid!==k;if(a.delayError&&$?(p=C(()=>G(O,$)),p(a.delayError)):(clearTimeout(m),p=null,$?Qe(s.errors,O,$):wt(s.errors,O)),($?!ua(ee,$):ee)||!Yt(oe)||J){const ie={...oe,...J&&ll(k)?{isValid:k}:{},errors:s.errors,name:O};s={...s,...ie},x.state.next(ie)}},ne=async O=>{j(O,!0);const k=await a.resolver(f,a.context,O2(O||h.mount,r,a.criteriaMode,a.shouldUseNativeValidation));return j(O),k},xe=async O=>{const{errors:k}=await ne(O);if(O)for(const $ of O){const oe=me(k,$);oe?Qe(s.errors,$,oe):wt(s.errors,$)}else s.errors=k;return k},ce=async(O,k,$={valid:!0})=>{for(const oe in O){const ee=O[oe];if(ee){const{_f:J,...ie}=ee;if(J){const be=h.array.has(J.name),We=ee._f&&C2(ee._f);We&&g.validatingFields&&j([oe],!0);const tt=await bp(ee,h.disabled,f,D,a.shouldUseNativeValidation&&!k,be);if(We&&g.validatingFields&&j([oe]),tt[J.name]&&($.valid=!1,k))break;!k&&(me(tt,J.name)?be?L2(s.errors,tt,J.name):Qe(s.errors,J.name,tt[J.name]):wt(s.errors,J.name))}!Yt(ie)&&await ce(ie,k,$)}}return $.valid},W=()=>{for(const O of h.unMount){const k=me(r,O);k&&(k._f.refs?k._f.refs.every($=>!df($)):!df(k._f.ref))&&St(O)}h.unMount=new Set},re=(O,k)=>!a.disabled&&(O&&k&&Qe(f,O,k),!ua(ue(),o)),fe=(O,k,$)=>A2(O,h,{...d.mount?f:bt(k)?o:sl(O)?{[O]:k}:k},$,k),ve=O=>zu(me(d.mount?f:o,O,a.shouldUnregister?me(o,O,[]):[])),q=(O,k,$={})=>{const oe=me(r,O);let ee=k;if(oe){const J=oe._f;J&&(!J.disabled&&Qe(f,O,_y(k,J)),ee=Eu(J.ref)&&$t(k)?"":k,xy(J.ref)?[...J.ref.options].forEach(ie=>ie.selected=ee.includes(ie.value)):J.refs?Zs(J.ref)?J.refs.length>1?J.refs.forEach(ie=>(!ie.defaultChecked||!ie.disabled)&&(ie.checked=Array.isArray(ee)?!!ee.find(be=>be===ie.value):ee===ie.value)):J.refs[0]&&(J.refs[0].checked=!!ee):J.refs.forEach(ie=>ie.checked=ie.value===ee):Wf(J.ref)?J.ref.value="":(J.ref.value=ee,J.ref.type||x.state.next({name:O,values:Bt(f)})))}($.shouldDirty||$.shouldTouch)&&I(O,ee,$.shouldTouch,$.shouldDirty,!0),$.shouldValidate&&Y(O)},P=(O,k,$)=>{for(const oe in k){const ee=k[oe],J=`${O}.${oe}`,ie=me(r,J);(h.array.has(O)||ht(ee)||ie&&!ie._f)&&!ja(ee)?P(J,ee,$):q(J,ee,$)}},le=(O,k,$={})=>{const oe=me(r,O),ee=h.array.has(O),J=Bt(k);Qe(f,O,J),ee?(x.array.next({name:O,values:Bt(f)}),(g.isDirty||g.dirtyFields||S.isDirty||S.dirtyFields)&&$.shouldDirty&&x.state.next({name:O,dirtyFields:zs(o,f),isDirty:re(O,J)})):oe&&!oe._f&&!$t(J)?P(O,J,$):q(O,J,$),yp(O,h)&&x.state.next({...s}),x.state.next({name:d.mount?O:void 0,values:Bt(f)})},_e=async O=>{d.mount=!0;const k=O.target;let $=k.name,oe=!0;const ee=me(r,$),J=ie=>{oe=Number.isNaN(ie)||ja(ie)&&isNaN(ie.getTime())||ua(ie,me(f,$,ie))};if(ee){let ie,be;const We=k.type?hp(ee._f):b2(O),tt=O.type===up.BLUR||O.type===up.FOCUS_OUT,Dn=!j2(ee._f)&&!a.resolver&&!me(s.errors,$)&&!ee._f.deps||U2(tt,me(s.touchedFields,$),s.isSubmitted,E,T),pt=yp($,h,tt);Qe(f,$,We),tt?(ee._f.onBlur&&ee._f.onBlur(O),p&&p(0)):ee._f.onChange&&ee._f.onChange(O);const qe=I($,We,tt),Xt=!Yt(qe)||pt;if(!tt&&x.state.next({name:$,type:O.type,values:Bt(f)}),Dn)return(g.isValid||S.isValid)&&(a.mode==="onBlur"?tt&&w():tt||w()),Xt&&x.state.next({name:$,...pt?{}:qe});if(!tt&&pt&&x.state.next({...s}),a.resolver){const{errors:an}=await ne([$]);if(J(We),oe){const Zt=gp(s.errors,r,$),Zn=gp(an,r,Zt.name||$);ie=Zn.error,$=Zn.name,be=Yt(an)}}else j([$],!0),ie=(await bp(ee,h.disabled,f,D,a.shouldUseNativeValidation))[$],j([$]),J(We),oe&&(ie?be=!1:(g.isValid||S.isValid)&&(be=await ce(r,!0)));oe&&(ee._f.deps&&Y(ee._f.deps),K($,be,ie,qe))}},R=(O,k)=>{if(me(s.errors,k)&&O.focus)return O.focus(),1},Y=async(O,k={})=>{let $,oe;const ee=qs(O);if(a.resolver){const J=await xe(bt(O)?O:ee);$=Yt(J),oe=O?!ee.some(ie=>me(J,ie)):$}else O?(oe=(await Promise.all(ee.map(async J=>{const ie=me(r,J);return await ce(ie&&ie._f?{[J]:ie}:ie)}))).every(Boolean),!(!oe&&!s.isValid)&&w()):oe=$=await ce(r);return x.state.next({...!sl(O)||(g.isValid||S.isValid)&&$!==s.isValid?{}:{name:O},...a.resolver||!O?{isValid:$}:{},errors:s.errors}),k.shouldFocus&&!oe&&Vs(r,R,O?ee:h.mount),oe},ue=O=>{const k={...d.mount?f:o};return bt(O)?k:sl(O)?me(k,O):O.map($=>me(k,$))},ae=(O,k)=>({invalid:!!me((k||s).errors,O),isDirty:!!me((k||s).dirtyFields,O),error:me((k||s).errors,O),isValidating:!!me(s.validatingFields,O),isTouched:!!me((k||s).touchedFields,O)}),pe=O=>{O&&qs(O).forEach(k=>wt(s.errors,k)),x.state.next({errors:O?s.errors:{}})},je=(O,k,$)=>{const oe=(me(r,O,{_f:{}})._f||{}).ref,ee=me(s.errors,O)||{},{ref:J,message:ie,type:be,...We}=ee;Qe(s.errors,O,{...We,...k,ref:oe}),x.state.next({name:O,errors:s.errors,isValid:!1}),$&&$.shouldFocus&&oe&&oe.focus&&oe.focus()},we=(O,k)=>Bn(O)?x.state.subscribe({next:$=>O(fe(void 0,k),$)}):fe(O,k,!0),it=O=>x.state.subscribe({next:k=>{N2(O.name,k.name,O.exact)&&D2(k,O.formState||g,Ye,O.reRenderRoot)&&O.callback({values:{...f},...s,...k})}}).unsubscribe,Re=O=>(d.mount=!0,S={...S,...O.formState},it({...O,formState:S})),St=(O,k={})=>{for(const $ of O?qs(O):h.mount)h.mount.delete($),h.array.delete($),k.keepValue||(wt(r,$),wt(f,$)),!k.keepError&&wt(s.errors,$),!k.keepDirty&&wt(s.dirtyFields,$),!k.keepTouched&&wt(s.touchedFields,$),!k.keepIsValidating&&wt(s.validatingFields,$),!a.shouldUnregister&&!k.keepDefaultValue&&wt(o,$);x.state.next({values:Bt(f)}),x.state.next({...s,...k.keepDirty?{isDirty:re()}:{}}),!k.keepIsValid&&w()},At=({disabled:O,name:k})=>{(ll(O)&&d.mount||O||h.disabled.has(k))&&(O?h.disabled.add(k):h.disabled.delete(k))},jt=(O,k={})=>{let $=me(r,O);const oe=ll(k.disabled)||ll(a.disabled);return Qe(r,O,{...$||{},_f:{...$&&$._f?$._f:{ref:{name:O}},name:O,mount:!0,...k}}),h.mount.add(O),$?At({disabled:ll(k.disabled)?k.disabled:a.disabled,name:O}):te(O,!0,k.value),{...oe?{disabled:k.disabled||a.disabled}:{},...a.progressive?{required:!!k.required,min:ks(k.min),max:ks(k.max),minLength:ks(k.minLength),maxLength:ks(k.maxLength),pattern:ks(k.pattern)}:{},name:O,onChange:_e,onBlur:_e,ref:ee=>{if(ee){jt(O,k),$=me(r,O);const J=bt(ee.value)&&ee.querySelectorAll&&ee.querySelectorAll("input,select,textarea")[0]||ee,ie=_2(J),be=$._f.refs||[];if(ie?be.find(We=>We===J):J===$._f.ref)return;Qe(r,O,{_f:{...$._f,...ie?{refs:[...be.filter(df),J,...Array.isArray(me(o,O))?[{}]:[]],ref:{type:J.type,name:O}}:{ref:J}}}),te(O,!1,void 0,J)}else $=me(r,O,{}),$._f&&($._f.mount=!1),(a.shouldUnregister||k.shouldUnregister)&&!(x2(h.array,O)&&d.action)&&h.unMount.add(O)}}},pn=()=>a.shouldFocusError&&Vs(r,R,h.mount),Gt=O=>{ll(O)&&(x.state.next({disabled:O}),Vs(r,(k,$)=>{const oe=me(r,$);oe&&(k.disabled=oe._f.disabled||O,Array.isArray(oe._f.refs)&&oe._f.refs.forEach(ee=>{ee.disabled=oe._f.disabled||O}))},0,!1))},Xn=(O,k)=>async $=>{let oe;$&&($.preventDefault&&$.preventDefault(),$.persist&&$.persist());let ee=Bt(f);if(x.state.next({isSubmitting:!0}),a.resolver){const{errors:J,values:ie}=await ne();s.errors=J,ee=ie}else await ce(r);if(h.disabled.size)for(const J of h.disabled)Qe(ee,J,void 0);if(wt(s.errors,"root"),Yt(s.errors)){x.state.next({errors:{}});try{await O(ee,$)}catch(J){oe=J}}else k&&await k({...s.errors},$),pn(),setTimeout(pn);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Yt(s.errors)&&!oe,submitCount:s.submitCount+1,errors:s.errors}),oe)throw oe},Dt=(O,k={})=>{me(r,O)&&(bt(k.defaultValue)?le(O,Bt(me(o,O))):(le(O,k.defaultValue),Qe(o,O,Bt(k.defaultValue))),k.keepTouched||wt(s.touchedFields,O),k.keepDirty||(wt(s.dirtyFields,O),s.isDirty=k.defaultValue?re(O,Bt(me(o,O))):re()),k.keepError||(wt(s.errors,O),g.isValid&&w()),x.state.next({...s}))},dl=(O,k={})=>{const $=O?Bt(O):o,oe=Bt($),ee=Yt(O),J=ee?o:oe;if(k.keepDefaultValues||(o=$),!k.keepValues){if(k.keepDirtyValues){const ie=new Set([...h.mount,...Object.keys(zs(o,f))]);for(const be of Array.from(ie))me(s.dirtyFields,be)?Qe(J,be,me(f,be)):le(be,me(J,be))}else{if(Jf&&bt(O))for(const ie of h.mount){const be=me(r,ie);if(be&&be._f){const We=Array.isArray(be._f.refs)?be._f.refs[0]:be._f.ref;if(Eu(We)){const tt=We.closest("form");if(tt){tt.reset();break}}}}for(const ie of h.mount)le(ie,me(J,ie))}f=Bt(J),x.array.next({values:{...J}}),x.state.next({values:{...J}})}h={mount:k.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!g.isValid||!!k.keepIsValid||!!k.keepDirtyValues,d.watch=!!a.shouldUnregister,x.state.next({submitCount:k.keepSubmitCount?s.submitCount:0,isDirty:ee?!1:k.keepDirty?s.isDirty:!!(k.keepDefaultValues&&!ua(O,o)),isSubmitted:k.keepIsSubmitted?s.isSubmitted:!1,dirtyFields:ee?{}:k.keepDirtyValues?k.keepDefaultValues&&f?zs(o,f):s.dirtyFields:k.keepDefaultValues&&O?zs(o,O):k.keepDirty?s.dirtyFields:{},touchedFields:k.keepTouched?s.touchedFields:{},errors:k.keepErrors?s.errors:{},isSubmitSuccessful:k.keepIsSubmitSuccessful?s.isSubmitSuccessful:!1,isSubmitting:!1})},ut=(O,k)=>dl(Bn(O)?O(f):O,k),jn=(O,k={})=>{const $=me(r,O),oe=$&&$._f;if(oe){const ee=oe.refs?oe.refs[0]:oe.ref;ee.focus&&(ee.focus(),k.shouldSelect&&Bn(ee.select)&&ee.select())}},Ye=O=>{s={...s,...O}},ln={control:{register:jt,unregister:St,getFieldState:ae,handleSubmit:Xn,setError:je,_subscribe:it,_runSchema:ne,_getWatch:fe,_getDirty:re,_setValid:w,_setFieldArray:M,_setDisabledField:At,_setErrors:H,_getFieldArray:ve,_reset:dl,_resetDefaultValues:()=>Bn(a.defaultValues)&&a.defaultValues().then(O=>{ut(O,a.resetOptions),x.state.next({isLoading:!1})}),_removeUnmounted:W,_disableForm:Gt,_subjects:x,_proxyFormState:g,get _fields(){return r},get _formValues(){return f},get _state(){return d},set _state(O){d=O},get _defaultValues(){return o},get _names(){return h},set _names(O){h=O},get _formState(){return s},get _options(){return a},set _options(O){a={...a,...O}}},subscribe:Re,trigger:Y,register:jt,handleSubmit:Xn,watch:we,setValue:le,getValues:ue,reset:ut,resetField:Dt,clearErrors:pe,unregister:St,setError:je,setFocus:jn,getFieldState:ae};return{...ln,formControl:ln}}const B2=typeof window<"u"?Pe.useLayoutEffect:Pe.useEffect;function Ui(l={}){const a=Pe.useRef(void 0),s=Pe.useRef(void 0),[r,o]=Pe.useState({isDirty:!1,isValidating:!1,isLoading:Bn(l.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1,isReady:!1,defaultValues:Bn(l.defaultValues)?void 0:l.defaultValues});a.current||(a.current={...l.formControl?l.formControl:k2(l),formState:r},l.formControl&&l.defaultValues&&!Bn(l.defaultValues)&&l.formControl.reset(l.defaultValues,l.resetOptions));const f=a.current.control;return f._options=l,B2(()=>{const d=f._subscribe({formState:f._proxyFormState,callback:()=>o({...f._formState}),reRenderRoot:!0});return o(h=>({...h,isReady:!0})),f._formState.isReady=!0,d},[f]),Pe.useEffect(()=>f._disableForm(l.disabled),[f,l.disabled]),Pe.useEffect(()=>{l.mode&&(f._options.mode=l.mode),l.reValidateMode&&(f._options.reValidateMode=l.reValidateMode),l.errors&&!Yt(l.errors)&&f._setErrors(l.errors)},[f,l.errors,l.mode,l.reValidateMode]),Pe.useEffect(()=>{l.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,l.shouldUnregister]),Pe.useEffect(()=>{if(f._proxyFormState.isDirty){const d=f._getDirty();d!==r.isDirty&&f._subjects.state.next({isDirty:d})}},[f,r.isDirty]),Pe.useEffect(()=>{l.values&&!ua(l.values,s.current)?(f._reset(l.values,f._options.resetOptions),s.current=l.values,o(d=>({...d}))):f._resetDefaultValues()},[f,l.values]),Pe.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),a.current.formState=w2(r,f),a.current}var hf,Sp;function H2(){if(Sp)return hf;Sp=1;function l(w){this._maxSize=w,this.clear()}l.prototype.clear=function(){this._size=0,this._values=Object.create(null)},l.prototype.get=function(w){return this._values[w]},l.prototype.set=function(w,j){return this._size>=this._maxSize&&this.clear(),w in this._values||this._size++,this._values[w]=j};var a=/[^.^\]^[]+|(?=\[\]|\.\.)/g,s=/^\d+$/,r=/^\d/,o=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,f=/^\s*(['"]?)(.*?)(\1)\s*$/,d=512,h=new l(d),p=new l(d),m=new l(d);hf={Cache:l,split:S,normalizePath:g,setter:function(w){var j=g(w);return p.get(w)||p.set(w,function(G,H){for(var te=0,I=j.length,K=G;te<I-1;){var ne=j[te];if(ne==="__proto__"||ne==="constructor"||ne==="prototype")return G;K=K[j[te++]]}K[j[te]]=H})},getter:function(w,j){var M=g(w);return m.get(w)||m.set(w,function(H){for(var te=0,I=M.length;te<I;)if(H!=null||!j)H=H[M[te++]];else return;return H})},join:function(w){return w.reduce(function(j,M){return j+(T(M)||s.test(M)?"["+M+"]":(j?".":"")+M)},"")},forEach:function(w,j,M){x(Array.isArray(w)?w:S(w),j,M)}};function g(w){return h.get(w)||h.set(w,S(w).map(function(j){return j.replace(f,"$2")}))}function S(w){return w.match(a)||[""]}function x(w,j,M){var G=w.length,H,te,I,K;for(te=0;te<G;te++)H=w[te],H&&(C(H)&&(H='"'+H+'"'),K=T(H),I=!K&&/^\d+$/.test(H),j.call(M,H,K,I,te,w))}function T(w){return typeof w=="string"&&w&&["'",'"'].indexOf(w.charAt(0))!==-1}function E(w){return w.match(r)&&!w.match(s)}function D(w){return o.test(w)}function C(w){return!T(w)&&(E(w)||D(w))}return hf}var Ua=H2(),mf,xp;function q2(){if(xp)return mf;xp=1;const l=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,a=g=>g.match(l)||[],s=g=>g[0].toUpperCase()+g.slice(1),r=(g,S)=>a(g).join(S).toLowerCase(),o=g=>a(g).reduce((S,x)=>`${S}${S?x[0].toUpperCase()+x.slice(1).toLowerCase():x.toLowerCase()}`,"");return mf={words:a,upperFirst:s,camelCase:o,pascalCase:g=>s(o(g)),snakeCase:g=>r(g,"_"),kebabCase:g=>r(g,"-"),sentenceCase:g=>s(r(g," ")),titleCase:g=>a(g).map(s).join(" ")},mf}var pf=q2(),cu={exports:{}},Ep;function V2(){if(Ep)return cu.exports;Ep=1,cu.exports=function(o){return l(a(o),o)},cu.exports.array=l;function l(o,f){var d=o.length,h=new Array(d),p={},m=d,g=s(f),S=r(o);for(f.forEach(function(T){if(!S.has(T[0])||!S.has(T[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});m--;)p[m]||x(o[m],m,new Set);return h;function x(T,E,D){if(D.has(T)){var C;try{C=", node was:"+JSON.stringify(T)}catch{C=""}throw new Error("Cyclic dependency"+C)}if(!S.has(T))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(T));if(!p[E]){p[E]=!0;var w=g.get(T)||new Set;if(w=Array.from(w),E=w.length){D.add(T);do{var j=w[--E];x(j,S.get(j),D)}while(E);D.delete(T)}h[--d]=T}}}function a(o){for(var f=new Set,d=0,h=o.length;d<h;d++){var p=o[d];f.add(p[0]),f.add(p[1])}return Array.from(f)}function s(o){for(var f=new Map,d=0,h=o.length;d<h;d++){var p=o[d];f.has(p[0])||f.set(p[0],new Set),f.has(p[1])||f.set(p[1],new Set),f.get(p[0]).add(p[1])}return f}function r(o){for(var f=new Map,d=0,h=o.length;d<h;d++)f.set(o[d],d);return f}return cu.exports}var F2=V2();const Y2=Vf(F2),$2=Object.prototype.toString,G2=Error.prototype.toString,X2=RegExp.prototype.toString,Z2=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",Q2=/^Symbol\((.*)\)(.*)$/;function K2(l){return l!=+l?"NaN":l===0&&1/l<0?"-0":""+l}function wp(l,a=!1){if(l==null||l===!0||l===!1)return""+l;const s=typeof l;if(s==="number")return K2(l);if(s==="string")return a?`"${l}"`:l;if(s==="function")return"[Function "+(l.name||"anonymous")+"]";if(s==="symbol")return Z2.call(l).replace(Q2,"Symbol($1)");const r=$2.call(l).slice(8,-1);return r==="Date"?isNaN(l.getTime())?""+l:l.toISOString(l):r==="Error"||l instanceof Error?"["+G2.call(l)+"]":r==="RegExp"?X2.call(l):null}function oa(l,a){let s=wp(l,a);return s!==null?s:JSON.stringify(l,function(r,o){let f=wp(this[r],a);return f!==null?f:o},2)}function Ry(l){return l==null?[]:[].concat(l)}let Oy,Cy,jy,J2=/\$\{\s*(\w+)\s*\}/g;Oy=Symbol.toStringTag;class Ap{constructor(a,s,r,o){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[Oy]="Error",this.name="ValidationError",this.value=s,this.path=r,this.type=o,this.errors=[],this.inner=[],Ry(a).forEach(f=>{if(It.isError(f)){this.errors.push(...f.errors);const d=f.inner.length?f.inner:[f];this.inner.push(...d)}else this.errors.push(f)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}Cy=Symbol.hasInstance;jy=Symbol.toStringTag;class It extends Error{static formatError(a,s){const r=s.label||s.path||"this";return s=Object.assign({},s,{path:r,originalPath:s.path}),typeof a=="string"?a.replace(J2,(o,f)=>oa(s[f])):typeof a=="function"?a(s):a}static isError(a){return a&&a.name==="ValidationError"}constructor(a,s,r,o,f){const d=new Ap(a,s,r,o);if(f)return d;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[jy]="Error",this.name=d.name,this.message=d.message,this.type=d.type,this.value=d.value,this.path=d.path,this.errors=d.errors,this.inner=d.inner,Error.captureStackTrace&&Error.captureStackTrace(this,It)}static[Cy](a){return Ap[Symbol.hasInstance](a)||super[Symbol.hasInstance](a)}}let al={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:l,type:a,value:s,originalValue:r})=>{const o=r!=null&&r!==s?` (cast from the value \`${oa(r,!0)}\`).`:".";return a!=="mixed"?`${l} must be a \`${a}\` type, but the final value was: \`${oa(s,!0)}\``+o:`${l} must match the configured type. The validated value was: \`${oa(s,!0)}\``+o}},Wt={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},P2={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},Of={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},W2={isValue:"${path} field must be ${value}"},yu={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},I2={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},eS={notType:l=>{const{path:a,value:s,spec:r}=l,o=r.types.length;if(Array.isArray(s)){if(s.length<o)return`${a} tuple value has too few items, expected a length of ${o} but got ${s.length} for value: \`${oa(s,!0)}\``;if(s.length>o)return`${a} tuple value has too many items, expected a length of ${o} but got ${s.length} for value: \`${oa(s,!0)}\``}return It.formatError(al.notType,l)}};Object.assign(Object.create(null),{mixed:al,string:Wt,number:P2,date:Of,object:yu,array:I2,boolean:W2,tuple:eS});const ed=l=>l&&l.__isYupSchema__;class _u{static fromOptions(a,s){if(!s.then&&!s.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:r,then:o,otherwise:f}=s,d=typeof r=="function"?r:(...h)=>h.every(p=>p===r);return new _u(a,(h,p)=>{var m;let g=d(...h)?o:f;return(m=g==null?void 0:g(p))!=null?m:p})}constructor(a,s){this.fn=void 0,this.refs=a,this.refs=a,this.fn=s}resolve(a,s){let r=this.refs.map(f=>f.getValue(s==null?void 0:s.value,s==null?void 0:s.parent,s==null?void 0:s.context)),o=this.fn(r,a,s);if(o===void 0||o===a)return a;if(!ed(o))throw new TypeError("conditions must return a schema object");return o.resolve(s)}}const fu={context:"$",value:"."};function td(l,a){return new da(l,a)}class da{constructor(a,s={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof a!="string")throw new TypeError("ref must be a string, got: "+a);if(this.key=a.trim(),a==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===fu.context,this.isValue=this.key[0]===fu.value,this.isSibling=!this.isContext&&!this.isValue;let r=this.isContext?fu.context:this.isValue?fu.value:"";this.path=this.key.slice(r.length),this.getter=this.path&&Ua.getter(this.path,!0),this.map=s.map}getValue(a,s,r){let o=this.isContext?r:this.isValue?a:s;return this.getter&&(o=this.getter(o||{})),this.map&&(o=this.map(o)),o}cast(a,s){return this.getValue(a,s==null?void 0:s.parent,s==null?void 0:s.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(a){return a&&a.__isYupRef}}da.prototype.__isYupRef=!0;const Da=l=>l==null;function wi(l){function a({value:s,path:r="",options:o,originalValue:f,schema:d},h,p){const{name:m,test:g,params:S,message:x,skipAbsent:T}=l;let{parent:E,context:D,abortEarly:C=d.spec.abortEarly,disableStackTrace:w=d.spec.disableStackTrace}=o;function j(ce){return da.isRef(ce)?ce.getValue(s,E,D):ce}function M(ce={}){const W=Object.assign({value:s,originalValue:f,label:d.spec.label,path:ce.path||r,spec:d.spec,disableStackTrace:ce.disableStackTrace||w},S,ce.params);for(const fe of Object.keys(W))W[fe]=j(W[fe]);const re=new It(It.formatError(ce.message||x,W),s,W.path,ce.type||m,W.disableStackTrace);return re.params=W,re}const G=C?h:p;let H={path:r,parent:E,type:m,from:o.from,createError:M,resolve:j,options:o,originalValue:f,schema:d};const te=ce=>{It.isError(ce)?G(ce):ce?p(null):G(M())},I=ce=>{It.isError(ce)?G(ce):h(ce)};if(T&&Da(s))return te(!0);let ne;try{var xe;if(ne=g.call(H,s,H),typeof((xe=ne)==null?void 0:xe.then)=="function"){if(o.sync)throw new Error(`Validation test of type: "${H.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(ne).then(te,I)}}catch(ce){I(ce);return}te(ne)}return a.OPTIONS=l,a}function tS(l,a,s,r=s){let o,f,d;return a?(Ua.forEach(a,(h,p,m)=>{let g=p?h.slice(1,h.length-1):h;l=l.resolve({context:r,parent:o,value:s});let S=l.type==="tuple",x=m?parseInt(g,10):0;if(l.innerType||S){if(S&&!m)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${d}" must contain an index to the tuple element, e.g. "${d}[0]"`);if(s&&x>=s.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${h}, in the path: ${a}. because there is no value at that index. `);o=s,s=s&&s[x],l=S?l.spec.types[x]:l.innerType}if(!m){if(!l.fields||!l.fields[g])throw new Error(`The schema does not contain the path: ${a}. (failed at: ${d} which is a type: "${l.type}")`);o=s,s=s&&s[g],l=l.fields[g]}f=g,d=p?"["+h+"]":"."+h}),{schema:l,parent:o,parentPath:f}):{parent:o,parentPath:a,schema:l}}class Tu extends Set{describe(){const a=[];for(const s of this.values())a.push(da.isRef(s)?s.describe():s);return a}resolveAll(a){let s=[];for(const r of this.values())s.push(a(r));return s}clone(){return new Tu(this.values())}merge(a,s){const r=this.clone();return a.forEach(o=>r.add(o)),s.forEach(o=>r.delete(o)),r}}function _i(l,a=new Map){if(ed(l)||!l||typeof l!="object")return l;if(a.has(l))return a.get(l);let s;if(l instanceof Date)s=new Date(l.getTime()),a.set(l,s);else if(l instanceof RegExp)s=new RegExp(l),a.set(l,s);else if(Array.isArray(l)){s=new Array(l.length),a.set(l,s);for(let r=0;r<l.length;r++)s[r]=_i(l[r],a)}else if(l instanceof Map){s=new Map,a.set(l,s);for(const[r,o]of l.entries())s.set(r,_i(o,a))}else if(l instanceof Set){s=new Set,a.set(l,s);for(const r of l)s.add(_i(r,a))}else if(l instanceof Object){s={},a.set(l,s);for(const[r,o]of Object.entries(l))s[r]=_i(o,a)}else throw Error(`Unable to clone ${l}`);return s}class ul{constructor(a){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new Tu,this._blacklist=new Tu,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(al.notType)}),this.type=a.type,this._typeCheck=a.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},a==null?void 0:a.spec),this.withMutation(s=>{s.nonNullable()})}get _type(){return this.type}clone(a){if(this._mutate)return a&&Object.assign(this.spec,a),this;const s=Object.create(Object.getPrototypeOf(this));return s.type=this.type,s._typeCheck=this._typeCheck,s._whitelist=this._whitelist.clone(),s._blacklist=this._blacklist.clone(),s.internalTests=Object.assign({},this.internalTests),s.exclusiveTests=Object.assign({},this.exclusiveTests),s.deps=[...this.deps],s.conditions=[...this.conditions],s.tests=[...this.tests],s.transforms=[...this.transforms],s.spec=_i(Object.assign({},this.spec,a)),s}label(a){let s=this.clone();return s.spec.label=a,s}meta(...a){if(a.length===0)return this.spec.meta;let s=this.clone();return s.spec.meta=Object.assign(s.spec.meta||{},a[0]),s}withMutation(a){let s=this._mutate;this._mutate=!0;let r=a(this);return this._mutate=s,r}concat(a){if(!a||a===this)return this;if(a.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${a.type}`);let s=this,r=a.clone();const o=Object.assign({},s.spec,r.spec);return r.spec=o,r.internalTests=Object.assign({},s.internalTests,r.internalTests),r._whitelist=s._whitelist.merge(a._whitelist,a._blacklist),r._blacklist=s._blacklist.merge(a._blacklist,a._whitelist),r.tests=s.tests,r.exclusiveTests=s.exclusiveTests,r.withMutation(f=>{a.tests.forEach(d=>{f.test(d.OPTIONS)})}),r.transforms=[...s.transforms,...r.transforms],r}isType(a){return a==null?!!(this.spec.nullable&&a===null||this.spec.optional&&a===void 0):this._typeCheck(a)}resolve(a){let s=this;if(s.conditions.length){let r=s.conditions;s=s.clone(),s.conditions=[],s=r.reduce((o,f)=>f.resolve(o,a),s),s=s.resolve(a)}return s}resolveOptions(a){var s,r,o,f;return Object.assign({},a,{from:a.from||[],strict:(s=a.strict)!=null?s:this.spec.strict,abortEarly:(r=a.abortEarly)!=null?r:this.spec.abortEarly,recursive:(o=a.recursive)!=null?o:this.spec.recursive,disableStackTrace:(f=a.disableStackTrace)!=null?f:this.spec.disableStackTrace})}cast(a,s={}){let r=this.resolve(Object.assign({value:a},s)),o=s.assert==="ignore-optionality",f=r._cast(a,s);if(s.assert!==!1&&!r.isType(f)){if(o&&Da(f))return f;let d=oa(a),h=oa(f);throw new TypeError(`The value of ${s.path||"field"} could not be cast to a value that satisfies the schema type: "${r.type}". 

attempted value: ${d} 
`+(h!==d?`result of cast: ${h}`:""))}return f}_cast(a,s){let r=a===void 0?a:this.transforms.reduce((o,f)=>f.call(this,o,a,this),a);return r===void 0&&(r=this.getDefault(s)),r}_validate(a,s={},r,o){let{path:f,originalValue:d=a,strict:h=this.spec.strict}=s,p=a;h||(p=this._cast(p,Object.assign({assert:!1},s)));let m=[];for(let g of Object.values(this.internalTests))g&&m.push(g);this.runTests({path:f,value:p,originalValue:d,options:s,tests:m},r,g=>{if(g.length)return o(g,p);this.runTests({path:f,value:p,originalValue:d,options:s,tests:this.tests},r,o)})}runTests(a,s,r){let o=!1,{tests:f,value:d,originalValue:h,path:p,options:m}=a,g=D=>{o||(o=!0,s(D,d))},S=D=>{o||(o=!0,r(D,d))},x=f.length,T=[];if(!x)return S([]);let E={value:d,originalValue:h,path:p,options:m,schema:this};for(let D=0;D<f.length;D++){const C=f[D];C(E,g,function(j){j&&(Array.isArray(j)?T.push(...j):T.push(j)),--x<=0&&S(T)})}}asNestedTest({key:a,index:s,parent:r,parentPath:o,originalParent:f,options:d}){const h=a??s;if(h==null)throw TypeError("Must include `key` or `index` for nested validations");const p=typeof h=="number";let m=r[h];const g=Object.assign({},d,{strict:!0,parent:r,value:m,originalValue:f[h],key:void 0,[p?"index":"key"]:h,path:p||h.includes(".")?`${o||""}[${p?h:`"${h}"`}]`:(o?`${o}.`:"")+a});return(S,x,T)=>this.resolve(g)._validate(m,g,x,T)}validate(a,s){var r;let o=this.resolve(Object.assign({},s,{value:a})),f=(r=s==null?void 0:s.disableStackTrace)!=null?r:o.spec.disableStackTrace;return new Promise((d,h)=>o._validate(a,s,(p,m)=>{It.isError(p)&&(p.value=m),h(p)},(p,m)=>{p.length?h(new It(p,m,void 0,void 0,f)):d(m)}))}validateSync(a,s){var r;let o=this.resolve(Object.assign({},s,{value:a})),f,d=(r=s==null?void 0:s.disableStackTrace)!=null?r:o.spec.disableStackTrace;return o._validate(a,Object.assign({},s,{sync:!0}),(h,p)=>{throw It.isError(h)&&(h.value=p),h},(h,p)=>{if(h.length)throw new It(h,a,void 0,void 0,d);f=p}),f}isValid(a,s){return this.validate(a,s).then(()=>!0,r=>{if(It.isError(r))return!1;throw r})}isValidSync(a,s){try{return this.validateSync(a,s),!0}catch(r){if(It.isError(r))return!1;throw r}}_getDefault(a){let s=this.spec.default;return s==null?s:typeof s=="function"?s.call(this,a):_i(s)}getDefault(a){return this.resolve(a||{})._getDefault(a)}default(a){return arguments.length===0?this._getDefault():this.clone({default:a})}strict(a=!0){return this.clone({strict:a})}nullability(a,s){const r=this.clone({nullable:a});return r.internalTests.nullable=wi({message:s,name:"nullable",test(o){return o===null?this.schema.spec.nullable:!0}}),r}optionality(a,s){const r=this.clone({optional:a});return r.internalTests.optionality=wi({message:s,name:"optionality",test(o){return o===void 0?this.schema.spec.optional:!0}}),r}optional(){return this.optionality(!0)}defined(a=al.defined){return this.optionality(!1,a)}nullable(){return this.nullability(!0)}nonNullable(a=al.notNull){return this.nullability(!1,a)}required(a=al.required){return this.clone().withMutation(s=>s.nonNullable(a).defined(a))}notRequired(){return this.clone().withMutation(a=>a.nullable().optional())}transform(a){let s=this.clone();return s.transforms.push(a),s}test(...a){let s;if(a.length===1?typeof a[0]=="function"?s={test:a[0]}:s=a[0]:a.length===2?s={name:a[0],test:a[1]}:s={name:a[0],message:a[1],test:a[2]},s.message===void 0&&(s.message=al.default),typeof s.test!="function")throw new TypeError("`test` is a required parameters");let r=this.clone(),o=wi(s),f=s.exclusive||s.name&&r.exclusiveTests[s.name]===!0;if(s.exclusive&&!s.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return s.name&&(r.exclusiveTests[s.name]=!!s.exclusive),r.tests=r.tests.filter(d=>!(d.OPTIONS.name===s.name&&(f||d.OPTIONS.test===o.OPTIONS.test))),r.tests.push(o),r}when(a,s){!Array.isArray(a)&&typeof a!="string"&&(s=a,a=".");let r=this.clone(),o=Ry(a).map(f=>new da(f));return o.forEach(f=>{f.isSibling&&r.deps.push(f.key)}),r.conditions.push(typeof s=="function"?new _u(o,s):_u.fromOptions(o,s)),r}typeError(a){let s=this.clone();return s.internalTests.typeError=wi({message:a,name:"typeError",skipAbsent:!0,test(r){return this.schema._typeCheck(r)?!0:this.createError({params:{type:this.schema.type}})}}),s}oneOf(a,s=al.oneOf){let r=this.clone();return a.forEach(o=>{r._whitelist.add(o),r._blacklist.delete(o)}),r.internalTests.whiteList=wi({message:s,name:"oneOf",skipAbsent:!0,test(o){let f=this.schema._whitelist,d=f.resolveAll(this.resolve);return d.includes(o)?!0:this.createError({params:{values:Array.from(f).join(", "),resolved:d}})}}),r}notOneOf(a,s=al.notOneOf){let r=this.clone();return a.forEach(o=>{r._blacklist.add(o),r._whitelist.delete(o)}),r.internalTests.blacklist=wi({message:s,name:"notOneOf",test(o){let f=this.schema._blacklist,d=f.resolveAll(this.resolve);return d.includes(o)?this.createError({params:{values:Array.from(f).join(", "),resolved:d}}):!0}}),r}strip(a=!0){let s=this.clone();return s.spec.strip=a,s}describe(a){const s=(a?this.resolve(a):this).clone(),{label:r,meta:o,optional:f,nullable:d}=s.spec;return{meta:o,label:r,optional:f,nullable:d,default:s.getDefault(a),type:s.type,oneOf:s._whitelist.describe(),notOneOf:s._blacklist.describe(),tests:s.tests.map(p=>({name:p.OPTIONS.name,params:p.OPTIONS.params})).filter((p,m,g)=>g.findIndex(S=>S.name===p.name)===m)}}}ul.prototype.__isYupSchema__=!0;for(const l of["validate","validateSync"])ul.prototype[`${l}At`]=function(a,s,r={}){const{parent:o,parentPath:f,schema:d}=tS(this,a,s,r.context);return d[l](o&&o[f],Object.assign({},r,{parent:o,path:a}))};for(const l of["equals","is"])ul.prototype[l]=ul.prototype.oneOf;for(const l of["not","nope"])ul.prototype[l]=ul.prototype.notOneOf;const nS=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function lS(l){const a=Cf(l);if(!a)return Date.parse?Date.parse(l):Number.NaN;if(a.z===void 0&&a.plusMinus===void 0)return new Date(a.year,a.month,a.day,a.hour,a.minute,a.second,a.millisecond).valueOf();let s=0;return a.z!=="Z"&&a.plusMinus!==void 0&&(s=a.hourOffset*60+a.minuteOffset,a.plusMinus==="+"&&(s=0-s)),Date.UTC(a.year,a.month,a.day,a.hour,a.minute+s,a.second,a.millisecond)}function Cf(l){var a,s;const r=nS.exec(l);return r?{year:Ml(r[1]),month:Ml(r[2],1)-1,day:Ml(r[3],1),hour:Ml(r[4]),minute:Ml(r[5]),second:Ml(r[6]),millisecond:r[7]?Ml(r[7].substring(0,3)):0,precision:(a=(s=r[7])==null?void 0:s.length)!=null?a:void 0,z:r[8]||void 0,plusMinus:r[9]||void 0,hourOffset:Ml(r[10]),minuteOffset:Ml(r[11])}:null}function Ml(l,a=0){return Number(l)||a}let aS=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,iS=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,sS=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,rS="^\\d{4}-\\d{2}-\\d{2}",uS="\\d{2}:\\d{2}:\\d{2}",oS="(([+-]\\d{2}(:?\\d{2})?)|Z)",cS=new RegExp(`${rS}T${uS}(\\.\\d+)?${oS}$`),fS=l=>Da(l)||l===l.trim(),dS={}.toString();function en(){return new Dy}class Dy extends ul{constructor(){super({type:"string",check(a){return a instanceof String&&(a=a.valueOf()),typeof a=="string"}}),this.withMutation(()=>{this.transform((a,s,r)=>{if(!r.spec.coerce||r.isType(a)||Array.isArray(a))return a;const o=a!=null&&a.toString?a.toString():a;return o===dS?a:o})})}required(a){return super.required(a).withMutation(s=>s.test({message:a||al.required,name:"required",skipAbsent:!0,test:r=>!!r.length}))}notRequired(){return super.notRequired().withMutation(a=>(a.tests=a.tests.filter(s=>s.OPTIONS.name!=="required"),a))}length(a,s=Wt.length){return this.test({message:s,name:"length",exclusive:!0,params:{length:a},skipAbsent:!0,test(r){return r.length===this.resolve(a)}})}min(a,s=Wt.min){return this.test({message:s,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(r){return r.length>=this.resolve(a)}})}max(a,s=Wt.max){return this.test({name:"max",exclusive:!0,message:s,params:{max:a},skipAbsent:!0,test(r){return r.length<=this.resolve(a)}})}matches(a,s){let r=!1,o,f;return s&&(typeof s=="object"?{excludeEmptyString:r=!1,message:o,name:f}=s:o=s),this.test({name:f||"matches",message:o||Wt.matches,params:{regex:a},skipAbsent:!0,test:d=>d===""&&r||d.search(a)!==-1})}email(a=Wt.email){return this.matches(aS,{name:"email",message:a,excludeEmptyString:!0})}url(a=Wt.url){return this.matches(iS,{name:"url",message:a,excludeEmptyString:!0})}uuid(a=Wt.uuid){return this.matches(sS,{name:"uuid",message:a,excludeEmptyString:!1})}datetime(a){let s="",r,o;return a&&(typeof a=="object"?{message:s="",allowOffset:r=!1,precision:o=void 0}=a:s=a),this.matches(cS,{name:"datetime",message:s||Wt.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:s||Wt.datetime_offset,params:{allowOffset:r},skipAbsent:!0,test:f=>{if(!f||r)return!0;const d=Cf(f);return d?!!d.z:!1}}).test({name:"datetime_precision",message:s||Wt.datetime_precision,params:{precision:o},skipAbsent:!0,test:f=>{if(!f||o==null)return!0;const d=Cf(f);return d?d.precision===o:!1}})}ensure(){return this.default("").transform(a=>a===null?"":a)}trim(a=Wt.trim){return this.transform(s=>s!=null?s.trim():s).test({message:a,name:"trim",test:fS})}lowercase(a=Wt.lowercase){return this.transform(s=>Da(s)?s:s.toLowerCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:s=>Da(s)||s===s.toLowerCase()})}uppercase(a=Wt.uppercase){return this.transform(s=>Da(s)?s:s.toUpperCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:s=>Da(s)||s===s.toUpperCase()})}}en.prototype=Dy.prototype;let hS=new Date(""),mS=l=>Object.prototype.toString.call(l)==="[object Date]";class nd extends ul{constructor(){super({type:"date",check(a){return mS(a)&&!isNaN(a.getTime())}}),this.withMutation(()=>{this.transform((a,s,r)=>!r.spec.coerce||r.isType(a)||a===null?a:(a=lS(a),isNaN(a)?nd.INVALID_DATE:new Date(a)))})}prepareParam(a,s){let r;if(da.isRef(a))r=a;else{let o=this.cast(a);if(!this._typeCheck(o))throw new TypeError(`\`${s}\` must be a Date or a value that can be \`cast()\` to a Date`);r=o}return r}min(a,s=Of.min){let r=this.prepareParam(a,"min");return this.test({message:s,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(o){return o>=this.resolve(r)}})}max(a,s=Of.max){let r=this.prepareParam(a,"max");return this.test({message:s,name:"max",exclusive:!0,params:{max:a},skipAbsent:!0,test(o){return o<=this.resolve(r)}})}}nd.INVALID_DATE=hS;function pS(l,a=[]){let s=[],r=new Set,o=new Set(a.map(([d,h])=>`${d}-${h}`));function f(d,h){let p=Ua.split(d)[0];r.add(p),o.has(`${h}-${p}`)||s.push([h,p])}for(const d of Object.keys(l)){let h=l[d];r.add(d),da.isRef(h)&&h.isSibling?f(h.path,d):ed(h)&&"deps"in h&&h.deps.forEach(p=>f(p,d))}return Y2.array(Array.from(r),s).reverse()}function _p(l,a){let s=1/0;return l.some((r,o)=>{var f;if((f=a.path)!=null&&f.includes(r))return s=o,!0}),s}function Ny(l){return(a,s)=>_p(l,a)-_p(l,s)}const yS=(l,a,s)=>{if(typeof l!="string")return l;let r=l;try{r=JSON.parse(l)}catch{}return s.isType(r)?r:l};function gu(l){if("fields"in l){const a={};for(const[s,r]of Object.entries(l.fields))a[s]=gu(r);return l.setFields(a)}if(l.type==="array"){const a=l.optional();return a.innerType&&(a.innerType=gu(a.innerType)),a}return l.type==="tuple"?l.optional().clone({types:l.spec.types.map(gu)}):"optional"in l?l.optional():l}const gS=(l,a)=>{const s=[...Ua.normalizePath(a)];if(s.length===1)return s[0]in l;let r=s.pop(),o=Ua.getter(Ua.join(s),!0)(l);return!!(o&&r in o)};let Tp=l=>Object.prototype.toString.call(l)==="[object Object]";function Rp(l,a){let s=Object.keys(l.fields);return Object.keys(a).filter(r=>s.indexOf(r)===-1)}const vS=Ny([]);function Mi(l){return new Uy(l)}class Uy extends ul{constructor(a){super({type:"object",check(s){return Tp(s)||typeof s=="function"}}),this.fields=Object.create(null),this._sortErrors=vS,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{a&&this.shape(a)})}_cast(a,s={}){var r;let o=super._cast(a,s);if(o===void 0)return this.getDefault(s);if(!this._typeCheck(o))return o;let f=this.fields,d=(r=s.stripUnknown)!=null?r:this.spec.noUnknown,h=[].concat(this._nodes,Object.keys(o).filter(S=>!this._nodes.includes(S))),p={},m=Object.assign({},s,{parent:p,__validating:s.__validating||!1}),g=!1;for(const S of h){let x=f[S],T=S in o;if(x){let E,D=o[S];m.path=(s.path?`${s.path}.`:"")+S,x=x.resolve({value:D,context:s.context,parent:p});let C=x instanceof ul?x.spec:void 0,w=C==null?void 0:C.strict;if(C!=null&&C.strip){g=g||S in o;continue}E=!s.__validating||!w?x.cast(o[S],m):o[S],E!==void 0&&(p[S]=E)}else T&&!d&&(p[S]=o[S]);(T!==S in p||p[S]!==o[S])&&(g=!0)}return g?p:o}_validate(a,s={},r,o){let{from:f=[],originalValue:d=a,recursive:h=this.spec.recursive}=s;s.from=[{schema:this,value:d},...f],s.__validating=!0,s.originalValue=d,super._validate(a,s,r,(p,m)=>{if(!h||!Tp(m)){o(p,m);return}d=d||m;let g=[];for(let S of this._nodes){let x=this.fields[S];!x||da.isRef(x)||g.push(x.asNestedTest({options:s,key:S,parent:m,parentPath:s.path,originalParent:d}))}this.runTests({tests:g,value:m,originalValue:d,options:s},r,S=>{o(S.sort(this._sortErrors).concat(p),m)})})}clone(a){const s=super.clone(a);return s.fields=Object.assign({},this.fields),s._nodes=this._nodes,s._excludedEdges=this._excludedEdges,s._sortErrors=this._sortErrors,s}concat(a){let s=super.concat(a),r=s.fields;for(let[o,f]of Object.entries(this.fields)){const d=r[o];r[o]=d===void 0?f:d}return s.withMutation(o=>o.setFields(r,[...this._excludedEdges,...a._excludedEdges]))}_getDefault(a){if("default"in this.spec)return super._getDefault(a);if(!this._nodes.length)return;let s={};return this._nodes.forEach(r=>{var o;const f=this.fields[r];let d=a;(o=d)!=null&&o.value&&(d=Object.assign({},d,{parent:d.value,value:d.value[r]})),s[r]=f&&"getDefault"in f?f.getDefault(d):void 0}),s}setFields(a,s){let r=this.clone();return r.fields=a,r._nodes=pS(a,s),r._sortErrors=Ny(Object.keys(a)),s&&(r._excludedEdges=s),r}shape(a,s=[]){return this.clone().withMutation(r=>{let o=r._excludedEdges;return s.length&&(Array.isArray(s[0])||(s=[s]),o=[...r._excludedEdges,...s]),r.setFields(Object.assign(r.fields,a),o)})}partial(){const a={};for(const[s,r]of Object.entries(this.fields))a[s]="optional"in r&&r.optional instanceof Function?r.optional():r;return this.setFields(a)}deepPartial(){return gu(this)}pick(a){const s={};for(const r of a)this.fields[r]&&(s[r]=this.fields[r]);return this.setFields(s,this._excludedEdges.filter(([r,o])=>a.includes(r)&&a.includes(o)))}omit(a){const s=[];for(const r of Object.keys(this.fields))a.includes(r)||s.push(r);return this.pick(s)}from(a,s,r){let o=Ua.getter(a,!0);return this.transform(f=>{if(!f)return f;let d=f;return gS(f,a)&&(d=Object.assign({},f),r||delete d[a],d[s]=o(f)),d})}json(){return this.transform(yS)}exact(a){return this.test({name:"exact",exclusive:!0,message:a||yu.exact,test(s){if(s==null)return!0;const r=Rp(this.schema,s);return r.length===0||this.createError({params:{properties:r.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(a=!0,s=yu.noUnknown){typeof a!="boolean"&&(s=a,a=!0);let r=this.test({name:"noUnknown",exclusive:!0,message:s,test(o){if(o==null)return!0;const f=Rp(this.schema,o);return!a||f.length===0||this.createError({params:{unknown:f.join(", ")}})}});return r.spec.noUnknown=a,r}unknown(a=!0,s=yu.noUnknown){return this.noUnknown(!a,s)}transformKeys(a){return this.transform(s=>{if(!s)return s;const r={};for(const o of Object.keys(s))r[a(o)]=s[o];return r})}camelCase(){return this.transformKeys(pf.camelCase)}snakeCase(){return this.transformKeys(pf.snakeCase)}constantCase(){return this.transformKeys(a=>pf.snakeCase(a).toUpperCase())}describe(a){const s=(a?this.resolve(a):this).clone(),r=super.describe(a);r.fields={};for(const[f,d]of Object.entries(s.fields)){var o;let h=a;(o=h)!=null&&o.value&&(h=Object.assign({},h,{parent:h.value,value:h.value[f]})),r.fields[f]=d.describe(h)}return r}}Mi.prototype=Uy.prototype;const Op=(l,a,s)=>{if(l&&"reportValidity"in l){const r=me(s,a);l.setCustomValidity(r&&r.message||""),l.reportValidity()}},My=(l,a)=>{for(const s in a.fields){const r=a.fields[s];r&&r.ref&&"reportValidity"in r.ref?Op(r.ref,s,l):r&&r.refs&&r.refs.forEach(o=>Op(o,s,l))}},bS=(l,a)=>{a.shouldUseNativeValidation&&My(l,a);const s={};for(const r in l){const o=me(a.fields,r),f=Object.assign(l[r]||{},{ref:o&&o.ref});if(SS(a.names||Object.keys(l),r)){const d=Object.assign({},me(s,r));Qe(d,"root",f),Qe(s,r,d)}else Qe(s,r,f)}return s},SS=(l,a)=>{const s=Cp(a);return l.some(r=>Cp(r).match(`^${s}\\.\\d+`))};function Cp(l){return l.replace(/\]|\[/g,"")}function Qs(l,a,s){return s===void 0&&(s={}),function(r,o,f){try{return Promise.resolve(function(d,h){try{var p=(a!=null&&a.context,Promise.resolve(l[s.mode==="sync"?"validateSync":"validate"](r,Object.assign({abortEarly:!1},a,{context:o}))).then(function(m){return f.shouldUseNativeValidation&&My({},f),{values:s.raw?Object.assign({},r):m,errors:{}}}))}catch(m){return h(m)}return p&&p.then?p.then(void 0,h):p}(0,function(d){if(!d.inner)throw d;return{values:{},errors:bS((h=d,p=!f.shouldUseNativeValidation&&f.criteriaMode==="all",(h.inner||[]).reduce(function(m,g){if(m[g.path]||(m[g.path]={message:g.message,type:g.type}),p){var S=m[g.path].types,x=S&&S[g.type];m[g.path]=Sy(g.path,p,m,g.type,x?[].concat(x,g.message):g.message)}return m},{})),f)};var h,p}))}catch(d){return Promise.reject(d)}}}const yf="http://127.0.0.1:8000/auth/",xS="http://127.0.0.1:8000/users/";class ES{async login(a,s){try{const r=await fetch(yf+"jwt/create/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:s})});if(!r.ok)return console.log("login failed!"),!1;const o=await r.json();if(o.access){localStorage.setItem("access_token",o.access),localStorage.setItem("refresh_token",o.refresh),console.log("token stored in the local storage!");const f=await this.getCurrentUser();sessionStorage.setItem("current_user",JSON.stringify(f))}else console.log("No access token in response!");return!0}catch(r){throw console.error("Login failed",r),r}}async resetPassword(a){try{const s=await fetch(yf+"users/reset_password/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a})});return s.ok||console.log("Reset password response:",s),s.status}catch(s){return console.log("Failed to reset password!",s),s}}async resetPasswordConfirm(a,s,r){try{const o=await fetch(yf+"users/reset_password_confirm/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({uid:a,token:s,new_password:r})});return o.ok||console.log("Reset password confirm response:",o.status),o.status}catch(o){return console.log("Failed to reset password confirm!",o),o}}async getCurrentUser(){try{const a=await fetch(xS+"me/",{method:"GET",headers:this.getAuthHeader()});if(!a.ok){const r=await a.json();return console.log("Error data: ",r),null}return await a.json()}catch(a){console.log("Failed to get the current user!",a)}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{"Content-Type":"application/json",Accept:"application/json",Authorization:a?`JWT ${a}`:""}}logout(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),sessionStorage.removeItem("current_user")}}const On=new ES;function ca(){return v.jsxs("div",{className:"loading-ripple",children:[v.jsx("div",{className:"circle-1"}),v.jsx("div",{className:"circle-2"}),v.jsx("div",{className:"circle-3"}),v.jsx("div",{className:"circle-4"})]})}const ku="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%23f5f5f9'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",wS="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Check'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%2020%2020'%3e%3cpath%20d='M8.294%2016.998c-.435%200-.847-.203-1.111-.553L3.61%2011.724a1.392%201.392%200%200%201%20.27-1.951%201.392%201.392%200%200%201%201.953.27l2.351%203.104%205.911-9.492a1.396%201.396%200%200%201%201.921-.445c.653.406.854%201.266.446%201.92L9.478%2016.34a1.39%201.39%200%200%201-1.12.656c-.022.002-.042.002-.064.002z'%20fill='%23fcfcfc'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e";function Hn({message:l,type:a}){let s=a==="success"?wS:ku;return v.jsxs("div",{className:`alert alert-${a}`,children:[v.jsx("img",{src:s,alt:"close-icon"}),v.jsx("p",{children:l})]})}function AS(){const l=$n(),[a,s]=A.useState(!1),[r,o]=A.useState(null);A.useEffect(()=>{r&&setTimeout(()=>{o(!1)},5e3)},[r]);const f=Mi().shape({email:en().required("Must not empty.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:en().required("Must not empty.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=Ui({resolver:Qs(f),mode:"all"}),g=async S=>{s(!0);try{await On.login(S.email,S.password)?(console.log("Login successfully!"),l("/home")):o(!0)}catch(x){console.log("login failed!",x)}finally{s(!1)}};return v.jsxs(v.Fragment,{children:[r&&v.jsx(Hn,{message:"Invalid credentials.",type:"danger"}),v.jsxs("main",{className:"login-page",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:g2,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("img",{src:v2,alt:"logo"}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),p.email&&v.jsx("span",{children:p.email.message}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email",...d("email")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password:"}),p.password&&v.jsx("span",{children:p.password.message}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password",...d("password")})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(ca,{}),a?"Verifying...":"Log In"]})]}),v.jsx("a",{onClick:()=>l("/reset-password"),children:"Forgot Password?"})]})]})]})}const Ru="data:image/svg+xml,%3csvg%20width='200'%20height='200'%20viewBox='0%200%20200%20200'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20x='30'%20y='29'%20width='147'%20height='147'%20rx='73.5'%20fill='%23DEDEDE'/%3e%3crect%20x='7'%20y='145'%20width='39'%20height='39'%20rx='19.5'%20fill='%23FFCEBC'/%3e%3cg%20clip-path='url(%23clip0_303_529)'%3e%3cpath%20d='M63.3333%20139.856C75.5553%20148.745%2087.778%20155.856%20100%20155.856C112.222%20155.856%20124.445%20148.745%20136.667%20139.856C126.592%20136.789%20121.481%20132.212%20121.333%20126.123C121.333%20125.037%20121.341%20123.496%20121.349%20120.602C121.349%20120.142%20121.35%20119.672%20121.352%20119.19C121.372%20111.43%20121.405%20101.19%20121.451%2090.79C131.005%2078.4054%20127.495%2063.886%20124.501%2064.248C120.831%2064.6947%2089.0287%2034.516%2082.9287%2032.9687C76.8287%2031.4214%2061.3333%2036.4167%2058.6667%2050.3334C56%2064.25%2054.8853%2099.3287%2065%20113.333C67.8782%20117.319%2072.4116%20118.878%2078.6%20118.01C78.6087%20120.923%2078.626%20122.557%2078.6667%20125.856C78.5873%20132.222%2073.4767%20136.793%2063.3333%20139.856Z'%20fill='url(%23paint0_linear_303_529)'/%3e%3cpath%20d='M78.6667%20118C93.3334%20116.333%20102.667%20110%20102.667%20110C102.667%20110%2092.0001%20123.333%2078.6667%20126V118Z'%20fill='%23FC9F6A'/%3e%3cpath%20d='M154%2091C163%2081.8752%20172.252%2055.2576%20167.5%2036.5C158%20-0.999866%2093.1667%200.500212%2073.5001%206.50021C60.2821%2010.5329%2047.0001%2021.5%2045%2025.0002C36.0559%2040.6526%2047.0108%2054.3335%2055.8001%2057.4555C63.7775%2060.2888%2077.2668%2063.1221%20100.204%2060.4555C104.301%2059.9788%20103.443%2072.5275%20105.633%2073.8842C108.917%2075.9195%20111.467%2063.1221%20120.472%2066.9648C129.477%2070.8075%20124.133%2088.6248%20114.467%2088.6248C111.133%2088.6248%20109.467%2097.7895%20118.467%20102.123C125%20105.333%20145.5%2098%20154%2091Z'%20fill='url(%23paint1_linear_303_529)'/%3e%3cpath%20d='M170%20160.205C176.347%20173.11%20180%20202.872%20180%20202.872H20C20%20202.872%2023.6547%20173.107%2030%20160.205C36.3453%20147.304%2073.4%20135.338%2073.4%20135.338C93.8753%20143.333%20106.405%20143.333%20126.581%20135.333C126.581%20135.333%20163.653%20147.301%20170%20160.205Z'%20fill='url(%23paint2_linear_303_529)'/%3e%3cpath%20d='M100%20136.661L93.3334%20148.667L72.6667%20136L78.6667%20127.333L100%20135.214L121.333%20127.333L127.333%20136L106.667%20148.667L100%20136.661Z'%20fill='white'/%3e%3c/g%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_303_529'%20x1='96.7478'%20y1='32.6981'%20x2='96.7478'%20y2='155.856'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F7B186'/%3e%3cstop%20offset='1'%20stop-color='%23FFC299'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_303_529'%20x1='92.9604'%20y1='22.2586'%20x2='92.9604'%20y2='102.912'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%231D0024'/%3e%3cstop%20offset='1'%20stop-color='%23100014'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_303_529'%20x1='100'%20y1='135.333'%20x2='100'%20y2='202.872'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%231D0024'/%3e%3cstop%20offset='1'%20stop-color='%23100014'/%3e%3c/linearGradient%3e%3cclipPath%20id='clip0_303_529'%3e%3crect%20width='200'%20height='200'%20rx='100'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",_S="data:image/svg+xml,%3csvg%20width='33'%20height='34'%20viewBox='0%200%2033%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='20.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M18.5%2012L21.5%2016'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M26.5%209.5L23.5%2015.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3ccircle%20cx='17'%20cy='10.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='23'%20cy='17.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='27'%20cy='7.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M5.5%2024L10%2021.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M13%2018.5L16%2012.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M27%2024.5L31.5326%2028.5793C31.7717%2028.7945%2031.7483%2029.1762%2031.4849%2029.3606L27%2032.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M30%2028.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M1%206.5L5.07934%201.9674C5.2945%201.72833%205.67616%201.75165%205.8606%202.01515L9%206.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M5%203.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3c/svg%3e",Ri=Math.min,Ma=Math.max,Ou=Math.round,du=Math.floor,rl=l=>({x:l,y:l}),TS={left:"right",right:"left",bottom:"top",top:"bottom"},RS={start:"end",end:"start"};function jf(l,a,s){return Ma(l,Ri(a,s))}function Ks(l,a){return typeof l=="function"?l(a):l}function ka(l){return l.split("-")[0]}function Js(l){return l.split("-")[1]}function Ly(l){return l==="x"?"y":"x"}function ld(l){return l==="y"?"height":"width"}function Oi(l){return["top","bottom"].includes(ka(l))?"y":"x"}function ad(l){return Ly(Oi(l))}function OS(l,a,s){s===void 0&&(s=!1);const r=Js(l),o=ad(l),f=ld(o);let d=o==="x"?r===(s?"end":"start")?"right":"left":r==="start"?"bottom":"top";return a.reference[f]>a.floating[f]&&(d=Cu(d)),[d,Cu(d)]}function CS(l){const a=Cu(l);return[Df(l),a,Df(a)]}function Df(l){return l.replace(/start|end/g,a=>RS[a])}function jS(l,a,s){const r=["left","right"],o=["right","left"],f=["top","bottom"],d=["bottom","top"];switch(l){case"top":case"bottom":return s?a?o:r:a?r:o;case"left":case"right":return a?f:d;default:return[]}}function DS(l,a,s,r){const o=Js(l);let f=jS(ka(l),s==="start",r);return o&&(f=f.map(d=>d+"-"+o),a&&(f=f.concat(f.map(Df)))),f}function Cu(l){return l.replace(/left|right|bottom|top/g,a=>TS[a])}function NS(l){return{top:0,right:0,bottom:0,left:0,...l}}function zy(l){return typeof l!="number"?NS(l):{top:l,right:l,bottom:l,left:l}}function ju(l){const{x:a,y:s,width:r,height:o}=l;return{width:r,height:o,top:s,left:a,right:a+r,bottom:s+o,x:a,y:s}}function jp(l,a,s){let{reference:r,floating:o}=l;const f=Oi(a),d=ad(a),h=ld(d),p=ka(a),m=f==="y",g=r.x+r.width/2-o.width/2,S=r.y+r.height/2-o.height/2,x=r[h]/2-o[h]/2;let T;switch(p){case"top":T={x:g,y:r.y-o.height};break;case"bottom":T={x:g,y:r.y+r.height};break;case"right":T={x:r.x+r.width,y:S};break;case"left":T={x:r.x-o.width,y:S};break;default:T={x:r.x,y:r.y}}switch(Js(a)){case"start":T[d]-=x*(s&&m?-1:1);break;case"end":T[d]+=x*(s&&m?-1:1);break}return T}const US=async(l,a,s)=>{const{placement:r="bottom",strategy:o="absolute",middleware:f=[],platform:d}=s,h=f.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(a));let m=await d.getElementRects({reference:l,floating:a,strategy:o}),{x:g,y:S}=jp(m,r,p),x=r,T={},E=0;for(let D=0;D<h.length;D++){const{name:C,fn:w}=h[D],{x:j,y:M,data:G,reset:H}=await w({x:g,y:S,initialPlacement:r,placement:x,strategy:o,middlewareData:T,rects:m,platform:d,elements:{reference:l,floating:a}});g=j??g,S=M??S,T={...T,[C]:{...T[C],...G}},H&&E<=50&&(E++,typeof H=="object"&&(H.placement&&(x=H.placement),H.rects&&(m=H.rects===!0?await d.getElementRects({reference:l,floating:a,strategy:o}):H.rects),{x:g,y:S}=jp(m,x,p)),D=-1)}return{x:g,y:S,placement:x,strategy:o,middlewareData:T}};async function ky(l,a){var s;a===void 0&&(a={});const{x:r,y:o,platform:f,rects:d,elements:h,strategy:p}=l,{boundary:m="clippingAncestors",rootBoundary:g="viewport",elementContext:S="floating",altBoundary:x=!1,padding:T=0}=Ks(a,l),E=zy(T),C=h[x?S==="floating"?"reference":"floating":S],w=ju(await f.getClippingRect({element:(s=await(f.isElement==null?void 0:f.isElement(C)))==null||s?C:C.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:m,rootBoundary:g,strategy:p})),j=S==="floating"?{x:r,y:o,width:d.floating.width,height:d.floating.height}:d.reference,M=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),G=await(f.isElement==null?void 0:f.isElement(M))?await(f.getScale==null?void 0:f.getScale(M))||{x:1,y:1}:{x:1,y:1},H=ju(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:j,offsetParent:M,strategy:p}):j);return{top:(w.top-H.top+E.top)/G.y,bottom:(H.bottom-w.bottom+E.bottom)/G.y,left:(w.left-H.left+E.left)/G.x,right:(H.right-w.right+E.right)/G.x}}const MS=l=>({name:"arrow",options:l,async fn(a){const{x:s,y:r,placement:o,rects:f,platform:d,elements:h,middlewareData:p}=a,{element:m,padding:g=0}=Ks(l,a)||{};if(m==null)return{};const S=zy(g),x={x:s,y:r},T=ad(o),E=ld(T),D=await d.getDimensions(m),C=T==="y",w=C?"top":"left",j=C?"bottom":"right",M=C?"clientHeight":"clientWidth",G=f.reference[E]+f.reference[T]-x[T]-f.floating[E],H=x[T]-f.reference[T],te=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let I=te?te[M]:0;(!I||!await(d.isElement==null?void 0:d.isElement(te)))&&(I=h.floating[M]||f.floating[E]);const K=G/2-H/2,ne=I/2-D[E]/2-1,xe=Ri(S[w],ne),ce=Ri(S[j],ne),W=xe,re=I-D[E]-ce,fe=I/2-D[E]/2+K,ve=jf(W,fe,re),q=!p.arrow&&Js(o)!=null&&fe!==ve&&f.reference[E]/2-(fe<W?xe:ce)-D[E]/2<0,P=q?fe<W?fe-W:fe-re:0;return{[T]:x[T]+P,data:{[T]:ve,centerOffset:fe-ve-P,...q&&{alignmentOffset:P}},reset:q}}}),LS=function(l){return l===void 0&&(l={}),{name:"flip",options:l,async fn(a){var s,r;const{placement:o,middlewareData:f,rects:d,initialPlacement:h,platform:p,elements:m}=a,{mainAxis:g=!0,crossAxis:S=!0,fallbackPlacements:x,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:D=!0,...C}=Ks(l,a);if((s=f.arrow)!=null&&s.alignmentOffset)return{};const w=ka(o),j=Oi(h),M=ka(h)===h,G=await(p.isRTL==null?void 0:p.isRTL(m.floating)),H=x||(M||!D?[Cu(h)]:CS(h)),te=E!=="none";!x&&te&&H.push(...DS(h,D,E,G));const I=[h,...H],K=await ky(a,C),ne=[];let xe=((r=f.flip)==null?void 0:r.overflows)||[];if(g&&ne.push(K[w]),S){const fe=OS(o,d,G);ne.push(K[fe[0]],K[fe[1]])}if(xe=[...xe,{placement:o,overflows:ne}],!ne.every(fe=>fe<=0)){var ce,W;const fe=(((ce=f.flip)==null?void 0:ce.index)||0)+1,ve=I[fe];if(ve)return{data:{index:fe,overflows:xe},reset:{placement:ve}};let q=(W=xe.filter(P=>P.overflows[0]<=0).sort((P,le)=>P.overflows[1]-le.overflows[1])[0])==null?void 0:W.placement;if(!q)switch(T){case"bestFit":{var re;const P=(re=xe.filter(le=>{if(te){const _e=Oi(le.placement);return _e===j||_e==="y"}return!0}).map(le=>[le.placement,le.overflows.filter(_e=>_e>0).reduce((_e,R)=>_e+R,0)]).sort((le,_e)=>le[1]-_e[1])[0])==null?void 0:re[0];P&&(q=P);break}case"initialPlacement":q=h;break}if(o!==q)return{reset:{placement:q}}}return{}}}};async function zS(l,a){const{placement:s,platform:r,elements:o}=l,f=await(r.isRTL==null?void 0:r.isRTL(o.floating)),d=ka(s),h=Js(s),p=Oi(s)==="y",m=["left","top"].includes(d)?-1:1,g=f&&p?-1:1,S=Ks(a,l);let{mainAxis:x,crossAxis:T,alignmentAxis:E}=typeof S=="number"?{mainAxis:S,crossAxis:0,alignmentAxis:null}:{mainAxis:S.mainAxis||0,crossAxis:S.crossAxis||0,alignmentAxis:S.alignmentAxis};return h&&typeof E=="number"&&(T=h==="end"?E*-1:E),p?{x:T*g,y:x*m}:{x:x*m,y:T*g}}const kS=function(l){return l===void 0&&(l=0),{name:"offset",options:l,async fn(a){var s,r;const{x:o,y:f,placement:d,middlewareData:h}=a,p=await zS(a,l);return d===((s=h.offset)==null?void 0:s.placement)&&(r=h.arrow)!=null&&r.alignmentOffset?{}:{x:o+p.x,y:f+p.y,data:{...p,placement:d}}}}},BS=function(l){return l===void 0&&(l={}),{name:"shift",options:l,async fn(a){const{x:s,y:r,placement:o}=a,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:C=>{let{x:w,y:j}=C;return{x:w,y:j}}},...p}=Ks(l,a),m={x:s,y:r},g=await ky(a,p),S=Oi(ka(o)),x=Ly(S);let T=m[x],E=m[S];if(f){const C=x==="y"?"top":"left",w=x==="y"?"bottom":"right",j=T+g[C],M=T-g[w];T=jf(j,T,M)}if(d){const C=S==="y"?"top":"left",w=S==="y"?"bottom":"right",j=E+g[C],M=E-g[w];E=jf(j,E,M)}const D=h.fn({...a,[x]:T,[S]:E});return{...D,data:{x:D.x-s,y:D.y-r,enabled:{[x]:f,[S]:d}}}}}};function Bu(){return typeof window<"u"}function Li(l){return By(l)?(l.nodeName||"").toLowerCase():"#document"}function mn(l){var a;return(l==null||(a=l.ownerDocument)==null?void 0:a.defaultView)||window}function fl(l){var a;return(a=(By(l)?l.ownerDocument:l.document)||window.document)==null?void 0:a.documentElement}function By(l){return Bu()?l instanceof Node||l instanceof mn(l).Node:!1}function Vn(l){return Bu()?l instanceof Element||l instanceof mn(l).Element:!1}function ol(l){return Bu()?l instanceof HTMLElement||l instanceof mn(l).HTMLElement:!1}function Dp(l){return!Bu()||typeof ShadowRoot>"u"?!1:l instanceof ShadowRoot||l instanceof mn(l).ShadowRoot}function Ps(l){const{overflow:a,overflowX:s,overflowY:r,display:o}=Fn(l);return/auto|scroll|overlay|hidden|clip/.test(a+r+s)&&!["inline","contents"].includes(o)}function HS(l){return["table","td","th"].includes(Li(l))}function Hu(l){return[":popover-open",":modal"].some(a=>{try{return l.matches(a)}catch{return!1}})}function id(l){const a=sd(),s=Vn(l)?Fn(l):l;return["transform","translate","scale","rotate","perspective"].some(r=>s[r]?s[r]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(s.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(s.contain||"").includes(r))}function qS(l){let a=fa(l);for(;ol(a)&&!Ci(a);){if(id(a))return a;if(Hu(a))return null;a=fa(a)}return null}function sd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ci(l){return["html","body","#document"].includes(Li(l))}function Fn(l){return mn(l).getComputedStyle(l)}function qu(l){return Vn(l)?{scrollLeft:l.scrollLeft,scrollTop:l.scrollTop}:{scrollLeft:l.scrollX,scrollTop:l.scrollY}}function fa(l){if(Li(l)==="html")return l;const a=l.assignedSlot||l.parentNode||Dp(l)&&l.host||fl(l);return Dp(a)?a.host:a}function Hy(l){const a=fa(l);return Ci(a)?l.ownerDocument?l.ownerDocument.body:l.body:ol(a)&&Ps(a)?a:Hy(a)}function Ys(l,a,s){var r;a===void 0&&(a=[]),s===void 0&&(s=!0);const o=Hy(l),f=o===((r=l.ownerDocument)==null?void 0:r.body),d=mn(o);if(f){const h=Nf(d);return a.concat(d,d.visualViewport||[],Ps(o)?o:[],h&&s?Ys(h):[])}return a.concat(o,Ys(o,[],s))}function Nf(l){return l.parent&&Object.getPrototypeOf(l.parent)?l.frameElement:null}function qy(l){const a=Fn(l);let s=parseFloat(a.width)||0,r=parseFloat(a.height)||0;const o=ol(l),f=o?l.offsetWidth:s,d=o?l.offsetHeight:r,h=Ou(s)!==f||Ou(r)!==d;return h&&(s=f,r=d),{width:s,height:r,$:h}}function rd(l){return Vn(l)?l:l.contextElement}function Ti(l){const a=rd(l);if(!ol(a))return rl(1);const s=a.getBoundingClientRect(),{width:r,height:o,$:f}=qy(a);let d=(f?Ou(s.width):s.width)/r,h=(f?Ou(s.height):s.height)/o;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const VS=rl(0);function Vy(l){const a=mn(l);return!sd()||!a.visualViewport?VS:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function FS(l,a,s){return a===void 0&&(a=!1),!s||a&&s!==mn(l)?!1:a}function Ba(l,a,s,r){a===void 0&&(a=!1),s===void 0&&(s=!1);const o=l.getBoundingClientRect(),f=rd(l);let d=rl(1);a&&(r?Vn(r)&&(d=Ti(r)):d=Ti(l));const h=FS(f,s,r)?Vy(f):rl(0);let p=(o.left+h.x)/d.x,m=(o.top+h.y)/d.y,g=o.width/d.x,S=o.height/d.y;if(f){const x=mn(f),T=r&&Vn(r)?mn(r):r;let E=x,D=Nf(E);for(;D&&r&&T!==E;){const C=Ti(D),w=D.getBoundingClientRect(),j=Fn(D),M=w.left+(D.clientLeft+parseFloat(j.paddingLeft))*C.x,G=w.top+(D.clientTop+parseFloat(j.paddingTop))*C.y;p*=C.x,m*=C.y,g*=C.x,S*=C.y,p+=M,m+=G,E=mn(D),D=Nf(E)}}return ju({width:g,height:S,x:p,y:m})}function ud(l,a){const s=qu(l).scrollLeft;return a?a.left+s:Ba(fl(l)).left+s}function Fy(l,a,s){s===void 0&&(s=!1);const r=l.getBoundingClientRect(),o=r.left+a.scrollLeft-(s?0:ud(l,r)),f=r.top+a.scrollTop;return{x:o,y:f}}function YS(l){let{elements:a,rect:s,offsetParent:r,strategy:o}=l;const f=o==="fixed",d=fl(r),h=a?Hu(a.floating):!1;if(r===d||h&&f)return s;let p={scrollLeft:0,scrollTop:0},m=rl(1);const g=rl(0),S=ol(r);if((S||!S&&!f)&&((Li(r)!=="body"||Ps(d))&&(p=qu(r)),ol(r))){const T=Ba(r);m=Ti(r),g.x=T.x+r.clientLeft,g.y=T.y+r.clientTop}const x=d&&!S&&!f?Fy(d,p,!0):rl(0);return{width:s.width*m.x,height:s.height*m.y,x:s.x*m.x-p.scrollLeft*m.x+g.x+x.x,y:s.y*m.y-p.scrollTop*m.y+g.y+x.y}}function $S(l){return Array.from(l.getClientRects())}function GS(l){const a=fl(l),s=qu(l),r=l.ownerDocument.body,o=Ma(a.scrollWidth,a.clientWidth,r.scrollWidth,r.clientWidth),f=Ma(a.scrollHeight,a.clientHeight,r.scrollHeight,r.clientHeight);let d=-s.scrollLeft+ud(l);const h=-s.scrollTop;return Fn(r).direction==="rtl"&&(d+=Ma(a.clientWidth,r.clientWidth)-o),{width:o,height:f,x:d,y:h}}function XS(l,a){const s=mn(l),r=fl(l),o=s.visualViewport;let f=r.clientWidth,d=r.clientHeight,h=0,p=0;if(o){f=o.width,d=o.height;const m=sd();(!m||m&&a==="fixed")&&(h=o.offsetLeft,p=o.offsetTop)}return{width:f,height:d,x:h,y:p}}function ZS(l,a){const s=Ba(l,!0,a==="fixed"),r=s.top+l.clientTop,o=s.left+l.clientLeft,f=ol(l)?Ti(l):rl(1),d=l.clientWidth*f.x,h=l.clientHeight*f.y,p=o*f.x,m=r*f.y;return{width:d,height:h,x:p,y:m}}function Np(l,a,s){let r;if(a==="viewport")r=XS(l,s);else if(a==="document")r=GS(fl(l));else if(Vn(a))r=ZS(a,s);else{const o=Vy(l);r={x:a.x-o.x,y:a.y-o.y,width:a.width,height:a.height}}return ju(r)}function Yy(l,a){const s=fa(l);return s===a||!Vn(s)||Ci(s)?!1:Fn(s).position==="fixed"||Yy(s,a)}function QS(l,a){const s=a.get(l);if(s)return s;let r=Ys(l,[],!1).filter(h=>Vn(h)&&Li(h)!=="body"),o=null;const f=Fn(l).position==="fixed";let d=f?fa(l):l;for(;Vn(d)&&!Ci(d);){const h=Fn(d),p=id(d);!p&&h.position==="fixed"&&(o=null),(f?!p&&!o:!p&&h.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ps(d)&&!p&&Yy(l,d))?r=r.filter(g=>g!==d):o=h,d=fa(d)}return a.set(l,r),r}function KS(l){let{element:a,boundary:s,rootBoundary:r,strategy:o}=l;const d=[...s==="clippingAncestors"?Hu(a)?[]:QS(a,this._c):[].concat(s),r],h=d[0],p=d.reduce((m,g)=>{const S=Np(a,g,o);return m.top=Ma(S.top,m.top),m.right=Ri(S.right,m.right),m.bottom=Ri(S.bottom,m.bottom),m.left=Ma(S.left,m.left),m},Np(a,h,o));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function JS(l){const{width:a,height:s}=qy(l);return{width:a,height:s}}function PS(l,a,s){const r=ol(a),o=fl(a),f=s==="fixed",d=Ba(l,!0,f,a);let h={scrollLeft:0,scrollTop:0};const p=rl(0);if(r||!r&&!f)if((Li(a)!=="body"||Ps(o))&&(h=qu(a)),r){const x=Ba(a,!0,f,a);p.x=x.x+a.clientLeft,p.y=x.y+a.clientTop}else o&&(p.x=ud(o));const m=o&&!r&&!f?Fy(o,h):rl(0),g=d.left+h.scrollLeft-p.x-m.x,S=d.top+h.scrollTop-p.y-m.y;return{x:g,y:S,width:d.width,height:d.height}}function gf(l){return Fn(l).position==="static"}function Up(l,a){if(!ol(l)||Fn(l).position==="fixed")return null;if(a)return a(l);let s=l.offsetParent;return fl(l)===s&&(s=s.ownerDocument.body),s}function $y(l,a){const s=mn(l);if(Hu(l))return s;if(!ol(l)){let o=fa(l);for(;o&&!Ci(o);){if(Vn(o)&&!gf(o))return o;o=fa(o)}return s}let r=Up(l,a);for(;r&&HS(r)&&gf(r);)r=Up(r,a);return r&&Ci(r)&&gf(r)&&!id(r)?s:r||qS(l)||s}const WS=async function(l){const a=this.getOffsetParent||$y,s=this.getDimensions,r=await s(l.floating);return{reference:PS(l.reference,await a(l.floating),l.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function IS(l){return Fn(l).direction==="rtl"}const ex={convertOffsetParentRelativeRectToViewportRelativeRect:YS,getDocumentElement:fl,getClippingRect:KS,getOffsetParent:$y,getElementRects:WS,getClientRects:$S,getDimensions:JS,getScale:Ti,isElement:Vn,isRTL:IS};function Gy(l,a){return l.x===a.x&&l.y===a.y&&l.width===a.width&&l.height===a.height}function tx(l,a){let s=null,r;const o=fl(l);function f(){var h;clearTimeout(r),(h=s)==null||h.disconnect(),s=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),f();const m=l.getBoundingClientRect(),{left:g,top:S,width:x,height:T}=m;if(h||a(),!x||!T)return;const E=du(S),D=du(o.clientWidth-(g+x)),C=du(o.clientHeight-(S+T)),w=du(g),M={rootMargin:-E+"px "+-D+"px "+-C+"px "+-w+"px",threshold:Ma(0,Ri(1,p))||1};let G=!0;function H(te){const I=te[0].intersectionRatio;if(I!==p){if(!G)return d();I?d(!1,I):r=setTimeout(()=>{d(!1,1e-7)},1e3)}I===1&&!Gy(m,l.getBoundingClientRect())&&d(),G=!1}try{s=new IntersectionObserver(H,{...M,root:o.ownerDocument})}catch{s=new IntersectionObserver(H,M)}s.observe(l)}return d(!0),f}function nx(l,a,s,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=r,m=rd(l),g=o||f?[...m?Ys(m):[],...Ys(a)]:[];g.forEach(w=>{o&&w.addEventListener("scroll",s,{passive:!0}),f&&w.addEventListener("resize",s)});const S=m&&h?tx(m,s):null;let x=-1,T=null;d&&(T=new ResizeObserver(w=>{let[j]=w;j&&j.target===m&&T&&(T.unobserve(a),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var M;(M=T)==null||M.observe(a)})),s()}),m&&!p&&T.observe(m),T.observe(a));let E,D=p?Ba(l):null;p&&C();function C(){const w=Ba(l);D&&!Gy(D,w)&&s(),D=w,E=requestAnimationFrame(C)}return s(),()=>{var w;g.forEach(j=>{o&&j.removeEventListener("scroll",s),f&&j.removeEventListener("resize",s)}),S==null||S(),(w=T)==null||w.disconnect(),T=null,p&&cancelAnimationFrame(E)}}const lx=kS,ax=BS,ix=LS,sx=MS,Mp=(l,a,s)=>{const r=new Map,o={platform:ex,...s},f={...o.platform,_c:r};return US(l,a,{...o,platform:f})};var vf={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Lp;function rx(){return Lp||(Lp=1,function(l){(function(){var a={}.hasOwnProperty;function s(){for(var f="",d=0;d<arguments.length;d++){var h=arguments[d];h&&(f=o(f,r(h)))}return f}function r(f){if(typeof f=="string"||typeof f=="number")return f;if(typeof f!="object")return"";if(Array.isArray(f))return s.apply(null,f);if(f.toString!==Object.prototype.toString&&!f.toString.toString().includes("[native code]"))return f.toString();var d="";for(var h in f)a.call(f,h)&&f[h]&&(d=o(d,h));return d}function o(f,d){return d?f?f+" "+d:f+d:f}l.exports?(s.default=s,l.exports=s):window.classNames=s})()}(vf)),vf.exports}var ux=rx();const Uf=Vf(ux);var zp={};const ox="react-tooltip-core-styles",cx="react-tooltip-base-styles",kp={core:!1,base:!1};function Bp({css:l,id:a=cx,type:s="base",ref:r}){var o,f;if(!l||typeof document>"u"||kp[s]||s==="core"&&typeof process<"u"&&(!((o=process==null?void 0:zp)===null||o===void 0)&&o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||s!=="base"&&typeof process<"u"&&(!((f=process==null?void 0:zp)===null||f===void 0)&&f.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;s==="core"&&(a=ox),r||(r={});const{insertAt:d}=r;if(document.getElementById(a))return;const h=document.head||document.getElementsByTagName("head")[0],p=document.createElement("style");p.id=a,p.type="text/css",d==="top"&&h.firstChild?h.insertBefore(p,h.firstChild):h.appendChild(p),p.styleSheet?p.styleSheet.cssText=l:p.appendChild(document.createTextNode(l)),kp[s]=!0}const Hp=async({elementReference:l=null,tooltipReference:a=null,tooltipArrowReference:s=null,place:r="top",offset:o=10,strategy:f="absolute",middlewares:d=[lx(Number(o)),ix({fallbackAxisSideDirection:"start"}),ax({padding:5})],border:h})=>{if(!l)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};if(a===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};const p=d;return s?(p.push(sx({element:s,padding:5})),Mp(l,a,{placement:r,strategy:f,middleware:p}).then(({x:m,y:g,placement:S,middlewareData:x})=>{var T,E;const D={left:`${m}px`,top:`${g}px`,border:h},{x:C,y:w}=(T=x.arrow)!==null&&T!==void 0?T:{x:0,y:0},j=(E={top:"bottom",right:"left",bottom:"top",left:"right"}[S.split("-")[0]])!==null&&E!==void 0?E:"bottom",M=h&&{borderBottom:h,borderRight:h};let G=0;if(h){const H=`${h}`.match(/(\d+)px/);G=H!=null&&H[1]?Number(H[1]):1}return{tooltipStyles:D,tooltipArrowStyles:{left:C!=null?`${C}px`:"",top:w!=null?`${w}px`:"",right:"",bottom:"",...M,[j]:`-${4+G}px`},place:S}})):Mp(l,a,{placement:"bottom",strategy:f,middleware:p}).then(({x:m,y:g,placement:S})=>({tooltipStyles:{left:`${m}px`,top:`${g}px`},tooltipArrowStyles:{},place:S}))},qp=(l,a)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(l,a),Vp=(l,a,s)=>{let r=null;const o=function(...f){const d=()=>{r=null};!r&&(l.apply(this,f),r=setTimeout(d,a))};return o.cancel=()=>{r&&(clearTimeout(r),r=null)},o},Fp=l=>l!==null&&!Array.isArray(l)&&typeof l=="object",Mf=(l,a)=>{if(l===a)return!0;if(Array.isArray(l)&&Array.isArray(a))return l.length===a.length&&l.every((o,f)=>Mf(o,a[f]));if(Array.isArray(l)!==Array.isArray(a))return!1;if(!Fp(l)||!Fp(a))return l===a;const s=Object.keys(l),r=Object.keys(a);return s.length===r.length&&s.every(o=>Mf(l[o],a[o]))},fx=l=>{if(!(l instanceof HTMLElement||l instanceof SVGElement))return!1;const a=getComputedStyle(l);return["overflow","overflow-x","overflow-y"].some(s=>{const r=a.getPropertyValue(s);return r==="auto"||r==="scroll"})},Yp=l=>{if(!l)return null;let a=l.parentElement;for(;a;){if(fx(a))return a;a=a.parentElement}return document.scrollingElement||document.documentElement},dx=typeof window<"u"?A.useLayoutEffect:A.useEffect,Rn=l=>{l.current&&(clearTimeout(l.current),l.current=null)},hx="DEFAULT_TOOLTIP_ID",mx={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},px=A.createContext({getTooltipData:()=>mx});function Xy(l=hx){return A.useContext(px).getTooltipData(l)}var Ai={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},bf={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const yx=({forwardRef:l,id:a,className:s,classNameArrow:r,variant:o="dark",anchorId:f,anchorSelect:d,place:h="top",offset:p=10,events:m=["hover"],openOnClick:g=!1,positionStrategy:S="absolute",middlewares:x,wrapper:T,delayShow:E=0,delayHide:D=0,float:C=!1,hidden:w=!1,noArrow:j=!1,clickable:M=!1,closeOnEsc:G=!1,closeOnScroll:H=!1,closeOnResize:te=!1,openEvents:I,closeEvents:K,globalCloseEvents:ne,imperativeModeOnly:xe,style:ce,position:W,afterShow:re,afterHide:fe,disableTooltip:ve,content:q,contentWrapperRef:P,isOpen:le,defaultIsOpen:_e=!1,setIsOpen:R,activeAnchor:Y,setActiveAnchor:ue,border:ae,opacity:pe,arrowColor:je,role:we="tooltip"})=>{var it;const Re=A.useRef(null),St=A.useRef(null),At=A.useRef(null),jt=A.useRef(null),pn=A.useRef(null),[Gt,Xn]=A.useState({tooltipStyles:{},tooltipArrowStyles:{},place:h}),[Dt,dl]=A.useState(!1),[ut,jn]=A.useState(!1),[Ye,hl]=A.useState(null),ln=A.useRef(!1),O=A.useRef(null),{anchorRefs:k,setActiveAnchor:$}=Xy(a),oe=A.useRef(!1),[ee,J]=A.useState([]),ie=A.useRef(!1),be=g||m.includes("click"),We=be||(I==null?void 0:I.click)||(I==null?void 0:I.dblclick)||(I==null?void 0:I.mousedown),tt=I?{...I}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!I&&be&&Object.assign(tt,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const Dn=K?{...K}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!K&&be&&Object.assign(Dn,{mouseleave:!1,blur:!1,mouseout:!1});const pt=ne?{...ne}:{escape:G||!1,scroll:H||!1,resize:te||!1,clickOutsideAnchor:We||!1};xe&&(Object.assign(tt,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Dn,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(pt,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),dx(()=>(ie.current=!0,()=>{ie.current=!1}),[]);const qe=se=>{ie.current&&(se&&jn(!0),setTimeout(()=>{ie.current&&(R==null||R(se),le===void 0&&dl(se))},10))};A.useEffect(()=>{if(le===void 0)return()=>null;le&&jn(!0);const se=setTimeout(()=>{dl(le)},10);return()=>{clearTimeout(se)}},[le]),A.useEffect(()=>{if(Dt!==ln.current)if(Rn(pn),ln.current=Dt,Dt)re==null||re();else{const se=(de=>{const ye=de.match(/^([\d.]+)(ms|s)$/);if(!ye)return 0;const[,Ie,yt]=ye;return Number(Ie)*(yt==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));pn.current=setTimeout(()=>{jn(!1),hl(null),fe==null||fe()},se+25)}},[Dt]);const Xt=se=>{Xn(de=>Mf(de,se)?de:se)},an=(se=E)=>{Rn(At),ut?qe(!0):At.current=setTimeout(()=>{qe(!0)},se)},Zt=(se=D)=>{Rn(jt),jt.current=setTimeout(()=>{oe.current||qe(!1)},se)},Zn=se=>{var de;if(!se)return;const ye=(de=se.currentTarget)!==null&&de!==void 0?de:se.target;if(!(ye!=null&&ye.isConnected))return ue(null),void $({current:null});E?an():qe(!0),ue(ye),$({current:ye}),Rn(jt)},ml=()=>{M?Zt(D||100):D?Zt():qe(!1),Rn(At)},pl=({x:se,y:de})=>{var ye;const Ie={getBoundingClientRect:()=>({x:se,y:de,width:0,height:0,top:de,left:se,right:se,bottom:de})};Hp({place:(ye=Ye==null?void 0:Ye.place)!==null&&ye!==void 0?ye:h,offset:p,elementReference:Ie,tooltipReference:Re.current,tooltipArrowReference:St.current,strategy:S,middlewares:x,border:ae}).then(yt=>{Xt(yt)})},Qn=se=>{if(!se)return;const de=se,ye={x:de.clientX,y:de.clientY};pl(ye),O.current=ye},Nn=se=>{var de;if(!Dt)return;const ye=se.target;ye.isConnected&&(!((de=Re.current)===null||de===void 0)&&de.contains(ye)||[document.querySelector(`[id='${f}']`),...ee].some(Ie=>Ie==null?void 0:Ie.contains(ye))||(qe(!1),Rn(At)))},qa=Vp(Zn,50),ot=Vp(ml,50),qt=se=>{ot.cancel(),qa(se)},Se=()=>{qa.cancel(),ot()},Ne=A.useCallback(()=>{var se,de;const ye=(se=Ye==null?void 0:Ye.position)!==null&&se!==void 0?se:W;ye?pl(ye):C?O.current&&pl(O.current):Y!=null&&Y.isConnected&&Hp({place:(de=Ye==null?void 0:Ye.place)!==null&&de!==void 0?de:h,offset:p,elementReference:Y,tooltipReference:Re.current,tooltipArrowReference:St.current,strategy:S,middlewares:x,border:ae}).then(Ie=>{ie.current&&Xt(Ie)})},[Dt,Y,q,ce,h,Ye==null?void 0:Ye.place,p,S,W,Ye==null?void 0:Ye.position,C]);A.useEffect(()=>{var se,de;const ye=new Set(k);ee.forEach(ze=>{ve!=null&&ve(ze)||ye.add({current:ze})});const Ie=document.querySelector(`[id='${f}']`);Ie&&!(ve!=null&&ve(Ie))&&ye.add({current:Ie});const yt=()=>{qe(!1)},yn=Yp(Y),gn=Yp(Re.current);pt.scroll&&(window.addEventListener("scroll",yt),yn==null||yn.addEventListener("scroll",yt),gn==null||gn.addEventListener("scroll",yt));let _t=null;pt.resize?window.addEventListener("resize",yt):Y&&Re.current&&(_t=nx(Y,Re.current,Ne,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const Tt=ze=>{ze.key==="Escape"&&qe(!1)};pt.escape&&window.addEventListener("keydown",Tt),pt.clickOutsideAnchor&&window.addEventListener("click",Nn);const $e=[],sn=ze=>!!(ze!=null&&ze.target&&(Y!=null&&Y.contains(ze.target))),kl=ze=>{Dt&&sn(ze)||Zn(ze)},ha=ze=>{Dt&&sn(ze)&&ml()},gl=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],ct=["click","dblclick","mousedown","mouseup"];Object.entries(tt).forEach(([ze,Un])=>{Un&&(gl.includes(ze)?$e.push({event:ze,listener:qt}):ct.includes(ze)&&$e.push({event:ze,listener:kl}))}),Object.entries(Dn).forEach(([ze,Un])=>{Un&&(gl.includes(ze)?$e.push({event:ze,listener:Se}):ct.includes(ze)&&$e.push({event:ze,listener:ha}))}),C&&$e.push({event:"pointermove",listener:Qn});const Bi=()=>{oe.current=!0},Hi=()=>{oe.current=!1,ml()},Kn=M&&(Dn.mouseout||Dn.mouseleave);return Kn&&((se=Re.current)===null||se===void 0||se.addEventListener("mouseover",Bi),(de=Re.current)===null||de===void 0||de.addEventListener("mouseout",Hi)),$e.forEach(({event:ze,listener:Un})=>{ye.forEach(Va=>{var Bl;(Bl=Va.current)===null||Bl===void 0||Bl.addEventListener(ze,Un)})}),()=>{var ze,Un;pt.scroll&&(window.removeEventListener("scroll",yt),yn==null||yn.removeEventListener("scroll",yt),gn==null||gn.removeEventListener("scroll",yt)),pt.resize?window.removeEventListener("resize",yt):_t==null||_t(),pt.clickOutsideAnchor&&window.removeEventListener("click",Nn),pt.escape&&window.removeEventListener("keydown",Tt),Kn&&((ze=Re.current)===null||ze===void 0||ze.removeEventListener("mouseover",Bi),(Un=Re.current)===null||Un===void 0||Un.removeEventListener("mouseout",Hi)),$e.forEach(({event:Va,listener:Bl})=>{ye.forEach(Qu=>{var Hl;(Hl=Qu.current)===null||Hl===void 0||Hl.removeEventListener(Va,Bl)})})}},[Y,Ne,ut,k,ee,I,K,ne,be,E,D]),A.useEffect(()=>{var se,de;let ye=(de=(se=Ye==null?void 0:Ye.anchorSelect)!==null&&se!==void 0?se:d)!==null&&de!==void 0?de:"";!ye&&a&&(ye=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`);const Ie=new MutationObserver(yt=>{const yn=[],gn=[];yt.forEach(_t=>{if(_t.type==="attributes"&&_t.attributeName==="data-tooltip-id"&&(_t.target.getAttribute("data-tooltip-id")===a?yn.push(_t.target):_t.oldValue===a&&gn.push(_t.target)),_t.type==="childList"){if(Y){const Tt=[..._t.removedNodes].filter($e=>$e.nodeType===1);if(ye)try{gn.push(...Tt.filter($e=>$e.matches(ye))),gn.push(...Tt.flatMap($e=>[...$e.querySelectorAll(ye)]))}catch{}Tt.some($e=>{var sn;return!!(!((sn=$e==null?void 0:$e.contains)===null||sn===void 0)&&sn.call($e,Y))&&(jn(!1),qe(!1),ue(null),Rn(At),Rn(jt),!0)})}if(ye)try{const Tt=[..._t.addedNodes].filter($e=>$e.nodeType===1);yn.push(...Tt.filter($e=>$e.matches(ye))),yn.push(...Tt.flatMap($e=>[...$e.querySelectorAll(ye)]))}catch{}}}),(yn.length||gn.length)&&J(_t=>[..._t.filter(Tt=>!gn.includes(Tt)),...yn])});return Ie.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{Ie.disconnect()}},[a,d,Ye==null?void 0:Ye.anchorSelect,Y]),A.useEffect(()=>{Ne()},[Ne]),A.useEffect(()=>{if(!(P!=null&&P.current))return()=>null;const se=new ResizeObserver(()=>{setTimeout(()=>Ne())});return se.observe(P.current),()=>{se.disconnect()}},[q,P==null?void 0:P.current]),A.useEffect(()=>{var se;const de=document.querySelector(`[id='${f}']`),ye=[...ee,de];Y&&ye.includes(Y)||ue((se=ee[0])!==null&&se!==void 0?se:de)},[f,ee,Y]),A.useEffect(()=>(_e&&qe(!0),()=>{Rn(At),Rn(jt)}),[]),A.useEffect(()=>{var se;let de=(se=Ye==null?void 0:Ye.anchorSelect)!==null&&se!==void 0?se:d;if(!de&&a&&(de=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`),de)try{const ye=Array.from(document.querySelectorAll(de));J(ye)}catch{J([])}},[a,d,Ye==null?void 0:Ye.anchorSelect]),A.useEffect(()=>{At.current&&(Rn(At),an(E))},[E]);const Lt=(it=Ye==null?void 0:Ye.content)!==null&&it!==void 0?it:q,yl=Dt&&Object.keys(Gt.tooltipStyles).length>0;return A.useImperativeHandle(l,()=>({open:se=>{if(se!=null&&se.anchorSelect)try{document.querySelector(se.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${se.anchorSelect}" is not a valid CSS selector`)}hl(se??null),se!=null&&se.delay?an(se.delay):qe(!0)},close:se=>{se!=null&&se.delay?Zt(se.delay):qe(!1)},activeAnchor:Y,place:Gt.place,isOpen:!!(ut&&!w&&Lt&&yl)})),ut&&!w&&Lt?Pe.createElement(T,{id:a,role:we,className:Uf("react-tooltip",Ai.tooltip,bf.tooltip,bf[o],s,`react-tooltip__place-${Gt.place}`,Ai[yl?"show":"closing"],yl?"react-tooltip__show":"react-tooltip__closing",S==="fixed"&&Ai.fixed,M&&Ai.clickable),onTransitionEnd:se=>{Rn(pn),Dt||se.propertyName!=="opacity"||(jn(!1),hl(null),fe==null||fe())},style:{...ce,...Gt.tooltipStyles,opacity:pe!==void 0&&yl?pe:void 0},ref:Re},Lt,Pe.createElement(T,{className:Uf("react-tooltip-arrow",Ai.arrow,bf.arrow,r,j&&Ai.noArrow),style:{...Gt.tooltipArrowStyles,background:je?`linear-gradient(to right bottom, transparent 50%, ${je} 50%)`:void 0},ref:St})):null},gx=({content:l})=>Pe.createElement("span",{dangerouslySetInnerHTML:{__html:l}}),Zy=Pe.forwardRef(({id:l,anchorId:a,anchorSelect:s,content:r,html:o,render:f,className:d,classNameArrow:h,variant:p="dark",place:m="top",offset:g=10,wrapper:S="div",children:x=null,events:T=["hover"],openOnClick:E=!1,positionStrategy:D="absolute",middlewares:C,delayShow:w=0,delayHide:j=0,float:M=!1,hidden:G=!1,noArrow:H=!1,clickable:te=!1,closeOnEsc:I=!1,closeOnScroll:K=!1,closeOnResize:ne=!1,openEvents:xe,closeEvents:ce,globalCloseEvents:W,imperativeModeOnly:re=!1,style:fe,position:ve,isOpen:q,defaultIsOpen:P=!1,disableStyleInjection:le=!1,border:_e,opacity:R,arrowColor:Y,setIsOpen:ue,afterShow:ae,afterHide:pe,disableTooltip:je,role:we="tooltip"},it)=>{const[Re,St]=A.useState(r),[At,jt]=A.useState(o),[pn,Gt]=A.useState(m),[Xn,Dt]=A.useState(p),[dl,ut]=A.useState(g),[jn,Ye]=A.useState(w),[hl,ln]=A.useState(j),[O,k]=A.useState(M),[$,oe]=A.useState(G),[ee,J]=A.useState(S),[ie,be]=A.useState(T),[We,tt]=A.useState(D),[Dn,pt]=A.useState(null),[qe,Xt]=A.useState(null),an=A.useRef(le),{anchorRefs:Zt,activeAnchor:Zn}=Xy(l),ml=ot=>ot==null?void 0:ot.getAttributeNames().reduce((qt,Se)=>{var Ne;return Se.startsWith("data-tooltip-")&&(qt[Se.replace(/^data-tooltip-/,"")]=(Ne=ot==null?void 0:ot.getAttribute(Se))!==null&&Ne!==void 0?Ne:null),qt},{}),pl=ot=>{const qt={place:Se=>{var Ne;Gt((Ne=Se)!==null&&Ne!==void 0?Ne:m)},content:Se=>{St(Se??r)},html:Se=>{jt(Se??o)},variant:Se=>{var Ne;Dt((Ne=Se)!==null&&Ne!==void 0?Ne:p)},offset:Se=>{ut(Se===null?g:Number(Se))},wrapper:Se=>{var Ne;J((Ne=Se)!==null&&Ne!==void 0?Ne:S)},events:Se=>{const Ne=Se==null?void 0:Se.split(" ");be(Ne??T)},"position-strategy":Se=>{var Ne;tt((Ne=Se)!==null&&Ne!==void 0?Ne:D)},"delay-show":Se=>{Ye(Se===null?w:Number(Se))},"delay-hide":Se=>{ln(Se===null?j:Number(Se))},float:Se=>{k(Se===null?M:Se==="true")},hidden:Se=>{oe(Se===null?G:Se==="true")},"class-name":Se=>{pt(Se)}};Object.values(qt).forEach(Se=>Se(null)),Object.entries(ot).forEach(([Se,Ne])=>{var Lt;(Lt=qt[Se])===null||Lt===void 0||Lt.call(qt,Ne)})};A.useEffect(()=>{St(r)},[r]),A.useEffect(()=>{jt(o)},[o]),A.useEffect(()=>{Gt(m)},[m]),A.useEffect(()=>{Dt(p)},[p]),A.useEffect(()=>{ut(g)},[g]),A.useEffect(()=>{Ye(w)},[w]),A.useEffect(()=>{ln(j)},[j]),A.useEffect(()=>{k(M)},[M]),A.useEffect(()=>{oe(G)},[G]),A.useEffect(()=>{tt(D)},[D]),A.useEffect(()=>{an.current!==le&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[le]),A.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:le==="core",disableBase:le}}))},[]),A.useEffect(()=>{var ot;const qt=new Set(Zt);let Se=s;if(!Se&&l&&(Se=`[data-tooltip-id='${l.replace(/'/g,"\\'")}']`),Se)try{document.querySelectorAll(Se).forEach(de=>{qt.add({current:de})})}catch{console.warn(`[react-tooltip] "${Se}" is not a valid CSS selector`)}const Ne=document.querySelector(`[id='${a}']`);if(Ne&&qt.add({current:Ne}),!qt.size)return()=>null;const Lt=(ot=qe??Ne)!==null&&ot!==void 0?ot:Zn.current,yl=new MutationObserver(de=>{de.forEach(ye=>{var Ie;if(!Lt||ye.type!=="attributes"||!(!((Ie=ye.attributeName)===null||Ie===void 0)&&Ie.startsWith("data-tooltip-")))return;const yt=ml(Lt);pl(yt)})}),se={attributes:!0,childList:!1,subtree:!1};if(Lt){const de=ml(Lt);pl(de),yl.observe(Lt,se)}return()=>{yl.disconnect()}},[Zt,Zn,qe,a,s]),A.useEffect(()=>{fe!=null&&fe.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),_e&&!qp("border",`${_e}`)&&console.warn(`[react-tooltip] "${_e}" is not a valid \`border\`.`),fe!=null&&fe.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),R&&!qp("opacity",`${R}`)&&console.warn(`[react-tooltip] "${R}" is not a valid \`opacity\`.`)},[]);let Qn=x;const Nn=A.useRef(null);if(f){const ot=f({content:(qe==null?void 0:qe.getAttribute("data-tooltip-content"))||Re||null,activeAnchor:qe});Qn=ot?Pe.createElement("div",{ref:Nn,className:"react-tooltip-content-wrapper"},ot):null}else Re&&(Qn=Re);At&&(Qn=Pe.createElement(gx,{content:At}));const qa={forwardRef:it,id:l,anchorId:a,anchorSelect:s,className:Uf(d,Dn),classNameArrow:h,content:Qn,contentWrapperRef:Nn,place:pn,variant:Xn,offset:dl,wrapper:ee,events:ie,openOnClick:E,positionStrategy:We,middlewares:C,delayShow:jn,delayHide:hl,float:O,hidden:$,noArrow:H,clickable:te,closeOnEsc:I,closeOnScroll:K,closeOnResize:ne,openEvents:xe,closeEvents:ce,globalCloseEvents:W,imperativeModeOnly:re,style:fe,position:ve,isOpen:q,defaultIsOpen:P,border:_e,opacity:R,arrowColor:Y,setIsOpen:ue,afterShow:ae,afterHide:pe,disableTooltip:je,activeAnchor:qe,setActiveAnchor:ot=>Xt(ot),role:we};return Pe.createElement(yx,{...qa})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",l=>{l.detail.disableCore||Bp({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),l.detail.disableBase||Bp({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});function od({showModal:l}){const a=$n(),s=cl(),[r,o]=A.useState({profile:JSON.parse(sessionStorage.getItem("current_user"))?JSON.parse(sessionStorage.getItem("current_user")).profile_picture:null}),[f,d]=A.useState(sessionStorage.getItem("current_user")?JSON.parse(sessionStorage.getItem("current_user")).is_superuser?"Admin":"User":"Guest"),[h,p]=A.useState(".short-term"),[m,g]=A.useState(!1),S=E=>{window.scrollTo({top:document.querySelector(E).offsetTop,behavior:"smooth"})},x=document.querySelectorAll("section.short-term, section.mid-term, section.long-term");window.addEventListener("scroll",()=>{let E="";x.forEach(D=>{const C=D.offsetTop,w=D.clientHeight;window.scrollY>=C-w/2&&(E=D.getAttribute("id"))}),p(`.${E}`)});const T=()=>{On.logout(),a("/")};return v.jsxs("nav",{children:[v.jsx(Zy,{id:"nav-menu-tooltip",place:"top",effect:"solid",className:"tooltip"}),v.jsxs("ul",{children:[v.jsx("li",{className:"system-icon",children:v.jsx("img",{src:_S,alt:"system-icon"})}),v.jsx("li",{className:h===".short-term"&&s.pathname.startsWith("/home")?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".short-term"),S("#short-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"1 year ahead","data-tooltip-offset":30,onClick:()=>{p(".short-term"),S("#short-term")},children:"Short-Term"})}),v.jsx("li",{className:h===".mid-term"?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".mid-term"),S("#mid-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"3 years ahead","data-tooltip-offset":30,onClick:()=>{p(".mid-term"),S("#mid-term")},children:"Mid-Term"})}),v.jsx("li",{className:h===".long-term"?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".long-term"),S("#long-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"5 years ahead","data-tooltip-offset":30,onClick:()=>{p(".long-term"),S("#long-term")},children:"Long-Term"})}),f=="Admin"||f=="User"?v.jsxs(v.Fragment,{children:[v.jsx("li",{onClick:l,children:"Upload Dataset"}),v.jsxs("li",{children:[v.jsx("img",{src:r.profile?`http://127.0.0.1:8000${r.profile}`:Ru,alt:"sample-profile",onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),className:s.pathname.includes("/user-management")||s.pathname.includes("/account")?"active":""}),m&&v.jsx("div",{className:`profile-menu-container ${f}`,onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:v.jsxs("div",{className:"profile-menu",children:[v.jsx("li",{onClick:()=>{a("/account"),p("profile")},className:s.pathname.startsWith("/account")?"active":"",children:"Edit Account Details"}),JSON.parse(sessionStorage.getItem("current_user")).is_superuser&&v.jsx("li",{onClick:()=>{a("/user-management"),p("profile")},className:s.pathname.startsWith("/user-management")?"active":"",children:"User Management"}),v.jsx("li",{onClick:T,children:"Log Out"})]})})]})]}):v.jsx("button",{disabled:"disabled",className:"view-as-guest",children:"View as Guest"})]})]})}const Du="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%235a6576'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",vx="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2024%2024'%20id='Upload'%3e%3cpath%20d='M8.71,7.71,11,5.41V15a1,1,0,0,0,2,0V5.41l2.29,2.3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42l-4-4a1,1,0,0,0-.33-.21,1,1,0,0,0-.76,0,1,1,0,0,0-.33.21l-4,4A1,1,0,1,0,8.71,7.71ZM21,12a1,1,0,0,0-1,1v6a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V13a1,1,0,0,0-2,0v6a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V13A1,1,0,0,0,21,12Z'%20fill='%234f46e5'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",bx="http://127.0.0.1:8000/dataset_validation/validate/";class Sx{async validate(a){const s=new FormData;s.append("file",a);try{const r=await fetch(bx,{method:"POST",body:s});return r.ok?await r.json():await r.json()}catch(r){console.log("Failed to validate dataset!",r)}}}const xx=new Sx,Ex="http://127.0.0.1:8000/dataset/";class wx{async postDataset(a){const s=new FormData,r=await On.getCurrentUser();s.append("file",a),s.append("uploaded_by_user",r.id);try{const o=await fetch(Ex,{method:"POST",headers:this.getAuthHeader(),body:s});return o.ok?await o.json():await o.json()}catch(o){console.log("Failed to validate dataset!",o)}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const Ax=new wx,Sf="http://127.0.0.1:8000/predictions/";class _x{async generatePredictions(a){try{const s=await fetch(`${Sf}generate/`,{method:"POST",headers:{...this.getAuthHeader(),"Content-Type":"application/json"},body:JSON.stringify({dataset_id:a})});if(!s.ok){const o=await s.json();throw new Error(o.error||"Failed to generate predictions")}return await s.json()}catch(s){throw console.error("Failed to generate predictions:",s),s}}async getPredictionsByDataset(a){try{const s=await fetch(`${Sf}by_dataset/?dataset_id=${a}`,{method:"GET",headers:this.getAuthHeader()});if(!s.ok){const o=await s.json();throw new Error(o.error||"Failed to fetch predictions")}return await s.json()}catch(s){throw console.error("Failed to fetch predictions:",s),s}}async getLatestTrends(){try{let a=[],s=[],r=[];const o=await fetch(`${Sf}latest_trends/`,{method:"GET"});if(!o.ok){const h=await o.json();throw new Error(h.error||"Failed to fetch latest trends")}const f=await o.json();f.forEach(h=>{h.category=="growth_rate"?a.push(h):h.category=="revenue"?s.push(h):h.category=="least_crowded"&&r.push(h)});const d=[...new Set(f.map(h=>h.prediction_result.year))];return[a,s,r,d.sort((h,p)=>h-p)]}catch(a){throw console.error("Failed to fetch latest trends:",a),a}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const Qy=new _x,Tx="/assets/analysis-chart-DyDYoyz_.gif";class Rx{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map,this.isConnecting=!1,this.isConnected=!1}connect(){if(!(this.isConnecting||this.isConnected)){this.isConnecting=!0;try{const s=`${window.location.protocol==="https:"?"wss:":"ws:"}//127.0.0.1:8000/ws/predictions/`;this.socket=new WebSocket(s),this.socket.onopen=r=>{console.log("WebSocket connected:",r),this.isConnected=!0,this.isConnecting=!1,this.reconnectAttempts=0,this.send({type:"ping",message:"Connection test"}),this.notifyListeners("connection_established",{message:"Connected to real-time updates"})},this.socket.onmessage=r=>{try{const o=JSON.parse(r.data);console.log("WebSocket message received:",o),this.notifyListeners(o.type,o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.socket.onclose=r=>{console.log("WebSocket disconnected:",r),this.isConnected=!1,this.isConnecting=!1,this.socket=null,this.notifyListeners("connection_lost",{message:"Connection to real-time updates lost"}),r.code!==1e3&&this.reconnectAttempts<this.maxReconnectAttempts&&this.scheduleReconnect()},this.socket.onerror=r=>{console.error("WebSocket error:",r),this.isConnecting=!1,this.notifyListeners("connection_error",{message:"Failed to connect to real-time updates",error:r})}}catch(a){console.error("Failed to create WebSocket connection:",a),this.isConnecting=!1}}}disconnect(){this.socket&&(this.socket.close(1e3,"Intentional disconnect"),this.socket=null),this.isConnected=!1,this.isConnecting=!1,this.reconnectAttempts=0}scheduleReconnect(){this.reconnectAttempts++,console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{!this.isConnected&&!this.isConnecting&&this.connect()},this.reconnectInterval)}send(a){this.socket&&this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(a)):console.warn("WebSocket is not connected. Message not sent:",a)}addEventListener(a,s){this.listeners.has(a)||this.listeners.set(a,[]),this.listeners.get(a).push(s)}removeEventListener(a,s){if(this.listeners.has(a)){const r=this.listeners.get(a),o=r.indexOf(s);o>-1&&r.splice(o,1)}}notifyListeners(a,s){this.listeners.has(a)&&this.listeners.get(a).forEach(r=>{try{r(s)}catch(o){console.error(`Error in WebSocket listener for ${a}:`,o)}})}getConnectionStatus(){return{isConnected:this.isConnected,isConnecting:this.isConnecting,reconnectAttempts:this.reconnectAttempts}}onDatasetUploaded(a){this.addEventListener("dataset_uploaded",a)}onPredictionStarted(a){this.addEventListener("prediction_started",a)}onPredictionCompleted(a){this.addEventListener("prediction_completed",a)}onPredictionDataUpdated(a){this.addEventListener("prediction_data_updated",a)}onPredictionError(a){this.addEventListener("prediction_error",a)}onConnectionEstablished(a){this.addEventListener("connection_established",a)}onConnectionLost(a){this.addEventListener("connection_lost",a)}onConnectionError(a){this.addEventListener("connection_error",a)}onUserCreated(a){this.addEventListener("user_created",a)}onUserUpdated(a){this.addEventListener("user_updated",a)}onUserStatusChanged(a){this.addEventListener("user_status_changed",a)}onUserDeleted(a){this.addEventListener("user_deleted",a)}onProfileUpdated(a){this.addEventListener("profile_updated",a)}}const Ke=new Rx;function Ox({showModal:l,onPredictionComplete:a}){const[s,r]=A.useState(!1),[o,f]=A.useState(null),[d,h]=A.useState(String),[p,m]=A.useState(""),[g,S]=A.useState(!1),[x,T]=A.useState([]),[E,D]=A.useState(!1);A.useEffect(()=>{const G=K=>{console.log("Dataset uploaded via WebSocket:",K),T(ne=>[...ne,`Dataset uploaded: ${K.message}`])},H=K=>{console.log("Prediction started via WebSocket:",K),m(K.message),T(ne=>[...ne,`Progress: ${K.message}`])},te=K=>{console.log("Prediction completed via WebSocket:",K),S(!0),m(K.message),T(ne=>[...ne,`Completed: ${K.message}`]),setTimeout(()=>{S(!1),l()},2e3)},I=K=>{console.log("Prediction error via WebSocket:",K),m(`Error: ${K.message}`),T(ne=>[...ne,`Error: ${K.error}`]),D(!1)};return Ke.onDatasetUploaded(G),Ke.onPredictionStarted(H),Ke.onPredictionCompleted(te),Ke.onPredictionError(I),()=>{Ke.removeEventListener("dataset_uploaded",G),Ke.removeEventListener("prediction_started",H),Ke.removeEventListener("prediction_completed",te),Ke.removeEventListener("prediction_error",I)}},[l]);const C=v.jsxs("div",{className:"dataset-requirements",children:["Must include exactly the following columns and data types:",v.jsx("br",{}),v.jsx("br",{}),v.jsxs("table",{children:[v.jsxs("tr",{children:[v.jsx("th",{children:"Column Name"}),v.jsx("th",{children:"Data Type"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Year"}),v.jsx("td",{children:"Integer"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Industry Sector"}),v.jsx("td",{children:"String"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Number of Businesses"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Revenue (PHP Millions)"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Growth Rate (%)"}),v.jsx("td",{children:"Number"})]})]})]}),w=G=>{if(G.target.files.length>0){const H=G.target.files[0];f(H)}else f(null)},j=G=>{h(null),G.preventDefault(),G.stopPropagation(),document.getElementById("file").value="",f(null)},M=async()=>{S(!1),D(!0);try{const G=await xx.validate(o);if(h(G),console.log(G),G.valid){m("Uploading dataset...");const H=await Ax.postDataset(o);if(H.id){m("Generating predictions... This may take a few moments.");const te=await Qy.generatePredictions(H.id);if(te.success)S(!0),m(`Successfully generated ${te.predictions_count} predictions for your dataset!`),h({success:!0,valid:!0,message:`Successfully generated ${te.predictions_count} predictions for your dataset!`}),a&&a(H.id,te),setTimeout(()=>{S(!1),l()},2e3);else throw new Error("Failed to generate predictions")}else throw new Error("Failed to upload dataset")}}catch(G){console.error("Error in submission process:",G),h({valid:!1,message:`Error: ${G.message||"An unexpected error occurred"}`}),m("")}finally{D(!1)}};return v.jsxs("div",{className:"upload-dataset-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsx(Zy,{id:"dataset-required-info",place:"bottom",effect:"solid",className:"tooltip",children:C}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),onClick:l,children:v.jsx("img",{src:s?ku:Du,alt:"close-icon"})}),v.jsx("h2",{children:"Upload Dataset"}),v.jsxs("p",{className:"reminder",children:["Please make sure the dataset is correct and complete to avoid inaccurate result. ","",v.jsx("a",{"data-tooltip-id":"dataset-required-info","data-tooltip-offset":10,children:"Hover to see Dataset Requirements."})]}),p&&v.jsxs("div",{className:`current-step ${g?"success":"processing"}`,children:[v.jsx("img",{src:Tx,alt:""}),v.jsx("p",{children:p})]}),d&&d.valid===!1&&v.jsx("div",{className:`response-message ${d.valid?"success":"error"}`,children:d.message}),p===""&&v.jsxs("section",{children:[v.jsx("input",{type:"file",name:"file",id:"file",accept:".csv",style:{display:"none"},onChange:w}),v.jsxs("label",{htmlFor:"file",children:[v.jsx("img",{src:vx,alt:"upload-icon",className:"upload-icon"}),v.jsxs("p",{children:[v.jsx("span",{children:"Click here"})," to upload your file."]}),v.jsx("p",{className:"supported-format",children:"Supported Format: csv"}),o&&v.jsxs("div",{className:"selected-file",children:[v.jsx("p",{children:o.name}),v.jsx("img",{src:Du,alt:"remove",className:"remove-icon",onClick:j})]})]})]}),v.jsxs("button",{disabled:o===null||E,className:"submit-button",onClick:M,children:[E&&v.jsx(ca,{}),E?"Processing...":"Make Prediction"]})]})]})}function Ft({color:l=null,data:a,topNumber:s,type:r,filterResult:o}){const[f,d]=A.useState(!1);A.useEffect(()=>{s!=1&&f?document.getElementById(`card-top-1-${r}`).classList.remove("active"):s!=1&&!f&&document.getElementById(`card-top-1-${r}`).classList.add("active")},[f]);const h=p=>p>=1e9?(p/1e9).toFixed(2)+" billiion":p>=1e6?(p/1e6).toFixed(2)+" million":p.toLocaleString();return v.jsxs("article",{id:`card-top-${s}-${r}`,className:`card-top-${s} ${s==1?"active":""} ${l??""}`,onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),children:[v.jsx("div",{className:`circle ${s==1?"active":""} ${l??""}`}),v.jsxs("div",{className:`info-container ${s==1?"active":""} ${l??""}`,children:[v.jsx("h1",{children:s}),v.jsxs("div",{className:"paragraph-container",children:[o=="Growing Industry Sector"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to achieve a growth rate of"," ",a?a.prediction_result.predicted_growth_rate:"___","%, based on current historical trend analysis."]}),o=="Industry Sector Revenue"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is expected to generate approximately ₱",a?h(Number(a.prediction_result.predicted_revenue)):"___"," ","in revenue, based on the current historical trend analysis."]})]}),o=="Least Crowded"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to be the least crowded industry, with an estimated"," ",a?h(a.prediction_result.predicted_least_crowded):"___"," ","businesses in operation, based on the current historical trend analysis."]}),v.jsx("h1",{className:`industry ${s==1?"active":""}`,children:a?a.prediction_result.industry_sector:"___"})]})]})}function Ky(l,a){return function(){return l.apply(a,arguments)}}const{toString:Cx}=Object.prototype,{getPrototypeOf:cd}=Object,{iterator:Vu,toStringTag:Jy}=Symbol,Fu=(l=>a=>{const s=Cx.call(a);return l[s]||(l[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Gn=l=>(l=l.toLowerCase(),a=>Fu(a)===l),Yu=l=>a=>typeof a===l,{isArray:zi}=Array,$s=Yu("undefined");function jx(l){return l!==null&&!$s(l)&&l.constructor!==null&&!$s(l.constructor)&&tn(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const Py=Gn("ArrayBuffer");function Dx(l){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(l):a=l&&l.buffer&&Py(l.buffer),a}const Nx=Yu("string"),tn=Yu("function"),Wy=Yu("number"),$u=l=>l!==null&&typeof l=="object",Ux=l=>l===!0||l===!1,vu=l=>{if(Fu(l)!=="object")return!1;const a=cd(l);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Jy in l)&&!(Vu in l)},Mx=Gn("Date"),Lx=Gn("File"),zx=Gn("Blob"),kx=Gn("FileList"),Bx=l=>$u(l)&&tn(l.pipe),Hx=l=>{let a;return l&&(typeof FormData=="function"&&l instanceof FormData||tn(l.append)&&((a=Fu(l))==="formdata"||a==="object"&&tn(l.toString)&&l.toString()==="[object FormData]"))},qx=Gn("URLSearchParams"),[Vx,Fx,Yx,$x]=["ReadableStream","Request","Response","Headers"].map(Gn),Gx=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ws(l,a,{allOwnKeys:s=!1}={}){if(l===null||typeof l>"u")return;let r,o;if(typeof l!="object"&&(l=[l]),zi(l))for(r=0,o=l.length;r<o;r++)a.call(null,l[r],r,l);else{const f=s?Object.getOwnPropertyNames(l):Object.keys(l),d=f.length;let h;for(r=0;r<d;r++)h=f[r],a.call(null,l[h],h,l)}}function Iy(l,a){a=a.toLowerCase();const s=Object.keys(l);let r=s.length,o;for(;r-- >0;)if(o=s[r],a===o.toLowerCase())return o;return null}const Na=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,eg=l=>!$s(l)&&l!==Na;function Lf(){const{caseless:l}=eg(this)&&this||{},a={},s=(r,o)=>{const f=l&&Iy(a,o)||o;vu(a[f])&&vu(r)?a[f]=Lf(a[f],r):vu(r)?a[f]=Lf({},r):zi(r)?a[f]=r.slice():a[f]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Ws(arguments[r],s);return a}const Xx=(l,a,s,{allOwnKeys:r}={})=>(Ws(a,(o,f)=>{s&&tn(o)?l[f]=Ky(o,s):l[f]=o},{allOwnKeys:r}),l),Zx=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),Qx=(l,a,s,r)=>{l.prototype=Object.create(a.prototype,r),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:a.prototype}),s&&Object.assign(l.prototype,s)},Kx=(l,a,s,r)=>{let o,f,d;const h={};if(a=a||{},l==null)return a;do{for(o=Object.getOwnPropertyNames(l),f=o.length;f-- >0;)d=o[f],(!r||r(d,l,a))&&!h[d]&&(a[d]=l[d],h[d]=!0);l=s!==!1&&cd(l)}while(l&&(!s||s(l,a))&&l!==Object.prototype);return a},Jx=(l,a,s)=>{l=String(l),(s===void 0||s>l.length)&&(s=l.length),s-=a.length;const r=l.indexOf(a,s);return r!==-1&&r===s},Px=l=>{if(!l)return null;if(zi(l))return l;let a=l.length;if(!Wy(a))return null;const s=new Array(a);for(;a-- >0;)s[a]=l[a];return s},Wx=(l=>a=>l&&a instanceof l)(typeof Uint8Array<"u"&&cd(Uint8Array)),Ix=(l,a)=>{const r=(l&&l[Vu]).call(l);let o;for(;(o=r.next())&&!o.done;){const f=o.value;a.call(l,f[0],f[1])}},eE=(l,a)=>{let s;const r=[];for(;(s=l.exec(a))!==null;)r.push(s);return r},tE=Gn("HTMLFormElement"),nE=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,o){return r.toUpperCase()+o}),$p=(({hasOwnProperty:l})=>(a,s)=>l.call(a,s))(Object.prototype),lE=Gn("RegExp"),tg=(l,a)=>{const s=Object.getOwnPropertyDescriptors(l),r={};Ws(s,(o,f)=>{let d;(d=a(o,f,l))!==!1&&(r[f]=d||o)}),Object.defineProperties(l,r)},aE=l=>{tg(l,(a,s)=>{if(tn(l)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=l[s];if(tn(r)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},iE=(l,a)=>{const s={},r=o=>{o.forEach(f=>{s[f]=!0})};return zi(l)?r(l):r(String(l).split(a)),s},sE=()=>{},rE=(l,a)=>l!=null&&Number.isFinite(l=+l)?l:a;function uE(l){return!!(l&&tn(l.append)&&l[Jy]==="FormData"&&l[Vu])}const oE=l=>{const a=new Array(10),s=(r,o)=>{if($u(r)){if(a.indexOf(r)>=0)return;if(!("toJSON"in r)){a[o]=r;const f=zi(r)?[]:{};return Ws(r,(d,h)=>{const p=s(d,o+1);!$s(p)&&(f[h]=p)}),a[o]=void 0,f}}return r};return s(l,0)},cE=Gn("AsyncFunction"),fE=l=>l&&($u(l)||tn(l))&&tn(l.then)&&tn(l.catch),ng=((l,a)=>l?setImmediate:a?((s,r)=>(Na.addEventListener("message",({source:o,data:f})=>{o===Na&&f===s&&r.length&&r.shift()()},!1),o=>{r.push(o),Na.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",tn(Na.postMessage)),dE=typeof queueMicrotask<"u"?queueMicrotask.bind(Na):typeof process<"u"&&process.nextTick||ng,hE=l=>l!=null&&tn(l[Vu]),F={isArray:zi,isArrayBuffer:Py,isBuffer:jx,isFormData:Hx,isArrayBufferView:Dx,isString:Nx,isNumber:Wy,isBoolean:Ux,isObject:$u,isPlainObject:vu,isReadableStream:Vx,isRequest:Fx,isResponse:Yx,isHeaders:$x,isUndefined:$s,isDate:Mx,isFile:Lx,isBlob:zx,isRegExp:lE,isFunction:tn,isStream:Bx,isURLSearchParams:qx,isTypedArray:Wx,isFileList:kx,forEach:Ws,merge:Lf,extend:Xx,trim:Gx,stripBOM:Zx,inherits:Qx,toFlatObject:Kx,kindOf:Fu,kindOfTest:Gn,endsWith:Jx,toArray:Px,forEachEntry:Ix,matchAll:eE,isHTMLForm:tE,hasOwnProperty:$p,hasOwnProp:$p,reduceDescriptors:tg,freezeMethods:aE,toObjectSet:iE,toCamelCase:nE,noop:sE,toFiniteNumber:rE,findKey:Iy,global:Na,isContextDefined:eg,isSpecCompliantForm:uE,toJSONObject:oE,isAsyncFn:cE,isThenable:fE,setImmediate:ng,asap:dE,isIterable:hE};function Oe(l,a,s,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",a&&(this.code=a),s&&(this.config=s),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}F.inherits(Oe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:F.toJSONObject(this.config),code:this.code,status:this.status}}});const lg=Oe.prototype,ag={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{ag[l]={value:l}});Object.defineProperties(Oe,ag);Object.defineProperty(lg,"isAxiosError",{value:!0});Oe.from=(l,a,s,r,o,f)=>{const d=Object.create(lg);return F.toFlatObject(l,d,function(p){return p!==Error.prototype},h=>h!=="isAxiosError"),Oe.call(d,l.message,a,s,r,o),d.cause=l,d.name=l.name,f&&Object.assign(d,f),d};const mE=null;function zf(l){return F.isPlainObject(l)||F.isArray(l)}function ig(l){return F.endsWith(l,"[]")?l.slice(0,-2):l}function Gp(l,a,s){return l?l.concat(a).map(function(o,f){return o=ig(o),!s&&f?"["+o+"]":o}).join(s?".":""):a}function pE(l){return F.isArray(l)&&!l.some(zf)}const yE=F.toFlatObject(F,{},null,function(a){return/^is[A-Z]/.test(a)});function Gu(l,a,s){if(!F.isObject(l))throw new TypeError("target must be an object");a=a||new FormData,s=F.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(D,C){return!F.isUndefined(C[D])});const r=s.metaTokens,o=s.visitor||g,f=s.dots,d=s.indexes,p=(s.Blob||typeof Blob<"u"&&Blob)&&F.isSpecCompliantForm(a);if(!F.isFunction(o))throw new TypeError("visitor must be a function");function m(E){if(E===null)return"";if(F.isDate(E))return E.toISOString();if(!p&&F.isBlob(E))throw new Oe("Blob is not supported. Use a Buffer instead.");return F.isArrayBuffer(E)||F.isTypedArray(E)?p&&typeof Blob=="function"?new Blob([E]):Buffer.from(E):E}function g(E,D,C){let w=E;if(E&&!C&&typeof E=="object"){if(F.endsWith(D,"{}"))D=r?D:D.slice(0,-2),E=JSON.stringify(E);else if(F.isArray(E)&&pE(E)||(F.isFileList(E)||F.endsWith(D,"[]"))&&(w=F.toArray(E)))return D=ig(D),w.forEach(function(M,G){!(F.isUndefined(M)||M===null)&&a.append(d===!0?Gp([D],G,f):d===null?D:D+"[]",m(M))}),!1}return zf(E)?!0:(a.append(Gp(C,D,f),m(E)),!1)}const S=[],x=Object.assign(yE,{defaultVisitor:g,convertValue:m,isVisitable:zf});function T(E,D){if(!F.isUndefined(E)){if(S.indexOf(E)!==-1)throw Error("Circular reference detected in "+D.join("."));S.push(E),F.forEach(E,function(w,j){(!(F.isUndefined(w)||w===null)&&o.call(a,w,F.isString(j)?j.trim():j,D,x))===!0&&T(w,D?D.concat(j):[j])}),S.pop()}}if(!F.isObject(l))throw new TypeError("data must be an object");return T(l),a}function Xp(l){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(r){return a[r]})}function fd(l,a){this._pairs=[],l&&Gu(l,this,a)}const sg=fd.prototype;sg.append=function(a,s){this._pairs.push([a,s])};sg.toString=function(a){const s=a?function(r){return a.call(this,r,Xp)}:Xp;return this._pairs.map(function(o){return s(o[0])+"="+s(o[1])},"").join("&")};function gE(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rg(l,a,s){if(!a)return l;const r=s&&s.encode||gE;F.isFunction(s)&&(s={serialize:s});const o=s&&s.serialize;let f;if(o?f=o(a,s):f=F.isURLSearchParams(a)?a.toString():new fd(a,s).toString(r),f){const d=l.indexOf("#");d!==-1&&(l=l.slice(0,d)),l+=(l.indexOf("?")===-1?"?":"&")+f}return l}class Zp{constructor(){this.handlers=[]}use(a,s,r){return this.handlers.push({fulfilled:a,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){F.forEach(this.handlers,function(r){r!==null&&a(r)})}}const ug={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},vE=typeof URLSearchParams<"u"?URLSearchParams:fd,bE=typeof FormData<"u"?FormData:null,SE=typeof Blob<"u"?Blob:null,xE={isBrowser:!0,classes:{URLSearchParams:vE,FormData:bE,Blob:SE},protocols:["http","https","file","blob","url","data"]},dd=typeof window<"u"&&typeof document<"u",kf=typeof navigator=="object"&&navigator||void 0,EE=dd&&(!kf||["ReactNative","NativeScript","NS"].indexOf(kf.product)<0),wE=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",AE=dd&&window.location.href||"http://localhost",_E=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:dd,hasStandardBrowserEnv:EE,hasStandardBrowserWebWorkerEnv:wE,navigator:kf,origin:AE},Symbol.toStringTag,{value:"Module"})),Ht={..._E,...xE};function TE(l,a){return Gu(l,new Ht.classes.URLSearchParams,Object.assign({visitor:function(s,r,o,f){return Ht.isNode&&F.isBuffer(s)?(this.append(r,s.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},a))}function RE(l){return F.matchAll(/\w+|\[(\w*)]/g,l).map(a=>a[0]==="[]"?"":a[1]||a[0])}function OE(l){const a={},s=Object.keys(l);let r;const o=s.length;let f;for(r=0;r<o;r++)f=s[r],a[f]=l[f];return a}function og(l){function a(s,r,o,f){let d=s[f++];if(d==="__proto__")return!0;const h=Number.isFinite(+d),p=f>=s.length;return d=!d&&F.isArray(o)?o.length:d,p?(F.hasOwnProp(o,d)?o[d]=[o[d],r]:o[d]=r,!h):((!o[d]||!F.isObject(o[d]))&&(o[d]=[]),a(s,r,o[d],f)&&F.isArray(o[d])&&(o[d]=OE(o[d])),!h)}if(F.isFormData(l)&&F.isFunction(l.entries)){const s={};return F.forEachEntry(l,(r,o)=>{a(RE(r),o,s,0)}),s}return null}function CE(l,a,s){if(F.isString(l))try{return(a||JSON.parse)(l),F.trim(l)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(l)}const Is={transitional:ug,adapter:["xhr","http","fetch"],transformRequest:[function(a,s){const r=s.getContentType()||"",o=r.indexOf("application/json")>-1,f=F.isObject(a);if(f&&F.isHTMLForm(a)&&(a=new FormData(a)),F.isFormData(a))return o?JSON.stringify(og(a)):a;if(F.isArrayBuffer(a)||F.isBuffer(a)||F.isStream(a)||F.isFile(a)||F.isBlob(a)||F.isReadableStream(a))return a;if(F.isArrayBufferView(a))return a.buffer;if(F.isURLSearchParams(a))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let h;if(f){if(r.indexOf("application/x-www-form-urlencoded")>-1)return TE(a,this.formSerializer).toString();if((h=F.isFileList(a))||r.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return Gu(h?{"files[]":a}:a,p&&new p,this.formSerializer)}}return f||o?(s.setContentType("application/json",!1),CE(a)):a}],transformResponse:[function(a){const s=this.transitional||Is.transitional,r=s&&s.forcedJSONParsing,o=this.responseType==="json";if(F.isResponse(a)||F.isReadableStream(a))return a;if(a&&F.isString(a)&&(r&&!this.responseType||o)){const d=!(s&&s.silentJSONParsing)&&o;try{return JSON.parse(a)}catch(h){if(d)throw h.name==="SyntaxError"?Oe.from(h,Oe.ERR_BAD_RESPONSE,this,null,this.response):h}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ht.classes.FormData,Blob:Ht.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};F.forEach(["delete","get","head","post","put","patch"],l=>{Is.headers[l]={}});const jE=F.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),DE=l=>{const a={};let s,r,o;return l&&l.split(`
`).forEach(function(d){o=d.indexOf(":"),s=d.substring(0,o).trim().toLowerCase(),r=d.substring(o+1).trim(),!(!s||a[s]&&jE[s])&&(s==="set-cookie"?a[s]?a[s].push(r):a[s]=[r]:a[s]=a[s]?a[s]+", "+r:r)}),a},Qp=Symbol("internals");function Bs(l){return l&&String(l).trim().toLowerCase()}function bu(l){return l===!1||l==null?l:F.isArray(l)?l.map(bu):String(l)}function NE(l){const a=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(l);)a[r[1]]=r[2];return a}const UE=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function xf(l,a,s,r,o){if(F.isFunction(r))return r.call(this,a,s);if(o&&(a=s),!!F.isString(a)){if(F.isString(r))return a.indexOf(r)!==-1;if(F.isRegExp(r))return r.test(a)}}function ME(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,s,r)=>s.toUpperCase()+r)}function LE(l,a){const s=F.toCamelCase(" "+a);["get","set","has"].forEach(r=>{Object.defineProperty(l,r+s,{value:function(o,f,d){return this[r].call(this,a,o,f,d)},configurable:!0})})}let nn=class{constructor(a){a&&this.set(a)}set(a,s,r){const o=this;function f(h,p,m){const g=Bs(p);if(!g)throw new Error("header name must be a non-empty string");const S=F.findKey(o,g);(!S||o[S]===void 0||m===!0||m===void 0&&o[S]!==!1)&&(o[S||p]=bu(h))}const d=(h,p)=>F.forEach(h,(m,g)=>f(m,g,p));if(F.isPlainObject(a)||a instanceof this.constructor)d(a,s);else if(F.isString(a)&&(a=a.trim())&&!UE(a))d(DE(a),s);else if(F.isObject(a)&&F.isIterable(a)){let h={},p,m;for(const g of a){if(!F.isArray(g))throw TypeError("Object iterator must return a key-value pair");h[m=g[0]]=(p=h[m])?F.isArray(p)?[...p,g[1]]:[p,g[1]]:g[1]}d(h,s)}else a!=null&&f(s,a,r);return this}get(a,s){if(a=Bs(a),a){const r=F.findKey(this,a);if(r){const o=this[r];if(!s)return o;if(s===!0)return NE(o);if(F.isFunction(s))return s.call(this,o,r);if(F.isRegExp(s))return s.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,s){if(a=Bs(a),a){const r=F.findKey(this,a);return!!(r&&this[r]!==void 0&&(!s||xf(this,this[r],r,s)))}return!1}delete(a,s){const r=this;let o=!1;function f(d){if(d=Bs(d),d){const h=F.findKey(r,d);h&&(!s||xf(r,r[h],h,s))&&(delete r[h],o=!0)}}return F.isArray(a)?a.forEach(f):f(a),o}clear(a){const s=Object.keys(this);let r=s.length,o=!1;for(;r--;){const f=s[r];(!a||xf(this,this[f],f,a,!0))&&(delete this[f],o=!0)}return o}normalize(a){const s=this,r={};return F.forEach(this,(o,f)=>{const d=F.findKey(r,f);if(d){s[d]=bu(o),delete s[f];return}const h=a?ME(f):String(f).trim();h!==f&&delete s[f],s[h]=bu(o),r[h]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const s=Object.create(null);return F.forEach(this,(r,o)=>{r!=null&&r!==!1&&(s[o]=a&&F.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,s])=>a+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...s){const r=new this(a);return s.forEach(o=>r.set(o)),r}static accessor(a){const r=(this[Qp]=this[Qp]={accessors:{}}).accessors,o=this.prototype;function f(d){const h=Bs(d);r[h]||(LE(o,d),r[h]=!0)}return F.isArray(a)?a.forEach(f):f(a),this}};nn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);F.reduceDescriptors(nn.prototype,({value:l},a)=>{let s=a[0].toUpperCase()+a.slice(1);return{get:()=>l,set(r){this[s]=r}}});F.freezeMethods(nn);function Ef(l,a){const s=this||Is,r=a||s,o=nn.from(r.headers);let f=r.data;return F.forEach(l,function(h){f=h.call(s,f,o.normalize(),a?a.status:void 0)}),o.normalize(),f}function cg(l){return!!(l&&l.__CANCEL__)}function ki(l,a,s){Oe.call(this,l??"canceled",Oe.ERR_CANCELED,a,s),this.name="CanceledError"}F.inherits(ki,Oe,{__CANCEL__:!0});function fg(l,a,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?l(s):a(new Oe("Request failed with status code "+s.status,[Oe.ERR_BAD_REQUEST,Oe.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function zE(l){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return a&&a[1]||""}function kE(l,a){l=l||10;const s=new Array(l),r=new Array(l);let o=0,f=0,d;return a=a!==void 0?a:1e3,function(p){const m=Date.now(),g=r[f];d||(d=m),s[o]=p,r[o]=m;let S=f,x=0;for(;S!==o;)x+=s[S++],S=S%l;if(o=(o+1)%l,o===f&&(f=(f+1)%l),m-d<a)return;const T=g&&m-g;return T?Math.round(x*1e3/T):void 0}}function BE(l,a){let s=0,r=1e3/a,o,f;const d=(m,g=Date.now())=>{s=g,o=null,f&&(clearTimeout(f),f=null),l.apply(null,m)};return[(...m)=>{const g=Date.now(),S=g-s;S>=r?d(m,g):(o=m,f||(f=setTimeout(()=>{f=null,d(o)},r-S)))},()=>o&&d(o)]}const Nu=(l,a,s=3)=>{let r=0;const o=kE(50,250);return BE(f=>{const d=f.loaded,h=f.lengthComputable?f.total:void 0,p=d-r,m=o(p),g=d<=h;r=d;const S={loaded:d,total:h,progress:h?d/h:void 0,bytes:p,rate:m||void 0,estimated:m&&h&&g?(h-d)/m:void 0,event:f,lengthComputable:h!=null,[a?"download":"upload"]:!0};l(S)},s)},Kp=(l,a)=>{const s=l!=null;return[r=>a[0]({lengthComputable:s,total:l,loaded:r}),a[1]]},Jp=l=>(...a)=>F.asap(()=>l(...a)),HE=Ht.hasStandardBrowserEnv?((l,a)=>s=>(s=new URL(s,Ht.origin),l.protocol===s.protocol&&l.host===s.host&&(a||l.port===s.port)))(new URL(Ht.origin),Ht.navigator&&/(msie|trident)/i.test(Ht.navigator.userAgent)):()=>!0,qE=Ht.hasStandardBrowserEnv?{write(l,a,s,r,o,f){const d=[l+"="+encodeURIComponent(a)];F.isNumber(s)&&d.push("expires="+new Date(s).toGMTString()),F.isString(r)&&d.push("path="+r),F.isString(o)&&d.push("domain="+o),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(l){const a=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function VE(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function FE(l,a){return a?l.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):l}function dg(l,a,s){let r=!VE(a);return l&&(r||s==!1)?FE(l,a):a}const Pp=l=>l instanceof nn?{...l}:l;function Ha(l,a){a=a||{};const s={};function r(m,g,S,x){return F.isPlainObject(m)&&F.isPlainObject(g)?F.merge.call({caseless:x},m,g):F.isPlainObject(g)?F.merge({},g):F.isArray(g)?g.slice():g}function o(m,g,S,x){if(F.isUndefined(g)){if(!F.isUndefined(m))return r(void 0,m,S,x)}else return r(m,g,S,x)}function f(m,g){if(!F.isUndefined(g))return r(void 0,g)}function d(m,g){if(F.isUndefined(g)){if(!F.isUndefined(m))return r(void 0,m)}else return r(void 0,g)}function h(m,g,S){if(S in a)return r(m,g);if(S in l)return r(void 0,m)}const p={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:h,headers:(m,g,S)=>o(Pp(m),Pp(g),S,!0)};return F.forEach(Object.keys(Object.assign({},l,a)),function(g){const S=p[g]||o,x=S(l[g],a[g],g);F.isUndefined(x)&&S!==h||(s[g]=x)}),s}const hg=l=>{const a=Ha({},l);let{data:s,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:f,headers:d,auth:h}=a;a.headers=d=nn.from(d),a.url=rg(dg(a.baseURL,a.url,a.allowAbsoluteUrls),l.params,l.paramsSerializer),h&&d.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let p;if(F.isFormData(s)){if(Ht.hasStandardBrowserEnv||Ht.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((p=d.getContentType())!==!1){const[m,...g]=p?p.split(";").map(S=>S.trim()).filter(Boolean):[];d.setContentType([m||"multipart/form-data",...g].join("; "))}}if(Ht.hasStandardBrowserEnv&&(r&&F.isFunction(r)&&(r=r(a)),r||r!==!1&&HE(a.url))){const m=o&&f&&qE.read(f);m&&d.set(o,m)}return a},YE=typeof XMLHttpRequest<"u",$E=YE&&function(l){return new Promise(function(s,r){const o=hg(l);let f=o.data;const d=nn.from(o.headers).normalize();let{responseType:h,onUploadProgress:p,onDownloadProgress:m}=o,g,S,x,T,E;function D(){T&&T(),E&&E(),o.cancelToken&&o.cancelToken.unsubscribe(g),o.signal&&o.signal.removeEventListener("abort",g)}let C=new XMLHttpRequest;C.open(o.method.toUpperCase(),o.url,!0),C.timeout=o.timeout;function w(){if(!C)return;const M=nn.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),H={data:!h||h==="text"||h==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:M,config:l,request:C};fg(function(I){s(I),D()},function(I){r(I),D()},H),C=null}"onloadend"in C?C.onloadend=w:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(w)},C.onabort=function(){C&&(r(new Oe("Request aborted",Oe.ECONNABORTED,l,C)),C=null)},C.onerror=function(){r(new Oe("Network Error",Oe.ERR_NETWORK,l,C)),C=null},C.ontimeout=function(){let G=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const H=o.transitional||ug;o.timeoutErrorMessage&&(G=o.timeoutErrorMessage),r(new Oe(G,H.clarifyTimeoutError?Oe.ETIMEDOUT:Oe.ECONNABORTED,l,C)),C=null},f===void 0&&d.setContentType(null),"setRequestHeader"in C&&F.forEach(d.toJSON(),function(G,H){C.setRequestHeader(H,G)}),F.isUndefined(o.withCredentials)||(C.withCredentials=!!o.withCredentials),h&&h!=="json"&&(C.responseType=o.responseType),m&&([x,E]=Nu(m,!0),C.addEventListener("progress",x)),p&&C.upload&&([S,T]=Nu(p),C.upload.addEventListener("progress",S),C.upload.addEventListener("loadend",T)),(o.cancelToken||o.signal)&&(g=M=>{C&&(r(!M||M.type?new ki(null,l,C):M),C.abort(),C=null)},o.cancelToken&&o.cancelToken.subscribe(g),o.signal&&(o.signal.aborted?g():o.signal.addEventListener("abort",g)));const j=zE(o.url);if(j&&Ht.protocols.indexOf(j)===-1){r(new Oe("Unsupported protocol "+j+":",Oe.ERR_BAD_REQUEST,l));return}C.send(f||null)})},GE=(l,a)=>{const{length:s}=l=l?l.filter(Boolean):[];if(a||s){let r=new AbortController,o;const f=function(m){if(!o){o=!0,h();const g=m instanceof Error?m:this.reason;r.abort(g instanceof Oe?g:new ki(g instanceof Error?g.message:g))}};let d=a&&setTimeout(()=>{d=null,f(new Oe(`timeout ${a} of ms exceeded`,Oe.ETIMEDOUT))},a);const h=()=>{l&&(d&&clearTimeout(d),d=null,l.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),l=null)};l.forEach(m=>m.addEventListener("abort",f));const{signal:p}=r;return p.unsubscribe=()=>F.asap(h),p}},XE=function*(l,a){let s=l.byteLength;if(s<a){yield l;return}let r=0,o;for(;r<s;)o=r+a,yield l.slice(r,o),r=o},ZE=async function*(l,a){for await(const s of QE(l))yield*XE(s,a)},QE=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const a=l.getReader();try{for(;;){const{done:s,value:r}=await a.read();if(s)break;yield r}}finally{await a.cancel()}},Wp=(l,a,s,r)=>{const o=ZE(l,a);let f=0,d,h=p=>{d||(d=!0,r&&r(p))};return new ReadableStream({async pull(p){try{const{done:m,value:g}=await o.next();if(m){h(),p.close();return}let S=g.byteLength;if(s){let x=f+=S;s(x)}p.enqueue(new Uint8Array(g))}catch(m){throw h(m),m}},cancel(p){return h(p),o.return()}},{highWaterMark:2})},Xu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",mg=Xu&&typeof ReadableStream=="function",KE=Xu&&(typeof TextEncoder=="function"?(l=>a=>l.encode(a))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),pg=(l,...a)=>{try{return!!l(...a)}catch{return!1}},JE=mg&&pg(()=>{let l=!1;const a=new Request(Ht.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!a}),Ip=64*1024,Bf=mg&&pg(()=>F.isReadableStream(new Response("").body)),Uu={stream:Bf&&(l=>l.body)};Xu&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!Uu[a]&&(Uu[a]=F.isFunction(l[a])?s=>s[a]():(s,r)=>{throw new Oe(`Response type '${a}' is not supported`,Oe.ERR_NOT_SUPPORT,r)})})})(new Response);const PE=async l=>{if(l==null)return 0;if(F.isBlob(l))return l.size;if(F.isSpecCompliantForm(l))return(await new Request(Ht.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(F.isArrayBufferView(l)||F.isArrayBuffer(l))return l.byteLength;if(F.isURLSearchParams(l)&&(l=l+""),F.isString(l))return(await KE(l)).byteLength},WE=async(l,a)=>{const s=F.toFiniteNumber(l.getContentLength());return s??PE(a)},IE=Xu&&(async l=>{let{url:a,method:s,data:r,signal:o,cancelToken:f,timeout:d,onDownloadProgress:h,onUploadProgress:p,responseType:m,headers:g,withCredentials:S="same-origin",fetchOptions:x}=hg(l);m=m?(m+"").toLowerCase():"text";let T=GE([o,f&&f.toAbortSignal()],d),E;const D=T&&T.unsubscribe&&(()=>{T.unsubscribe()});let C;try{if(p&&JE&&s!=="get"&&s!=="head"&&(C=await WE(g,r))!==0){let H=new Request(a,{method:"POST",body:r,duplex:"half"}),te;if(F.isFormData(r)&&(te=H.headers.get("content-type"))&&g.setContentType(te),H.body){const[I,K]=Kp(C,Nu(Jp(p)));r=Wp(H.body,Ip,I,K)}}F.isString(S)||(S=S?"include":"omit");const w="credentials"in Request.prototype;E=new Request(a,{...x,signal:T,method:s.toUpperCase(),headers:g.normalize().toJSON(),body:r,duplex:"half",credentials:w?S:void 0});let j=await fetch(E);const M=Bf&&(m==="stream"||m==="response");if(Bf&&(h||M&&D)){const H={};["status","statusText","headers"].forEach(ne=>{H[ne]=j[ne]});const te=F.toFiniteNumber(j.headers.get("content-length")),[I,K]=h&&Kp(te,Nu(Jp(h),!0))||[];j=new Response(Wp(j.body,Ip,I,()=>{K&&K(),D&&D()}),H)}m=m||"text";let G=await Uu[F.findKey(Uu,m)||"text"](j,l);return!M&&D&&D(),await new Promise((H,te)=>{fg(H,te,{data:G,headers:nn.from(j.headers),status:j.status,statusText:j.statusText,config:l,request:E})})}catch(w){throw D&&D(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new Oe("Network Error",Oe.ERR_NETWORK,l,E),{cause:w.cause||w}):Oe.from(w,w&&w.code,l,E)}}),Hf={http:mE,xhr:$E,fetch:IE};F.forEach(Hf,(l,a)=>{if(l){try{Object.defineProperty(l,"name",{value:a})}catch{}Object.defineProperty(l,"adapterName",{value:a})}});const ey=l=>`- ${l}`,ew=l=>F.isFunction(l)||l===null||l===!1,yg={getAdapter:l=>{l=F.isArray(l)?l:[l];const{length:a}=l;let s,r;const o={};for(let f=0;f<a;f++){s=l[f];let d;if(r=s,!ew(s)&&(r=Hf[(d=String(s)).toLowerCase()],r===void 0))throw new Oe(`Unknown adapter '${d}'`);if(r)break;o[d||"#"+f]=r}if(!r){const f=Object.entries(o).map(([h,p])=>`adapter ${h} `+(p===!1?"is not supported by the environment":"is not available in the build"));let d=a?f.length>1?`since :
`+f.map(ey).join(`
`):" "+ey(f[0]):"as no adapter specified";throw new Oe("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return r},adapters:Hf};function wf(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new ki(null,l)}function ty(l){return wf(l),l.headers=nn.from(l.headers),l.data=Ef.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),yg.getAdapter(l.adapter||Is.adapter)(l).then(function(r){return wf(l),r.data=Ef.call(l,l.transformResponse,r),r.headers=nn.from(r.headers),r},function(r){return cg(r)||(wf(l),r&&r.response&&(r.response.data=Ef.call(l,l.transformResponse,r.response),r.response.headers=nn.from(r.response.headers))),Promise.reject(r)})}const gg="1.9.0",Zu={};["object","boolean","number","function","string","symbol"].forEach((l,a)=>{Zu[l]=function(r){return typeof r===l||"a"+(a<1?"n ":" ")+l}});const ny={};Zu.transitional=function(a,s,r){function o(f,d){return"[Axios v"+gg+"] Transitional option '"+f+"'"+d+(r?". "+r:"")}return(f,d,h)=>{if(a===!1)throw new Oe(o(d," has been removed"+(s?" in "+s:"")),Oe.ERR_DEPRECATED);return s&&!ny[d]&&(ny[d]=!0,console.warn(o(d," has been deprecated since v"+s+" and will be removed in the near future"))),a?a(f,d,h):!0}};Zu.spelling=function(a){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${a}`),!0)};function tw(l,a,s){if(typeof l!="object")throw new Oe("options must be an object",Oe.ERR_BAD_OPTION_VALUE);const r=Object.keys(l);let o=r.length;for(;o-- >0;){const f=r[o],d=a[f];if(d){const h=l[f],p=h===void 0||d(h,f,l);if(p!==!0)throw new Oe("option "+f+" must be "+p,Oe.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new Oe("Unknown option "+f,Oe.ERR_BAD_OPTION)}}const Su={assertOptions:tw,validators:Zu},tl=Su.validators;let La=class{constructor(a){this.defaults=a||{},this.interceptors={request:new Zp,response:new Zp}}async request(a,s){try{return await this._request(a,s)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?f&&!String(r.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+f):r.stack=f}catch{}}throw r}}_request(a,s){typeof a=="string"?(s=s||{},s.url=a):s=a||{},s=Ha(this.defaults,s);const{transitional:r,paramsSerializer:o,headers:f}=s;r!==void 0&&Su.assertOptions(r,{silentJSONParsing:tl.transitional(tl.boolean),forcedJSONParsing:tl.transitional(tl.boolean),clarifyTimeoutError:tl.transitional(tl.boolean)},!1),o!=null&&(F.isFunction(o)?s.paramsSerializer={serialize:o}:Su.assertOptions(o,{encode:tl.function,serialize:tl.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Su.assertOptions(s,{baseUrl:tl.spelling("baseURL"),withXsrfToken:tl.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let d=f&&F.merge(f.common,f[s.method]);f&&F.forEach(["delete","get","head","post","put","patch","common"],E=>{delete f[E]}),s.headers=nn.concat(d,f);const h=[];let p=!0;this.interceptors.request.forEach(function(D){typeof D.runWhen=="function"&&D.runWhen(s)===!1||(p=p&&D.synchronous,h.unshift(D.fulfilled,D.rejected))});const m=[];this.interceptors.response.forEach(function(D){m.push(D.fulfilled,D.rejected)});let g,S=0,x;if(!p){const E=[ty.bind(this),void 0];for(E.unshift.apply(E,h),E.push.apply(E,m),x=E.length,g=Promise.resolve(s);S<x;)g=g.then(E[S++],E[S++]);return g}x=h.length;let T=s;for(S=0;S<x;){const E=h[S++],D=h[S++];try{T=E(T)}catch(C){D.call(this,C);break}}try{g=ty.call(this,T)}catch(E){return Promise.reject(E)}for(S=0,x=m.length;S<x;)g=g.then(m[S++],m[S++]);return g}getUri(a){a=Ha(this.defaults,a);const s=dg(a.baseURL,a.url,a.allowAbsoluteUrls);return rg(s,a.params,a.paramsSerializer)}};F.forEach(["delete","get","head","options"],function(a){La.prototype[a]=function(s,r){return this.request(Ha(r||{},{method:a,url:s,data:(r||{}).data}))}});F.forEach(["post","put","patch"],function(a){function s(r){return function(f,d,h){return this.request(Ha(h||{},{method:a,headers:r?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}La.prototype[a]=s(),La.prototype[a+"Form"]=s(!0)});let nw=class vg{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(f){s=f});const r=this;this.promise.then(o=>{if(!r._listeners)return;let f=r._listeners.length;for(;f-- >0;)r._listeners[f](o);r._listeners=null}),this.promise.then=o=>{let f;const d=new Promise(h=>{r.subscribe(h),f=h}).then(o);return d.cancel=function(){r.unsubscribe(f)},d},a(function(f,d,h){r.reason||(r.reason=new ki(f,d,h),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const s=this._listeners.indexOf(a);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const a=new AbortController,s=r=>{a.abort(r)};return this.subscribe(s),a.signal.unsubscribe=()=>this.unsubscribe(s),a.signal}static source(){let a;return{token:new vg(function(o){a=o}),cancel:a}}};function lw(l){return function(s){return l.apply(null,s)}}function aw(l){return F.isObject(l)&&l.isAxiosError===!0}const qf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(qf).forEach(([l,a])=>{qf[a]=l});function bg(l){const a=new La(l),s=Ky(La.prototype.request,a);return F.extend(s,La.prototype,a,{allOwnKeys:!0}),F.extend(s,a,null,{allOwnKeys:!0}),s.create=function(o){return bg(Ha(l,o))},s}const mt=bg(Is);mt.Axios=La;mt.CanceledError=ki;mt.CancelToken=nw;mt.isCancel=cg;mt.VERSION=gg;mt.toFormData=Gu;mt.AxiosError=Oe;mt.Cancel=mt.CanceledError;mt.all=function(a){return Promise.all(a)};mt.spread=lw;mt.isAxiosError=aw;mt.mergeConfig=Ha;mt.AxiosHeaders=nn;mt.formToJSON=l=>og(F.isHTMLForm(l)?new FormData(l):l);mt.getAdapter=yg.getAdapter;mt.HttpStatusCode=qf;mt.default=mt;const{Axios:Aw,AxiosError:_w,CanceledError:Tw,isCancel:Rw,CancelToken:Ow,VERSION:Cw,all:jw,Cancel:Dw,isAxiosError:Nw,spread:Uw,toFormData:Mw,AxiosHeaders:Lw,HttpStatusCode:zw,formToJSON:kw,getAdapter:Bw,mergeConfig:Hw}=mt,iw="http://127.0.0.1:8000/",Sg=mt.create({baseURL:iw,timeout:5e3,headers:{"Content-Type":"application/json",accept:"application/json"}});Sg.interceptors.request.use(l=>{const a=localStorage.getItem("access_token");return a&&(l.headers.Authorization=`JWT ${a}`),l},l=>Promise.reject(l));const sw=Pe.createContext({}),xg=!0;function rw({baseColor:l,highlightColor:a,width:s,height:r,borderRadius:o,circle:f,direction:d,duration:h,enableAnimation:p=xg,customHighlightBackground:m}){const g={};return d==="rtl"&&(g["--animation-direction"]="reverse"),typeof h=="number"&&(g["--animation-duration"]=`${h}s`),p||(g["--pseudo-element-display"]="none"),(typeof s=="string"||typeof s=="number")&&(g.width=s),(typeof r=="string"||typeof r=="number")&&(g.height=r),(typeof o=="string"||typeof o=="number")&&(g.borderRadius=o),f&&(g.borderRadius="50%"),typeof l<"u"&&(g["--base-color"]=l),typeof a<"u"&&(g["--highlight-color"]=a),typeof m=="string"&&(g["--custom-highlight-background"]=m),g}function il({count:l=1,wrapper:a,className:s,containerClassName:r,containerTestId:o,circle:f=!1,style:d,...h}){var p,m,g;const S=Pe.useContext(sw),x={...h};for(const[M,G]of Object.entries(h))typeof G>"u"&&delete x[M];const T={...S,...x,circle:f},E={...d,...rw(T)};let D="react-loading-skeleton";s&&(D+=` ${s}`);const C=(p=T.inline)!==null&&p!==void 0?p:!1,w=[],j=Math.ceil(l);for(let M=0;M<j;M++){let G=E;if(j>l&&M===j-1){const te=(m=G.width)!==null&&m!==void 0?m:"100%",I=l%1,K=typeof te=="number"?te*I:`calc(${te} * ${I})`;G={...G,width:K}}const H=Pe.createElement("span",{className:D,style:G,key:M},"‌");C?w.push(H):w.push(Pe.createElement(Pe.Fragment,{key:M},H,Pe.createElement("br",null)))}return Pe.createElement("span",{className:r,"data-testid":o,"aria-live":"polite","aria-busy":(g=T.enableAnimation)!==null&&g!==void 0?g:xg},a?w.map((M,G)=>Pe.createElement(a,{key:G},M)):w)}function Af(){return v.jsxs(v.Fragment,{children:[v.jsx(il,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(il,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(il,{height:500,width:350,borderRadius:40,baseColor:"#ffd700",highlightColor:"#909098"}),v.jsx(il,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(il,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"})]})}function uw(){const[l,a]=A.useState(!1),[s,r]=A.useState("Growing Industry Sector"),[o,f]=A.useState([]),[d,h]=A.useState([]),[p,m]=A.useState([]),[g,S]=A.useState([]),[x,T]=A.useState(null),[E,D]=A.useState(null),[C,w]=A.useState(!0),[j,M]=A.useState(!1),[G,H]=A.useState(null),te=async()=>{try{const K=await Qy.getLatestTrends();console.log("Latest trends:",K),f(K[0]),h(K[1]),m(K[2]),S(K[3]),w(!1),H(new Date().toLocaleTimeString())}catch(K){console.error("Failed to fetch latest trends:",K),w(!1)}};A.useEffect(()=>{(async()=>{const ne=await On.getCurrentUser();D(ne)})()},[]),A.useEffect(()=>{Ke.connect();const K=W=>{console.log("WebSocket connected:",W.message),M(!0)},ne=W=>{console.log("WebSocket disconnected:",W.message),M(!1)},xe=W=>{console.log("Prediction completed:",W),te()},ce=W=>{console.log("Dataset uploaded:",W)};return Ke.onConnectionEstablished(K),Ke.onConnectionLost(ne),Ke.onPredictionCompleted(xe),Ke.onDatasetUploaded(ce),te(),()=>{Ke.removeEventListener("connection_established",K),Ke.removeEventListener("connection_lost",ne),Ke.removeEventListener("prediction_completed",xe),Ke.removeEventListener("dataset_uploaded",ce),Ke.disconnect()}},[]),A.useEffect(()=>{s=="Growing Industry Sector"?T("Top 5 Growing Industry Sectors in"):s=="Industry Sector Revenue"?T("Top 5 Industry Sectors by Revenue in"):T("Top 5 Least Crowded Industry Sectors in")},[s]),l?document.body.style.overflow="hidden":document.body.style.overflow="auto";const I=(K,ne,xe)=>{if(xe=="Growing Industry Sector")return o.find(ce=>ce.type===K&&ce.rank===ne);if(xe=="Industry Sector Revenue")return d.find(ce=>ce.type===K&&ce.rank===ne);if(xe=="Least Crowded")return p.find(ce=>ce.type===K&&ce.rank===ne)};return v.jsxs(v.Fragment,{children:[l&&v.jsx(Ox,{showModal:()=>a(!1)}),v.jsx("nav",{children:v.jsx(od,{showModal:()=>a(!0)})}),v.jsxs("main",{className:"home",children:[v.jsxs("section",{className:"short-term",id:"short-term",children:[v.jsxs("div",{className:"filter",children:[v.jsx("p",{children:"Filter Results"}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Growing Industry Sector"?"active":""}`,onClick:()=>r("Growing Industry Sector"),children:v.jsx("span",{children:"Growing Industry Sector"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Industry Sector Revenue"?"active":""}`,onClick:()=>r("Industry Sector Revenue"),children:v.jsx("span",{children:"Industry Sector Revenue"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Least Crowded"?"active":""}`,onClick:()=>r("Least Crowded"),children:v.jsx("span",{children:"Least Crowded"})})})]}),v.jsxs("h1",{children:["Short-Term Outlook: ",x," ",g.length>0?g[0]:"🔮"]}),v.jsxs("section",{className:"short-term-contents",children:[C&&v.jsx(Af,{}),!C&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"short-term",data:I("short-term",4,s),filterResult:s}),v.jsx(Ft,{topNumber:2,type:"short-term",data:I("short-term",2,s),filterResult:s}),v.jsx(Ft,{topNumber:1,type:"short-term",data:I("short-term",1,s),filterResult:s}),v.jsx(Ft,{topNumber:3,type:"short-term",data:I("short-term",3,s),filterResult:s}),v.jsx(Ft,{topNumber:5,type:"short-term",data:I("short-term",5,s),filterResult:s})]})]})]}),v.jsxs("section",{className:"mid-term",id:"mid-term",children:[v.jsxs("h1",{children:["Mid-Term Outlook: ",x," ",g.length>0?g[1]:"🔮"]}),v.jsxs("section",{className:"mid-term-contents",children:[C&&v.jsx(Af,{}),!C&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"mid-term",data:I("mid-term",4,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:2,type:"mid-term",data:I("mid-term",2,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:1,type:"mid-term",data:I("mid-term",1,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:3,type:"mid-term",data:I("mid-term",3,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:5,type:"mid-term",data:I("mid-term",5,s),filterResult:s,color:"dark"})]})]})]}),v.jsxs("section",{className:"long-term",id:"long-term",children:[v.jsxs("h1",{children:["Long-Term Outlook: ",x," ",g.length>0?g[2]:"🔮"," ","(Based on Business Count)"]}),v.jsxs("section",{className:"long-term-contents",children:[C&&v.jsx(Af,{}),!C&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"long-term",data:I("long-term",4,s),filterResult:s}),v.jsx(Ft,{topNumber:2,type:"long-term",data:I("long-term",2,s),filterResult:s}),v.jsx(Ft,{topNumber:1,type:"long-term",data:I("long-term",1,s),filterResult:s}),v.jsx(Ft,{topNumber:3,type:"long-term",data:I("long-term",3,s),filterResult:s}),v.jsx(Ft,{topNumber:5,type:"long-term",data:I("long-term",5,s),filterResult:s})]})]})]})]})]})}function ow(){const l=$n(),[a,s]=A.useState([]),r=Mi().shape({email:en().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:en().required("Password is required.").min(12).max(16).matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:en().oneOf([td("password"),null],"Password don't match.").required("Confirm Password is required.")}),{register:o,handleSubmit:f,formState:{errors:d},watch:h}=Ui({resolver:Qs(r),mode:"all"}),p=g=>{console.log(g),Sg.post("register/",{email:g.email,password:g.confirmPassword}).then(()=>{l("/login")})},m=h("password","");return A.useEffect(()=>{let g=[];m.length<12&&g.push("- At least 12 characters."),m.length>16&&g.push("- Not exceed 16 characters."),/[0-9]/.test(m)||g.push("- At least one number."),/[a-z]/.test(m)||g.push("- At least one lowercase letter."),/[A-Z]/.test(m)||g.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(m)||g.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(m)&&g.push("Must not contain spaces"),s(g)},[m]),v.jsx("main",{className:"register-page",children:v.jsxs("form",{onSubmit:f(p),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),d.email&&v.jsx("span",{children:d.email.message}),v.jsx("input",{type:"email",name:"email",id:"email",placeholder:"Enter email...",...o("email")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password:"}),a.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),a.map((g,S)=>v.jsx("span",{children:g},S))]}),v.jsx("input",{type:"password",name:"password",id:"password",placeholder:"Enter password...",...o("password")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password:"}),d.confirmPassword&&v.jsx("span",{children:d.confirmPassword.message}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",placeholder:"Enter confirm password...",...o("confirmPassword")})]}),v.jsx("button",{type:"submit",children:"Create Account"})]})})}function cw(){const l=localStorage.getItem("access_token");return l?sessionStorage.getItem("current_user")&&!JSON.parse(sessionStorage.getItem("current_user")).is_superuser?v.jsx(cf,{to:"/"}):l?v.jsx(jb,{}):v.jsx(cf,{to:"/"}):v.jsx(cf,{to:"/"})}const Hs="http://127.0.0.1:8000/",fw="http://127.0.0.1:8000/users/list_by_status/?is_active=";class dw{async register(a,s,r,o,f){try{const d=await fetch(Hs+"register/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:s,first_name:r,last_name:o,is_superuser:f})});return d.ok,d}catch(d){console.log("Failed to register!",d)}}async updateUserInfo(a,s,r){try{const o=await fetch(Hs+"users/"+a+"/",{method:"PUT",headers:On.getAuthHeader(),body:JSON.stringify({first_name:s,last_name:r})});if(!o.ok){const d=await o.json();return console.log("failed to update user info",d),o.status}const f=await o.json();return o.status}catch(o){console.log("Failed to update user info!",o)}}async updateProfile(a,s){const r=new FormData;r.append("profile_picture",s);try{const o=On.getAccessToken(),f=await fetch(Hs+"users/"+a+"/",{method:"PATCH",headers:{Authorization:o?`JWT ${o}`:""},body:r});if(!f.ok){const d=await f.json();return console.log("failed to update user profile picture",d),f.status}return f.status}catch(o){return console.log("Failed to update user profile picture!",o),o}}async getAllUsers(){try{const a=await fetch(Hs+"users/",{method:"GET",headers:On.getAuthHeader()});if(!a.ok){const r=await a.json();return console.log("failed to fetch all users"),r}const s=await a.json();return Array.from(s)}catch(a){console.log("Failed to get all users!",a)}}async getUsersByisActive(a){try{const s=await fetch(fw+a,{method:"GET",headers:On.getAuthHeader()});if(!s.ok){const o=await s.json();return console.log("failed to fetch is_active users"),o}const r=await s.json();return Array.from(r)}catch(s){console.log("Failed to get users by status!",s)}}async changeUserStatus(a){try{const s=await fetch(Hs+"users/change_status/",{method:"PATCH",headers:On.getAuthHeader(),body:JSON.stringify({user_id:a})});if(!s.ok){const r=await s.json();return console.log("failed to change user status",r),!1}return!0}catch(s){console.log("Failed to change user status!",s)}}}const za=new dw;function hw({showRegisterUserModal:l}){const a=$n(),[s,r]=A.useState(!1),[o,f]=A.useState([]),[d,h]=A.useState(!1),[p,m]=A.useState(!1),g=Mi().shape({firstName:en().required("First name is required."),lastName:en().required("Last name is required."),email:en().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:en().required("Password is required.").matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:en().required("Confirm password is required.").oneOf([td("password"),null],"Password don't match.")}),{register:S,handleSubmit:x,formState:{errors:T,isValid:E},watch:D}=Ui({resolver:Qs(g),mode:"all"}),C=async j=>{h(!0);try{(await za.register(j.email,j.password,j.firstName,j.lastName,j.isAdmin)).ok?(a("/user-management",{state:{registrationSuccess:!0}}),l()):m(!0)}catch(M){console.error("Error during registration:",M)}finally{h(!1)}},w=D("password","");return A.useEffect(()=>{let j=[];w.length<12&&j.push("- At least 12 characters."),w.length>16&&j.push("- Not exceed 16 characters."),/[0-9]/.test(w)||j.push("- At least one number."),/[a-z]/.test(w)||j.push("- At least one lowercase letter."),/[A-Z]/.test(w)||j.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(w)||j.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(w)&&j.push("Must not contain spaces"),f(j)},[w]),A.useEffect(()=>{p&&setTimeout(()=>{m(!1)},5e3)},[p]),v.jsxs(v.Fragment,{children:[p&&v.jsx(Hn,{message:"Registration failed. Please try again.",type:"danger"}),v.jsxs("div",{className:"register-user-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsxs("header",{children:[v.jsx("button",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),onClick:l,children:v.jsx("img",{src:s?ku:Du,alt:"close-icon"})}),v.jsx("h2",{children:"Register New User"})]}),v.jsxs("form",{onSubmit:x(C),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First Name *"}),v.jsx("input",{type:"text",name:"first-name",id:"first-name",required:!0,placeholder:"Enter first name...",...S("firstName")}),T.firstName&&v.jsx("span",{children:T.firstName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last Name *"}),v.jsx("input",{type:"text",name:"last-name",id:"last-name",required:!0,placeholder:"Enter last name...",...S("lastName")}),T.lastName&&v.jsx("span",{children:T.lastName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email *"}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email...",...S("email")}),T.email&&v.jsx("span",{children:T.email.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password *"}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password...",...S("password")}),o.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),o.map((j,M)=>v.jsx("span",{children:j},M))]})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password *"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",required:!0,placeholder:"Enter confirm password...",...S("confirmPassword")}),T.confirmPassword&&v.jsx("span",{children:T.confirmPassword.message})]}),v.jsxs("fieldset",{children:[v.jsx("input",{type:"checkbox",name:"is-admin",id:"is-admin",...S("isAdmin")}),v.jsx("label",{htmlFor:"is-admin",children:"Click the checkbox if the new user is an admin."})]}),v.jsxs("button",{className:"submit-button",disabled:!E||d,children:[d&&v.jsx(ca,{}),d?"Registering...":"Register"]})]})]})]})]})}const mw="/assets/active-user-Bd-Ue2Qb.svg",pw="/assets/delete-account-BwubX78G.svg";function yw({isShow:l,action:a,userId:s}){let r;const o=$n(),[f,d]=A.useState(!1),[h,p]=A.useState(!1);a==="Activate"?r=mw:r=pw;const m=async()=>{p(!0);try{if(await za.changeUserStatus(s))o("/user-management",{state:{changeStatusSuccess:!0}}),l();else return}catch(g){console.log("Failed to change user status!",g)}finally{p(!1)}};return v.jsxs("section",{className:"confirm-status-change-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),onClick:l,className:"close-btn",children:v.jsx("img",{src:f?ku:Du,alt:"close-icon"})}),v.jsx("img",{src:r,alt:"icon","data-action":a}),v.jsxs("h2",{children:[a," Account"]}),v.jsxs("p",{children:["Are you sure you want to ",a.toLowerCase()," the account of this user?"]}),v.jsxs("div",{className:"group-buttons",children:[v.jsxs("button",{className:"submit-button",onClick:l,children:["No, keep ",a.toLowerCase()]}),v.jsxs("button",{className:"submit-button",onClick:m,disabled:h,children:[h&&v.jsx(ca,{}),h?"Processing...":`Yes, ${a.toLowerCase()}`]})]})]})]})}function gw(){var te,I;const a=cl(),s=$n(),[r,o]=A.useState("All"),[f,d]=A.useState(!1),[h,p]=A.useState(!1),[m,g]=A.useState([]),[S,x]=A.useState(!0),[T,E]=A.useState(!1),[D,C]=A.useState({userId:null,action:null}),[w,j]=A.useState(!1),M=(te=a.state)==null?void 0:te.registrationSuccess,G=(I=a.state)==null?void 0:I.changeStatusSuccess,H=async()=>{g([]),x(!0);try{let K;r==="All"?K=await za.getAllUsers():r==="Active"?K=await za.getUsersByisActive(1):r==="Inactive"&&(K=await za.getUsersByisActive(0)),g(K||[]),console.log(`Users ${r}:`,K)}catch(K){console.error("Failed to fetch users:",K),g([])}finally{x(!1)}};return A.useEffect(()=>{M&&(p(!0),setTimeout(()=>{p(!1),s(a.pathname,{state:{registrationSuccess:!1}})},5e3)),G&&(j(!0),setTimeout(()=>{j(!1),s(a.pathname,{state:{changeStatusSuccess:!1}})},5e3))},[M,G]),A.useEffect(()=>{const K=ce=>{console.log("User created via WebSocket:",ce),p(!0),setTimeout(()=>p(!1),5e3),H()},ne=ce=>{console.log("User updated via WebSocket:",ce),H()},xe=ce=>{console.log("User status changed via WebSocket:",ce),j(!0),setTimeout(()=>j(!1),5e3),H()};return Ke.onUserCreated(K),Ke.onUserUpdated(ne),Ke.onUserStatusChanged(xe),()=>{Ke.removeEventListener("user_created",K),Ke.removeEventListener("user_updated",ne),Ke.removeEventListener("user_status_changed",xe)}},[]),A.useEffect(()=>{H()},[r]),v.jsxs(v.Fragment,{children:[f&&v.jsx(hw,{showRegisterUserModal:()=>d(!1)}),h&&v.jsx(Hn,{message:"Registration successful!",type:"success"}),T&&v.jsx(yw,{isShow:()=>E(!1),action:D.action,userId:D.userId}),w&&v.jsx(Hn,{message:"User status changed successfully!",type:"success"}),v.jsx("nav",{children:v.jsx(od,{})}),v.jsxs("main",{className:"user-management",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"User Management"}),v.jsx("button",{onClick:()=>d(!0),children:"Add User"})]})}),v.jsx("section",{children:v.jsxs("div",{className:"table-header",children:[v.jsxs("h2",{children:[r," Users ",!S&&v.jsxs("span",{children:["(",m.length,")"]})]}),v.jsxs("ul",{children:[v.jsx("li",{className:r==="All"?"active":"",onClick:()=>o("All"),children:"All"}),v.jsx("li",{className:r==="Active"?"active":"",onClick:()=>o("Active"),children:"Active"}),v.jsx("li",{className:r==="Inactive"?"active":"",onClick:()=>o("Inactive"),children:"Inactive"})]})]})}),S&&v.jsx("section",{className:"grid-container",children:Array.from({length:8}).map((K,ne)=>v.jsxs("article",{className:"user-container",children:[v.jsx(il,{height:40,width:40,circle:!0}),v.jsx(il,{height:20,width:100}),v.jsxs("section",{className:"status-and-role",children:[v.jsx(il,{height:20,width:60}),v.jsx(il,{height:20,width:60})]}),v.jsx(il,{height:40,width:200,borderRadius:40})]},ne))}),!S&&m.length==0&&v.jsx("p",{children:"No users found."}),m.length>0&&v.jsx("section",{className:"grid-container",children:m.map((K,ne)=>v.jsxs("article",{className:"user-container",children:[v.jsx("img",{src:K.profile_picture?`http://127.0.0.1:8000${K.profile_picture}`:Ru,alt:"Profile"}),v.jsxs("h2",{children:[K.first_name," ",K.last_name]}),v.jsxs("section",{className:"status-and-role",children:[v.jsxs("span",{className:"status","data-status":K.is_active===!0?"active":"inactive",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:K.is_active===!0?"Active":"Inactive"})]}),K.is_superuser===!0&&v.jsxs("span",{className:"role","data-role":"admin",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:"Admin"})]})]}),v.jsx("button",{onClick:()=>{E(!0),C({userId:K.id,action:K.is_active===!0?"Deactivate":"Activate"})},children:K.is_active===!0?"Deactivate Account":"Activate Account"})]},ne))})]})]})}function vw(){const[l,a]=A.useState({id:JSON.parse(sessionStorage.getItem("current_user")).id,firstName:JSON.parse(sessionStorage.getItem("current_user")).first_name,lastName:JSON.parse(sessionStorage.getItem("current_user")).last_name,email:JSON.parse(sessionStorage.getItem("current_user")).email,is_superuser:JSON.parse(sessionStorage.getItem("current_user")).is_superuser,profile:JSON.parse(sessionStorage.getItem("current_user")).profile_picture}),[s,r]=A.useState(null),[o,f]=A.useState(!1),[d,h]=A.useState(!1),[p,m]=A.useState(!1),[g,S]=A.useState(null),[x,T]=A.useState(null),[E,D]=A.useState(!1),[C,w]=A.useState(null),j=W=>{const[re,fe]=W.split("@");return`${re[0]+"*".repeat(re.length-1)}@${fe}`},M=W=>{if(W.target.files.length>0){const re=W.target.files[0];S(re);const fe=new FileReader;fe.onload=ve=>{w(ve.target.result)},fe.readAsDataURL(re)}else S(null),w(null)},{register:G,handleSubmit:H,watch:te}=Ui({defaultValues:{firstName:l.firstName,lastName:l.lastName},mode:"all"}),I=te("firstName",""),K=te("lastName","");A.useEffect(()=>{const W=I.trim(),re=K.trim(),fe=W!=="",ve=re!=="",q=W!==l.firstName,P=re!==l.lastName;f(fe&&ve&&(q||P))},[I,K,l]);const ne=async W=>{m(!0),h(!0),console.log("data:",W);try{const re=await za.updateUserInfo(JSON.parse(sessionStorage.getItem("current_user")).id,W.firstName,W.lastName);console.log("response received:",re),!re===200?(console.log("failed to update user info",re),r(re)):(console.log("going to update user info..."),ce(),m(!1),r(re))}catch(re){console.log("Failed to update user info!",re)}finally{h(!1)}},xe=async(W,re)=>{D(!0);const fe=await za.updateProfile(W,re);!fe===200?(console.log("failed to update user profile picture",fe),T(fe)):(console.log("going to update user profile picture..."),ce(),T(fe),D(!1))},ce=async()=>{const W=await On.getCurrentUser();sessionStorage.setItem("current_user",JSON.stringify(W)),a({id:W.id,firstName:W.first_name,lastName:W.last_name,email:W.email,is_superuser:W.is_superuser,profile:W.profile_picture})};return A.useEffect(()=>{s!=null&&setTimeout(()=>{r(null)},5e3)},[s]),A.useEffect(()=>{const W=JSON.parse(sessionStorage.getItem("current_user")).id,re=ve=>{console.log("Profile updated via WebSocket:",ve),ve.user_id===W&&(ce(),r(200))},fe=ve=>{console.log("User updated via WebSocket:",ve),ve.user_id===W&&(ce(),r(200))};return Ke.onProfileUpdated(re),Ke.onUserUpdated(fe),()=>{Ke.removeEventListener("profile_updated",re),Ke.removeEventListener("user_updated",fe)}},[]),v.jsxs(v.Fragment,{children:[s===200&&v.jsx(Hn,{message:"Successfully Updated!",type:"success"}),s!=200&&s!=null&&v.jsx(Hn,{message:"Unable to update information. Please try again!",type:"danger"}),x===200&&v.jsx(Hn,{message:"Successfully changed profile picture!",type:"success"}),v.jsx(od,{}),v.jsxs("main",{className:"account-details",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"Account Profile"}),v.jsxs("div",{className:"profile-container",children:[v.jsx("h4",{children:l.firstName+" "+l.lastName}),v.jsx("img",{src:l.profile?`http://127.0.0.1:8000${l.profile}`:Ru,alt:"profile-picture"})]})]})}),v.jsxs("section",{children:[v.jsxs("section",{className:"left-panel",children:[v.jsx("img",{src:C||(l.profile?`http://127.0.0.1:8000${l.profile}`:Ru),alt:""}),v.jsx("input",{type:"file",name:"profile",id:"profile",style:{display:"none"},accept:"image/*",onChange:M}),g===null&&v.jsx("label",{htmlFor:"profile",children:"Change Profile Picture"}),g!=null&&v.jsxs("button",{onClick:()=>xe(l.id,g),disabled:E,className:"submit-button",children:[E&&v.jsx(ca,{}),E?"Saving...":"Save Profile Change"]}),v.jsxs("form",{action:H(ne),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First name"}),v.jsx("input",{type:"text",name:"first-name",id:"first-name",defaultValue:l.firstName,...G("firstName")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last name"}),v.jsx("input",{type:"text",name:"last-name",id:"last-name",defaultValue:l.lastName,...G("lastName")})]}),v.jsxs("button",{type:"submit",className:"submit-button",disabled:!o||d,children:[d&&v.jsx(ca,{}),"Save Change"]})]})]}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h2",{children:"Account Overview"}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"text",name:"email",id:"email",value:j(l.email),disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"role",children:"Role:"}),v.jsx("input",{type:"text",name:"role",id:"role",value:l.is_superuser?"Admin":"Operator",disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"date-created",children:"Date Created:"}),v.jsx("input",{type:"text",name:"date-created",id:"date-created",placeholder:"2024-01-01",disabled:!0})]})]})]})]})]})}const Eg="/assets/forgot_password-jtzYLH6S.png";function bw(){const l=$n(),[a,s]=A.useState(!1),[r,o]=A.useState(null),f=Mi().shape({email:en().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=Ui({resolver:Qs(f),mode:"all"}),g=async S=>{s(!0);const x=await On.resetPassword(S.email);o(x),s(null)};return A.useEffect(()=>{r!=null&&setTimeout(()=>{o(null)},5e3)},[r]),console.log("responseStatus:",r),v.jsxs(v.Fragment,{children:[r===204&&v.jsx(Hn,{message:`If that email address is in our database, \r
          we will send you an email to reset your password.`,type:"success"}),r!=204&&r!=null&&v.jsx(Hn,{message:"Unable to sent reset password. Please try again.",type:"danger"}),v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:Eg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Password Reset"}),v.jsx("p",{children:"Enter your email address and we will send you a link to reset your password."}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"email",name:"email",id:"email",placeholder:"Enter email...",...d("email")}),p.email&&v.jsx("span",{children:p.email.message})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(ca,{}),a?"Submitting...":"Sent Reset Link"]}),v.jsx("a",{onClick:()=>l("/login"),children:"Back to Log In"})]})]})]})]})}function Sw(){const l=$n(),{uid:a,token:s}=yb(),[r,o]=A.useState(!1),[f,d]=A.useState([]),[h,p]=A.useState(null),m=Mi().shape({password:en().required("Password is required.").min(12).max(64).matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,64}$/),confirmPassword:en().oneOf([td("password"),null],"Password don't match.").required("Confirm Password is required.")}),{register:g,handleSubmit:S,formState:{errors:x,isValid:T},watch:E}=Ui({resolver:Qs(m),mode:"all"}),D=async w=>{o(!0);const j=await On.resetPasswordConfirm(a,s,w.password);console.log("response response:",j),p(j),o(!1),console.log(w)};A.useEffect(()=>{h!=null&&setTimeout(()=>{p(null)},5e3)},[h]);const C=E("password","");return A.useEffect(()=>{let w=[];C.length<12&&w.push("- At least 12 characters."),C.length>64&&w.push("- Not exceed 64 characters."),/[0-9]/.test(C)||w.push("- At least one number."),/[a-z]/.test(C)||w.push("- At least one lowercase letter."),/[A-Z]/.test(C)||w.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(C)||w.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(C)&&w.push("Must not contain spaces"),d(w)},[C]),v.jsxs(v.Fragment,{children:[h===204&&v.jsx(Hn,{message:"Password changed successfully!",type:"success"}),h!=204&&h!=null&&v.jsx(Hn,{message:"Unable to change password. Please try again.",type:"danger"}),v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:Eg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Set-Up New Password"}),v.jsx("p",{children:"Set a new password for your account."}),v.jsxs("form",{onSubmit:S(D),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"New Password:"}),v.jsx("input",{type:"password",name:"password",id:"password",placeholder:"Enter new password...",...g("password")})]}),f.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),f.map((w,j)=>v.jsx("span",{children:w},j))]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password:"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",placeholder:"Enter confirm password...",...g("confirmPassword")}),x.confirmPassword&&v.jsx("span",{children:x.confirmPassword.message})]}),v.jsxs("button",{type:"submit",disabled:!T||r,className:"submit-button",children:[r&&v.jsx(ca,{}),r?"Submitting...":"Submit"]}),v.jsx("a",{onClick:()=>l("/login"),children:"Back to Log In"})]})]})]})]})}function xw(){return v.jsx(n2,{children:v.jsxs(Nb,{children:[v.jsx(nl,{path:"/",element:v.jsx(y2,{})}),v.jsx(nl,{path:"/login",element:v.jsx(AS,{})}),v.jsx(nl,{path:"/register",element:v.jsx(ow,{})}),v.jsx(nl,{path:"/home",element:v.jsx(uw,{})}),v.jsx(nl,{path:"/account",element:v.jsx(vw,{})}),v.jsx(nl,{path:"/reset-password",element:v.jsx(bw,{})}),v.jsx(nl,{path:"/password/reset/confirm/:uid/:token",element:v.jsx(Sw,{})}),v.jsx(nl,{element:v.jsx(cw,{}),children:v.jsx(nl,{path:"/user-management",element:v.jsx(gw,{})})})]})})}B1.createRoot(document.getElementById("root")).render(v.jsx(A.StrictMode,{children:v.jsx(xw,{})}));
