(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function s(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function r(o){if(o.ep)return;o.ep=!0;const f=s(o);fetch(o.href,f)}})();function qf(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var nf={exports:{}},Ds={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var G0;function O1(){if(G0)return Ds;G0=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(r,o,f){var d=null;if(f!==void 0&&(d=""+f),o.key!==void 0&&(d=""+o.key),"key"in o){f={};for(var h in o)h!=="key"&&(f[h]=o[h])}else f=o;return o=f.ref,{$$typeof:l,type:r,key:d,ref:o!==void 0?o:null,props:f}}return Ds.Fragment=a,Ds.jsx=s,Ds.jsxs=s,Ds}var $0;function C1(){return $0||($0=1,nf.exports=O1()),nf.exports}var v=C1(),lf={exports:{}},Ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X0;function D1(){if(X0)return Ce;X0=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),S=Symbol.iterator;function x(T){return T===null||typeof T!="object"?null:(T=S&&T[S]||T["@@iterator"],typeof T=="function"?T:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_=Object.assign,N={};function O(T,Y,re){this.props=T,this.context=Y,this.refs=N,this.updater=re||R}O.prototype.isReactComponent={},O.prototype.setState=function(T,Y){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,Y,"setState")},O.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function E(){}E.prototype=O.prototype;function D(T,Y,re){this.props=T,this.context=Y,this.refs=N,this.updater=re||R}var U=D.prototype=new E;U.constructor=D,_(U,O.prototype),U.isPureReactComponent=!0;var G=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},te=Object.prototype.hasOwnProperty;function W(T,Y,re,le,ye,De){return re=De.ref,{$$typeof:l,type:T,key:Y,ref:re!==void 0?re:null,props:De}}function K(T,Y){return W(T.type,Y,void 0,void 0,void 0,T.props)}function ne(T){return typeof T=="object"&&T!==null&&T.$$typeof===l}function xe(T){var Y={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(re){return Y[re]})}var oe=/\/+/g;function ae(T,Y){return typeof T=="object"&&T!==null&&T.key!=null?xe(""+T.key):Y.toString(36)}function pe(){}function he(T){switch(T.status){case"fulfilled":return T.value;case"rejected":throw T.reason;default:switch(typeof T.status=="string"?T.then(pe,pe):(T.status="pending",T.then(function(Y){T.status==="pending"&&(T.status="fulfilled",T.value=Y)},function(Y){T.status==="pending"&&(T.status="rejected",T.reason=Y)})),T.status){case"fulfilled":return T.value;case"rejected":throw T.reason}}throw T}function we(T,Y,re,le,ye){var De=typeof T;(De==="undefined"||De==="boolean")&&(T=null);var _e=!1;if(T===null)_e=!0;else switch(De){case"bigint":case"string":case"number":_e=!0;break;case"object":switch(T.$$typeof){case l:case a:_e=!0;break;case g:return _e=T._init,we(_e(T._payload),Y,re,le,ye)}}if(_e)return ye=ye(T),_e=le===""?"."+ae(T,0):le,G(ye)?(re="",_e!=null&&(re=_e.replace(oe,"$&/")+"/"),we(ye,Y,re,"",function(St){return St})):ye!=null&&(ne(ye)&&(ye=K(ye,re+(ye.key==null||T&&T.key===ye.key?"":(""+ye.key).replace(oe,"$&/")+"/")+_e)),Y.push(ye)),1;_e=0;var it=le===""?".":le+":";if(G(T))for(var Re=0;Re<T.length;Re++)le=T[Re],De=it+ae(le,Re),_e+=we(le,Y,re,De,ye);else if(Re=x(T),typeof Re=="function")for(T=Re.call(T),Re=0;!(le=T.next()).done;)le=le.value,De=it+ae(le,Re++),_e+=we(le,Y,re,De,ye);else if(De==="object"){if(typeof T.then=="function")return we(he(T),Y,re,le,ye);throw Y=String(T),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return _e}function L(T,Y,re){if(T==null)return T;var le=[],ye=0;return we(T,le,"","",function(De){return Y.call(re,De,ye++)}),le}function X(T){if(T._status===-1){var Y=T._result;Y=Y(),Y.then(function(re){(T._status===0||T._status===-1)&&(T._status=1,T._result=re)},function(re){(T._status===0||T._status===-1)&&(T._status=2,T._result=re)}),T._status===-1&&(T._status=0,T._result=Y)}if(T._status===1)return T._result.default;throw T._result}var I=typeof reportError=="function"?reportError:function(T){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof T=="object"&&T!==null&&typeof T.message=="string"?String(T.message):String(T),error:T});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",T);return}console.error(T)};function me(){}return Ce.Children={map:L,forEach:function(T,Y,re){L(T,function(){Y.apply(this,arguments)},re)},count:function(T){var Y=0;return L(T,function(){Y++}),Y},toArray:function(T){return L(T,function(Y){return Y})||[]},only:function(T){if(!ne(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},Ce.Component=O,Ce.Fragment=s,Ce.Profiler=o,Ce.PureComponent=D,Ce.StrictMode=r,Ce.Suspense=p,Ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,Ce.__COMPILER_RUNTIME={__proto__:null,c:function(T){return k.H.useMemoCache(T)}},Ce.cache=function(T){return function(){return T.apply(null,arguments)}},Ce.cloneElement=function(T,Y,re){if(T==null)throw Error("The argument must be a React element, but you passed "+T+".");var le=_({},T.props),ye=T.key,De=void 0;if(Y!=null)for(_e in Y.ref!==void 0&&(De=void 0),Y.key!==void 0&&(ye=""+Y.key),Y)!te.call(Y,_e)||_e==="key"||_e==="__self"||_e==="__source"||_e==="ref"&&Y.ref===void 0||(le[_e]=Y[_e]);var _e=arguments.length-2;if(_e===1)le.children=re;else if(1<_e){for(var it=Array(_e),Re=0;Re<_e;Re++)it[Re]=arguments[Re+2];le.children=it}return W(T.type,ye,void 0,void 0,De,le)},Ce.createContext=function(T){return T={$$typeof:d,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null},T.Provider=T,T.Consumer={$$typeof:f,_context:T},T},Ce.createElement=function(T,Y,re){var le,ye={},De=null;if(Y!=null)for(le in Y.key!==void 0&&(De=""+Y.key),Y)te.call(Y,le)&&le!=="key"&&le!=="__self"&&le!=="__source"&&(ye[le]=Y[le]);var _e=arguments.length-2;if(_e===1)ye.children=re;else if(1<_e){for(var it=Array(_e),Re=0;Re<_e;Re++)it[Re]=arguments[Re+2];ye.children=it}if(T&&T.defaultProps)for(le in _e=T.defaultProps,_e)ye[le]===void 0&&(ye[le]=_e[le]);return W(T,De,void 0,void 0,null,ye)},Ce.createRef=function(){return{current:null}},Ce.forwardRef=function(T){return{$$typeof:h,render:T}},Ce.isValidElement=ne,Ce.lazy=function(T){return{$$typeof:g,_payload:{_status:-1,_result:T},_init:X}},Ce.memo=function(T,Y){return{$$typeof:m,type:T,compare:Y===void 0?null:Y}},Ce.startTransition=function(T){var Y=k.T,re={};k.T=re;try{var le=T(),ye=k.S;ye!==null&&ye(re,le),typeof le=="object"&&le!==null&&typeof le.then=="function"&&le.then(me,I)}catch(De){I(De)}finally{k.T=Y}},Ce.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},Ce.use=function(T){return k.H.use(T)},Ce.useActionState=function(T,Y,re){return k.H.useActionState(T,Y,re)},Ce.useCallback=function(T,Y){return k.H.useCallback(T,Y)},Ce.useContext=function(T){return k.H.useContext(T)},Ce.useDebugValue=function(){},Ce.useDeferredValue=function(T,Y){return k.H.useDeferredValue(T,Y)},Ce.useEffect=function(T,Y,re){var le=k.H;if(typeof re=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return le.useEffect(T,Y)},Ce.useId=function(){return k.H.useId()},Ce.useImperativeHandle=function(T,Y,re){return k.H.useImperativeHandle(T,Y,re)},Ce.useInsertionEffect=function(T,Y){return k.H.useInsertionEffect(T,Y)},Ce.useLayoutEffect=function(T,Y){return k.H.useLayoutEffect(T,Y)},Ce.useMemo=function(T,Y){return k.H.useMemo(T,Y)},Ce.useOptimistic=function(T,Y){return k.H.useOptimistic(T,Y)},Ce.useReducer=function(T,Y,re){return k.H.useReducer(T,Y,re)},Ce.useRef=function(T){return k.H.useRef(T)},Ce.useState=function(T){return k.H.useState(T)},Ce.useSyncExternalStore=function(T,Y,re){return k.H.useSyncExternalStore(T,Y,re)},Ce.useTransition=function(){return k.H.useTransition()},Ce.version="19.1.0",Ce}var Z0;function Vf(){return Z0||(Z0=1,lf.exports=D1()),lf.exports}var w=Vf();const Pe=qf(w);var af={exports:{}},Ns={},sf={exports:{}},rf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q0;function N1(){return Q0||(Q0=1,function(l){function a(L,X){var I=L.length;L.push(X);e:for(;0<I;){var me=I-1>>>1,T=L[me];if(0<o(T,X))L[me]=X,L[I]=T,I=me;else break e}}function s(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var X=L[0],I=L.pop();if(I!==X){L[0]=I;e:for(var me=0,T=L.length,Y=T>>>1;me<Y;){var re=2*(me+1)-1,le=L[re],ye=re+1,De=L[ye];if(0>o(le,I))ye<T&&0>o(De,le)?(L[me]=De,L[ye]=I,me=ye):(L[me]=le,L[re]=I,me=re);else if(ye<T&&0>o(De,I))L[me]=De,L[ye]=I,me=ye;else break e}}return X}function o(L,X){var I=L.sortIndex-X.sortIndex;return I!==0?I:L.id-X.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var p=[],m=[],g=1,S=null,x=3,R=!1,_=!1,N=!1,O=!1,E=typeof setTimeout=="function"?setTimeout:null,D=typeof clearTimeout=="function"?clearTimeout:null,U=typeof setImmediate<"u"?setImmediate:null;function G(L){for(var X=s(m);X!==null;){if(X.callback===null)r(m);else if(X.startTime<=L)r(m),X.sortIndex=X.expirationTime,a(p,X);else break;X=s(m)}}function k(L){if(N=!1,G(L),!_)if(s(p)!==null)_=!0,te||(te=!0,ae());else{var X=s(m);X!==null&&we(k,X.startTime-L)}}var te=!1,W=-1,K=5,ne=-1;function xe(){return O?!0:!(l.unstable_now()-ne<K)}function oe(){if(O=!1,te){var L=l.unstable_now();ne=L;var X=!0;try{e:{_=!1,N&&(N=!1,D(W),W=-1),R=!0;var I=x;try{t:{for(G(L),S=s(p);S!==null&&!(S.expirationTime>L&&xe());){var me=S.callback;if(typeof me=="function"){S.callback=null,x=S.priorityLevel;var T=me(S.expirationTime<=L);if(L=l.unstable_now(),typeof T=="function"){S.callback=T,G(L),X=!0;break t}S===s(p)&&r(p),G(L)}else r(p);S=s(p)}if(S!==null)X=!0;else{var Y=s(m);Y!==null&&we(k,Y.startTime-L),X=!1}}break e}finally{S=null,x=I,R=!1}X=void 0}}finally{X?ae():te=!1}}}var ae;if(typeof U=="function")ae=function(){U(oe)};else if(typeof MessageChannel<"u"){var pe=new MessageChannel,he=pe.port2;pe.port1.onmessage=oe,ae=function(){he.postMessage(null)}}else ae=function(){E(oe,0)};function we(L,X){W=E(function(){L(l.unstable_now())},X)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(L){L.callback=null},l.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<L?Math.floor(1e3/L):5},l.unstable_getCurrentPriorityLevel=function(){return x},l.unstable_next=function(L){switch(x){case 1:case 2:case 3:var X=3;break;default:X=x}var I=x;x=X;try{return L()}finally{x=I}},l.unstable_requestPaint=function(){O=!0},l.unstable_runWithPriority=function(L,X){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var I=x;x=L;try{return X()}finally{x=I}},l.unstable_scheduleCallback=function(L,X,I){var me=l.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?me+I:me):I=me,L){case 1:var T=-1;break;case 2:T=250;break;case 5:T=1073741823;break;case 4:T=1e4;break;default:T=5e3}return T=I+T,L={id:g++,callback:X,priorityLevel:L,startTime:I,expirationTime:T,sortIndex:-1},I>me?(L.sortIndex=I,a(m,L),s(p)===null&&L===s(m)&&(N?(D(W),W=-1):N=!0,we(k,I-me))):(L.sortIndex=T,a(p,L),_||R||(_=!0,te||(te=!0,ae()))),L},l.unstable_shouldYield=xe,l.unstable_wrapCallback=function(L){var X=x;return function(){var I=x;x=X;try{return L.apply(this,arguments)}finally{x=I}}}}(rf)),rf}var K0;function j1(){return K0||(K0=1,sf.exports=N1()),sf.exports}var uf={exports:{}},kt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var J0;function U1(){if(J0)return kt;J0=1;var l=Vf();function a(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(p,m,g){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:S==null?null:""+S,children:p,containerInfo:m,implementation:g}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return kt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,kt.createPortal=function(p,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return f(p,m,null,g)},kt.flushSync=function(p){var m=d.T,g=r.p;try{if(d.T=null,r.p=2,p)return p()}finally{d.T=m,r.p=g,r.d.f()}},kt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,r.d.C(p,m))},kt.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},kt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin),x=typeof m.integrity=="string"?m.integrity:void 0,R=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?r.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:x,fetchPriority:R}):g==="script"&&r.d.X(p,{crossOrigin:S,integrity:x,fetchPriority:R,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},kt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=h(m.as,m.crossOrigin);r.d.M(p,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&r.d.M(p)},kt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin);r.d.L(p,g,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},kt.preloadModule=function(p,m){if(typeof p=="string")if(m){var g=h(m.as,m.crossOrigin);r.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else r.d.m(p)},kt.requestFormReset=function(p){r.d.r(p)},kt.unstable_batchedUpdates=function(p,m){return p(m)},kt.useFormState=function(p,m,g){return d.H.useFormState(p,m,g)},kt.useFormStatus=function(){return d.H.useHostTransitionStatus()},kt.version="19.1.0",kt}var P0;function M1(){if(P0)return uf.exports;P0=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),uf.exports=U1(),uf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W0;function L1(){if(W0)return Ns;W0=1;var l=j1(),a=Vf(),s=M1();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(r(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(r(188));return t!==e?null:e}for(var n=e,i=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(i=u.return,i!==null){n=i;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return h(u),e;if(c===i)return h(u),t;c=c.sibling}throw Error(r(188))}if(n.return!==i.return)n=u,i=c;else{for(var y=!1,b=u.child;b;){if(b===n){y=!0,n=u,i=c;break}if(b===i){y=!0,i=u,n=c;break}b=b.sibling}if(!y){for(b=c.child;b;){if(b===n){y=!0,n=c,i=u;break}if(b===i){y=!0,i=c,n=u;break}b=b.sibling}if(!y)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var g=Object.assign,S=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),O=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),D=Symbol.for("react.consumer"),U=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),te=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),ne=Symbol.for("react.activity"),xe=Symbol.for("react.memo_cache_sentinel"),oe=Symbol.iterator;function ae(e){return e===null||typeof e!="object"?null:(e=oe&&e[oe]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Symbol.for("react.client.reference");function he(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===pe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _:return"Fragment";case O:return"Profiler";case N:return"StrictMode";case k:return"Suspense";case te:return"SuspenseList";case ne:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case U:return(e.displayName||"Context")+".Provider";case D:return(e._context.displayName||"Context")+".Consumer";case G:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case W:return t=e.displayName||null,t!==null?t:he(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return he(e(t))}catch{}}return null}var we=Array.isArray,L=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},me=[],T=-1;function Y(e){return{current:e}}function re(e){0>T||(e.current=me[T],me[T]=null,T--)}function le(e,t){T++,me[T]=e.current,e.current=t}var ye=Y(null),De=Y(null),_e=Y(null),it=Y(null);function Re(e,t){switch(le(_e,t),le(De,e),le(ye,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?v0(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=v0(t),e=b0(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}re(ye),le(ye,e)}function St(){re(ye),re(De),re(_e)}function _t(e){e.memoizedState!==null&&le(it,e);var t=ye.current,n=b0(t,e.type);t!==n&&(le(De,e),le(ye,n))}function Dt(e){De.current===e&&(re(ye),re(De)),it.current===e&&(re(it),As._currentValue=I)}var pn=Object.prototype.hasOwnProperty,$t=l.unstable_scheduleCallback,Zn=l.unstable_cancelCallback,Nt=l.unstable_shouldYield,dl=l.unstable_requestPaint,ut=l.unstable_now,Dn=l.unstable_getCurrentPriorityLevel,Ge=l.unstable_ImmediatePriority,hl=l.unstable_UserBlockingPriority,ln=l.unstable_NormalPriority,C=l.unstable_LowPriority,H=l.unstable_IdlePriority,$=l.log,ue=l.unstable_setDisableYieldValue,ee=null,P=null;function ie(e){if(typeof $=="function"&&ue(e),P&&typeof P.setStrictMode=="function")try{P.setStrictMode(ee,e)}catch{}}var be=Math.clz32?Math.clz32:Nn,We=Math.log,tt=Math.LN2;function Nn(e){return e>>>=0,e===0?32:31-(We(e)/tt|0)|0}var pt=256,Ve=4194304;function Xt(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function an(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var u=0,c=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var b=i&134217727;return b!==0?(i=b&~c,i!==0?u=Xt(i):(y&=b,y!==0?u=Xt(y):n||(n=b&~e,n!==0&&(u=Xt(n))))):(b=i&~c,b!==0?u=Xt(b):y!==0?u=Xt(y):n||(n=i&~e,n!==0&&(u=Xt(n)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:u}function Zt(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Qn(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ml(){var e=pt;return pt<<=1,(pt&4194048)===0&&(pt=256),e}function pl(){var e=Ve;return Ve<<=1,(Ve&62914560)===0&&(Ve=4194304),e}function Kn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function jn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function qa(e,t,n,i,u,c){var y=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,A=e.expirationTimes,B=e.hiddenUpdates;for(n=y&~n;0<n;){var Z=31-be(n),J=1<<Z;b[Z]=0,A[Z]=-1;var q=B[Z];if(q!==null)for(B[Z]=null,Z=0;Z<q.length;Z++){var V=q[Z];V!==null&&(V.lane&=-536870913)}n&=~J}i!==0&&ot(e,i,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(y&~t))}function ot(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-be(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function qt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-be(n),u=1<<i;u&t|e[i]&t&&(e[i]|=t),n&=~u}}function Se(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function je(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Lt(){var e=X.p;return e!==0?e:(e=window.event,e===void 0?32:B0(e.type))}function yl(e,t){var n=X.p;try{return X.p=e,t()}finally{X.p=n}}var se=Math.random().toString(36).slice(2),ce="__reactFiber$"+se,ge="__reactProps$"+se,Ie="__reactContainer$"+se,yt="__reactEvents$"+se,yn="__reactListeners$"+se,gn="__reactHandles$"+se,At="__reactResources$"+se,Tt="__reactMarker$"+se;function $e(e){delete e[ce],delete e[ge],delete e[yt],delete e[yn],delete e[gn]}function sn(e){var t=e[ce];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ie]||n[ce]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=w0(e);e!==null;){if(n=e[ce])return n;e=w0(e)}return t}e=n,n=e.parentNode}return null}function kl(e){if(e=e[ce]||e[Ie]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ha(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function gl(e){var t=e[At];return t||(t=e[At]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ct(e){e[Tt]=!0}var zi=new Set,ki={};function Jn(e,t){ze(e,t),ze(e+"Capture",t)}function ze(e,t){for(ki[e]=t,e=0;e<t.length;e++)zi.add(t[e])}var Un=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Va={},Bl={};function Qu(e){return pn.call(Bl,e)?!0:pn.call(Va,e)?!1:Un.test(e)?Bl[e]=!0:(Va[e]=!0,!1)}function Hl(e,t,n){if(Qu(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Is(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function vl(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var Ku,fd;function Fa(e){if(Ku===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ku=t&&t[1]||"",fd=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ku+e+fd}var Ju=!1;function Pu(e,t){if(!e||Ju)return"";Ju=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var J=function(){throw Error()};if(Object.defineProperty(J.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(J,[])}catch(V){var q=V}Reflect.construct(e,[],J)}else{try{J.call()}catch(V){q=V}e.call(J.prototype)}}else{try{throw Error()}catch(V){q=V}(J=e())&&typeof J.catch=="function"&&J.catch(function(){})}}catch(V){if(V&&q&&typeof V.stack=="string")return[V.stack,q.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),y=c[0],b=c[1];if(y&&b){var A=y.split(`
`),B=b.split(`
`);for(u=i=0;i<A.length&&!A[i].includes("DetermineComponentFrameRoot");)i++;for(;u<B.length&&!B[u].includes("DetermineComponentFrameRoot");)u++;if(i===A.length||u===B.length)for(i=A.length-1,u=B.length-1;1<=i&&0<=u&&A[i]!==B[u];)u--;for(;1<=i&&0<=u;i--,u--)if(A[i]!==B[u]){if(i!==1||u!==1)do if(i--,u--,0>u||A[i]!==B[u]){var Z=`
`+A[i].replace(" at new "," at ");return e.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",e.displayName)),Z}while(1<=i&&0<=u);break}}}finally{Ju=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Fa(n):""}function Eg(e){switch(e.tag){case 26:case 27:case 5:return Fa(e.type);case 16:return Fa("Lazy");case 13:return Fa("Suspense");case 19:return Fa("SuspenseList");case 0:case 15:return Pu(e.type,!1);case 11:return Pu(e.type.render,!1);case 1:return Pu(e.type,!0);case 31:return Fa("Activity");default:return""}}function dd(e){try{var t="";do t+=Eg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function vn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function hd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wg(e){var t=hd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(y){i=""+y,c.call(this,y)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(y){i=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function er(e){e._valueTracker||(e._valueTracker=wg(e))}function md(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=hd(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function tr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var _g=/[\n"\\]/g;function bn(e){return e.replace(_g,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Wu(e,t,n,i,u,c,y,b){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),t!=null?y==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+vn(t)):e.value!==""+vn(t)&&(e.value=""+vn(t)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),t!=null?Iu(e,y,vn(t)):n!=null?Iu(e,y,vn(n)):i!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+vn(b):e.removeAttribute("name")}function pd(e,t,n,i,u,c,y,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+vn(n):"",t=t!=null?""+vn(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}i=i??u,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=b?e.checked:!!i,e.defaultChecked=!!i,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function Iu(e,t,n){t==="number"&&tr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ya(e,t,n,i){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&i&&(e[n].defaultSelected=!0)}else{for(n=""+vn(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function yd(e,t,n){if(t!=null&&(t=""+vn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+vn(n):""}function gd(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(r(92));if(we(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=vn(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function Ga(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ag=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function vd(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Ag.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function bd(e,t,n){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var u in t)i=t[u],t.hasOwnProperty(u)&&n[u]!==i&&vd(e,u,i)}else for(var c in t)t.hasOwnProperty(c)&&vd(e,c,t[c])}function eo(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function nr(e){return Rg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var to=null;function no(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var $a=null,Xa=null;function Sd(e){var t=kl(e);if(t&&(e=t.stateNode)){var n=e[ge]||null;e:switch(e=t.stateNode,t.type){case"input":if(Wu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+bn(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var u=i[ge]||null;if(!u)throw Error(r(90));Wu(i,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&md(i)}break e;case"textarea":yd(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ya(e,!!n.multiple,t,!1)}}}var lo=!1;function xd(e,t,n){if(lo)return e(t,n);lo=!0;try{var i=e(t);return i}finally{if(lo=!1,($a!==null||Xa!==null)&&(Vr(),$a&&(t=$a,e=Xa,Xa=$a=null,Sd(t),e)))for(t=0;t<e.length;t++)Sd(e[t])}}function Bi(e,t){var n=e.stateNode;if(n===null)return null;var i=n[ge]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(r(231,t,typeof n));return n}var bl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ao=!1;if(bl)try{var Hi={};Object.defineProperty(Hi,"passive",{get:function(){ao=!0}}),window.addEventListener("test",Hi,Hi),window.removeEventListener("test",Hi,Hi)}catch{ao=!1}var ql=null,io=null,lr=null;function Ed(){if(lr)return lr;var e,t=io,n=t.length,i,u="value"in ql?ql.value:ql.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var y=n-e;for(i=1;i<=y&&t[n-i]===u[c-i];i++);return lr=u.slice(e,1<i?1-i:void 0)}function ar(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ir(){return!0}function wd(){return!1}function Qt(e){function t(n,i,u,c,y){this._reactName=n,this._targetInst=u,this.type=i,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ir:wd,this.isPropagationStopped=wd,this}return g(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ir)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ir)},persist:function(){},isPersistent:ir}),t}var ma={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},sr=Qt(ma),qi=g({},ma,{view:0,detail:0}),Og=Qt(qi),so,ro,Vi,rr=g({},qi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:oo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Vi&&(Vi&&e.type==="mousemove"?(so=e.screenX-Vi.screenX,ro=e.screenY-Vi.screenY):ro=so=0,Vi=e),so)},movementY:function(e){return"movementY"in e?e.movementY:ro}}),_d=Qt(rr),Cg=g({},rr,{dataTransfer:0}),Dg=Qt(Cg),Ng=g({},qi,{relatedTarget:0}),uo=Qt(Ng),jg=g({},ma,{animationName:0,elapsedTime:0,pseudoElement:0}),Ug=Qt(jg),Mg=g({},ma,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Lg=Qt(Mg),zg=g({},ma,{data:0}),Ad=Qt(zg),kg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Hg[e])?!!t[e]:!1}function oo(){return qg}var Vg=g({},qi,{key:function(e){if(e.key){var t=kg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ar(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:oo,charCode:function(e){return e.type==="keypress"?ar(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ar(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Fg=Qt(Vg),Yg=g({},rr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Td=Qt(Yg),Gg=g({},qi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:oo}),$g=Qt(Gg),Xg=g({},ma,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zg=Qt(Xg),Qg=g({},rr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Kg=Qt(Qg),Jg=g({},ma,{newState:0,oldState:0}),Pg=Qt(Jg),Wg=[9,13,27,32],co=bl&&"CompositionEvent"in window,Fi=null;bl&&"documentMode"in document&&(Fi=document.documentMode);var Ig=bl&&"TextEvent"in window&&!Fi,Rd=bl&&(!co||Fi&&8<Fi&&11>=Fi),Od=" ",Cd=!1;function Dd(e,t){switch(e){case"keyup":return Wg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Za=!1;function ev(e,t){switch(e){case"compositionend":return Nd(t);case"keypress":return t.which!==32?null:(Cd=!0,Od);case"textInput":return e=t.data,e===Od&&Cd?null:e;default:return null}}function tv(e,t){if(Za)return e==="compositionend"||!co&&Dd(e,t)?(e=Ed(),lr=io=ql=null,Za=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Rd&&t.locale!=="ko"?null:t.data;default:return null}}var nv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function jd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!nv[e.type]:t==="textarea"}function Ud(e,t,n,i){$a?Xa?Xa.push(i):Xa=[i]:$a=i,t=Zr(t,"onChange"),0<t.length&&(n=new sr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Yi=null,Gi=null;function lv(e){h0(e,0)}function ur(e){var t=ha(e);if(md(t))return e}function Md(e,t){if(e==="change")return t}var Ld=!1;if(bl){var fo;if(bl){var ho="oninput"in document;if(!ho){var zd=document.createElement("div");zd.setAttribute("oninput","return;"),ho=typeof zd.oninput=="function"}fo=ho}else fo=!1;Ld=fo&&(!document.documentMode||9<document.documentMode)}function kd(){Yi&&(Yi.detachEvent("onpropertychange",Bd),Gi=Yi=null)}function Bd(e){if(e.propertyName==="value"&&ur(Gi)){var t=[];Ud(t,Gi,e,no(e)),xd(lv,t)}}function av(e,t,n){e==="focusin"?(kd(),Yi=t,Gi=n,Yi.attachEvent("onpropertychange",Bd)):e==="focusout"&&kd()}function iv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ur(Gi)}function sv(e,t){if(e==="click")return ur(t)}function rv(e,t){if(e==="input"||e==="change")return ur(t)}function uv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var rn=typeof Object.is=="function"?Object.is:uv;function $i(e,t){if(rn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var u=n[i];if(!pn.call(t,u)||!rn(e[u],t[u]))return!1}return!0}function Hd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qd(e,t){var n=Hd(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hd(n)}}function Vd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Vd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Fd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=tr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=tr(e.document)}return t}function mo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ov=bl&&"documentMode"in document&&11>=document.documentMode,Qa=null,po=null,Xi=null,yo=!1;function Yd(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;yo||Qa==null||Qa!==tr(i)||(i=Qa,"selectionStart"in i&&mo(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Xi&&$i(Xi,i)||(Xi=i,i=Zr(po,"onSelect"),0<i.length&&(t=new sr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=Qa)))}function pa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ka={animationend:pa("Animation","AnimationEnd"),animationiteration:pa("Animation","AnimationIteration"),animationstart:pa("Animation","AnimationStart"),transitionrun:pa("Transition","TransitionRun"),transitionstart:pa("Transition","TransitionStart"),transitioncancel:pa("Transition","TransitionCancel"),transitionend:pa("Transition","TransitionEnd")},go={},Gd={};bl&&(Gd=document.createElement("div").style,"AnimationEvent"in window||(delete Ka.animationend.animation,delete Ka.animationiteration.animation,delete Ka.animationstart.animation),"TransitionEvent"in window||delete Ka.transitionend.transition);function ya(e){if(go[e])return go[e];if(!Ka[e])return e;var t=Ka[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gd)return go[e]=t[n];return e}var $d=ya("animationend"),Xd=ya("animationiteration"),Zd=ya("animationstart"),cv=ya("transitionrun"),fv=ya("transitionstart"),dv=ya("transitioncancel"),Qd=ya("transitionend"),Kd=new Map,vo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vo.push("scrollEnd");function Mn(e,t){Kd.set(e,t),Jn(t,[e])}var Jd=new WeakMap;function Sn(e,t){if(typeof e=="object"&&e!==null){var n=Jd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:dd(t)},Jd.set(e,t),t)}return{value:e,source:t,stack:dd(t)}}var xn=[],Ja=0,bo=0;function or(){for(var e=Ja,t=bo=Ja=0;t<e;){var n=xn[t];xn[t++]=null;var i=xn[t];xn[t++]=null;var u=xn[t];xn[t++]=null;var c=xn[t];if(xn[t++]=null,i!==null&&u!==null){var y=i.pending;y===null?u.next=u:(u.next=y.next,y.next=u),i.pending=u}c!==0&&Pd(n,u,c)}}function cr(e,t,n,i){xn[Ja++]=e,xn[Ja++]=t,xn[Ja++]=n,xn[Ja++]=i,bo|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function So(e,t,n,i){return cr(e,t,n,i),fr(e)}function Pa(e,t){return cr(e,null,null,t),fr(e)}function Pd(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var u=!1,c=e.return;c!==null;)c.childLanes|=n,i=c.alternate,i!==null&&(i.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-be(n),e=c.hiddenUpdates,i=e[u],i===null?e[u]=[t]:i.push(t),t.lane=n|536870912),c):null}function fr(e){if(50<gs)throw gs=0,Tc=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Wa={};function hv(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function un(e,t,n,i){return new hv(e,t,n,i)}function xo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Sl(e,t){var n=e.alternate;return n===null?(n=un(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Wd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function dr(e,t,n,i,u,c){var y=0;if(i=e,typeof e=="function")xo(e)&&(y=1);else if(typeof e=="string")y=p1(e,n,ye.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ne:return e=un(31,n,t,u),e.elementType=ne,e.lanes=c,e;case _:return ga(n.children,u,c,t);case N:y=8,u|=24;break;case O:return e=un(12,n,t,u|2),e.elementType=O,e.lanes=c,e;case k:return e=un(13,n,t,u),e.elementType=k,e.lanes=c,e;case te:return e=un(19,n,t,u),e.elementType=te,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case E:case U:y=10;break e;case D:y=9;break e;case G:y=11;break e;case W:y=14;break e;case K:y=16,i=null;break e}y=29,n=Error(r(130,e===null?"null":typeof e,"")),i=null}return t=un(y,n,t,u),t.elementType=e,t.type=i,t.lanes=c,t}function ga(e,t,n,i){return e=un(7,e,i,t),e.lanes=n,e}function Eo(e,t,n){return e=un(6,e,null,t),e.lanes=n,e}function wo(e,t,n){return t=un(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ia=[],ei=0,hr=null,mr=0,En=[],wn=0,va=null,xl=1,El="";function ba(e,t){Ia[ei++]=mr,Ia[ei++]=hr,hr=e,mr=t}function Id(e,t,n){En[wn++]=xl,En[wn++]=El,En[wn++]=va,va=e;var i=xl;e=El;var u=32-be(i)-1;i&=~(1<<u),n+=1;var c=32-be(t)+u;if(30<c){var y=u-u%5;c=(i&(1<<y)-1).toString(32),i>>=y,u-=y,xl=1<<32-be(t)+u|n<<u|i,El=c+e}else xl=1<<c|n<<u|i,El=e}function _o(e){e.return!==null&&(ba(e,1),Id(e,1,0))}function Ao(e){for(;e===hr;)hr=Ia[--ei],Ia[ei]=null,mr=Ia[--ei],Ia[ei]=null;for(;e===va;)va=En[--wn],En[wn]=null,El=En[--wn],En[wn]=null,xl=En[--wn],En[wn]=null}var Vt=null,st=null,qe=!1,Sa=null,Pn=!1,To=Error(r(519));function xa(e){var t=Error(r(418,""));throw Ki(Sn(t,e)),To}function eh(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[ce]=e,t[ge]=i,n){case"dialog":Le("cancel",t),Le("close",t);break;case"iframe":case"object":case"embed":Le("load",t);break;case"video":case"audio":for(n=0;n<bs.length;n++)Le(bs[n],t);break;case"source":Le("error",t);break;case"img":case"image":case"link":Le("error",t),Le("load",t);break;case"details":Le("toggle",t);break;case"input":Le("invalid",t),pd(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),er(t);break;case"select":Le("invalid",t);break;case"textarea":Le("invalid",t),gd(t,i.value,i.defaultValue,i.children),er(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||g0(t.textContent,n)?(i.popover!=null&&(Le("beforetoggle",t),Le("toggle",t)),i.onScroll!=null&&Le("scroll",t),i.onScrollEnd!=null&&Le("scrollend",t),i.onClick!=null&&(t.onclick=Qr),t=!0):t=!1,t||xa(e)}function th(e){for(Vt=e.return;Vt;)switch(Vt.tag){case 5:case 13:Pn=!1;return;case 27:case 3:Pn=!0;return;default:Vt=Vt.return}}function Zi(e){if(e!==Vt)return!1;if(!qe)return th(e),qe=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Fc(e.type,e.memoizedProps)),n=!n),n&&st&&xa(e),th(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){st=zn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}st=null}}else t===27?(t=st,na(e.type)?(e=Xc,Xc=null,st=e):st=t):st=Vt?zn(e.stateNode.nextSibling):null;return!0}function Qi(){st=Vt=null,qe=!1}function nh(){var e=Sa;return e!==null&&(Pt===null?Pt=e:Pt.push.apply(Pt,e),Sa=null),e}function Ki(e){Sa===null?Sa=[e]:Sa.push(e)}var Ro=Y(null),Ea=null,wl=null;function Vl(e,t,n){le(Ro,t._currentValue),t._currentValue=n}function _l(e){e._currentValue=Ro.current,re(Ro)}function Oo(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function Co(e,t,n,i){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var y=u.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=u;for(var A=0;A<t.length;A++)if(b.context===t[A]){c.lanes|=n,b=c.alternate,b!==null&&(b.lanes|=n),Oo(c.return,n,e),i||(y=null);break e}c=b.next}}else if(u.tag===18){if(y=u.return,y===null)throw Error(r(341));y.lanes|=n,c=y.alternate,c!==null&&(c.lanes|=n),Oo(y,n,e),y=null}else y=u.child;if(y!==null)y.return=u;else for(y=u;y!==null;){if(y===e){y=null;break}if(u=y.sibling,u!==null){u.return=y.return,y=u;break}y=y.return}u=y}}function Ji(e,t,n,i){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var y=u.alternate;if(y===null)throw Error(r(387));if(y=y.memoizedProps,y!==null){var b=u.type;rn(u.pendingProps.value,y.value)||(e!==null?e.push(b):e=[b])}}else if(u===it.current){if(y=u.alternate,y===null)throw Error(r(387));y.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(As):e=[As])}u=u.return}e!==null&&Co(t,e,n,i),t.flags|=262144}function pr(e){for(e=e.firstContext;e!==null;){if(!rn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function wa(e){Ea=e,wl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function zt(e){return lh(Ea,e)}function yr(e,t){return Ea===null&&wa(e),lh(e,t)}function lh(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},wl===null){if(e===null)throw Error(r(308));wl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else wl=wl.next=t;return n}var mv=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},pv=l.unstable_scheduleCallback,yv=l.unstable_NormalPriority,xt={$$typeof:U,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Do(){return{controller:new mv,data:new Map,refCount:0}}function Pi(e){e.refCount--,e.refCount===0&&pv(yv,function(){e.controller.abort()})}var Wi=null,No=0,ti=0,ni=null;function gv(e,t){if(Wi===null){var n=Wi=[];No=0,ti=Uc(),ni={status:"pending",value:void 0,then:function(i){n.push(i)}}}return No++,t.then(ah,ah),t}function ah(){if(--No===0&&Wi!==null){ni!==null&&(ni.status="fulfilled");var e=Wi;Wi=null,ti=0,ni=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function vv(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(i.status="rejected",i.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),i}var ih=L.S;L.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&gv(e,t),ih!==null&&ih(e,t)};var _a=Y(null);function jo(){var e=_a.current;return e!==null?e:et.pooledCache}function gr(e,t){t===null?le(_a,_a.current):le(_a,t.pool)}function sh(){var e=jo();return e===null?null:{parent:xt._currentValue,pool:e}}var Ii=Error(r(460)),rh=Error(r(474)),vr=Error(r(542)),Uo={then:function(){}};function uh(e){return e=e.status,e==="fulfilled"||e==="rejected"}function br(){}function oh(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(br,br),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,fh(e),e;default:if(typeof t.status=="string")t.then(br,br);else{if(e=et,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=i}},function(i){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,fh(e),e}throw es=t,Ii}}var es=null;function ch(){if(es===null)throw Error(r(459));var e=es;return es=null,e}function fh(e){if(e===Ii||e===vr)throw Error(r(483))}var Fl=!1;function Mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Yl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Gl(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Fe&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,t=fr(e),Pd(e,null,n),t}return cr(e,i,t,n),fr(e)}function ts(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,qt(e,n)}}function zo(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?u=c=y:c=c.next=y,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ko=!1;function ns(){if(ko){var e=ni;if(e!==null)throw e}}function ls(e,t,n,i){ko=!1;var u=e.updateQueue;Fl=!1;var c=u.firstBaseUpdate,y=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var A=b,B=A.next;A.next=null,y===null?c=B:y.next=B,y=A;var Z=e.alternate;Z!==null&&(Z=Z.updateQueue,b=Z.lastBaseUpdate,b!==y&&(b===null?Z.firstBaseUpdate=B:b.next=B,Z.lastBaseUpdate=A))}if(c!==null){var J=u.baseState;y=0,Z=B=A=null,b=c;do{var q=b.lane&-536870913,V=q!==b.lane;if(V?(Be&q)===q:(i&q)===q){q!==0&&q===ti&&(ko=!0),Z!==null&&(Z=Z.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var Te=e,Ee=b;q=t;var Qe=n;switch(Ee.tag){case 1:if(Te=Ee.payload,typeof Te=="function"){J=Te.call(Qe,J,q);break e}J=Te;break e;case 3:Te.flags=Te.flags&-65537|128;case 0:if(Te=Ee.payload,q=typeof Te=="function"?Te.call(Qe,J,q):Te,q==null)break e;J=g({},J,q);break e;case 2:Fl=!0}}q=b.callback,q!==null&&(e.flags|=64,V&&(e.flags|=8192),V=u.callbacks,V===null?u.callbacks=[q]:V.push(q))}else V={lane:q,tag:b.tag,payload:b.payload,callback:b.callback,next:null},Z===null?(B=Z=V,A=J):Z=Z.next=V,y|=q;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;V=b,b=V.next,V.next=null,u.lastBaseUpdate=V,u.shared.pending=null}}while(!0);Z===null&&(A=J),u.baseState=A,u.firstBaseUpdate=B,u.lastBaseUpdate=Z,c===null&&(u.shared.lanes=0),Wl|=y,e.lanes=y,e.memoizedState=J}}function dh(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function hh(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)dh(n[e],t)}var li=Y(null),Sr=Y(0);function mh(e,t){e=Nl,le(Sr,e),le(li,t),Nl=e|t.baseLanes}function Bo(){le(Sr,Nl),le(li,li.current)}function Ho(){Nl=Sr.current,re(li),re(Sr)}var $l=0,Ne=null,Xe=null,gt=null,xr=!1,ai=!1,Aa=!1,Er=0,as=0,ii=null,bv=0;function ft(){throw Error(r(321))}function qo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!rn(e[n],t[n]))return!1;return!0}function Vo(e,t,n,i,u,c){return $l=c,Ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=e===null||e.memoizedState===null?Ph:Wh,Aa=!1,c=n(i,u),Aa=!1,ai&&(c=yh(t,n,i,u)),ph(e),c}function ph(e){L.H=Or;var t=Xe!==null&&Xe.next!==null;if($l=0,gt=Xe=Ne=null,xr=!1,as=0,ii=null,t)throw Error(r(300));e===null||Rt||(e=e.dependencies,e!==null&&pr(e)&&(Rt=!0))}function yh(e,t,n,i){Ne=e;var u=0;do{if(ai&&(ii=null),as=0,ai=!1,25<=u)throw Error(r(301));if(u+=1,gt=Xe=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}L.H=Tv,c=t(n,i)}while(ai);return c}function Sv(){var e=L.H,t=e.useState()[0];return t=typeof t.then=="function"?is(t):t,e=e.useState()[0],(Xe!==null?Xe.memoizedState:null)!==e&&(Ne.flags|=1024),t}function Fo(){var e=Er!==0;return Er=0,e}function Yo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Go(e){if(xr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}xr=!1}$l=0,gt=Xe=Ne=null,ai=!1,as=Er=0,ii=null}function Kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return gt===null?Ne.memoizedState=gt=e:gt=gt.next=e,gt}function vt(){if(Xe===null){var e=Ne.alternate;e=e!==null?e.memoizedState:null}else e=Xe.next;var t=gt===null?Ne.memoizedState:gt.next;if(t!==null)gt=t,Xe=e;else{if(e===null)throw Ne.alternate===null?Error(r(467)):Error(r(310));Xe=e,e={memoizedState:Xe.memoizedState,baseState:Xe.baseState,baseQueue:Xe.baseQueue,queue:Xe.queue,next:null},gt===null?Ne.memoizedState=gt=e:gt=gt.next=e}return gt}function $o(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function is(e){var t=as;return as+=1,ii===null&&(ii=[]),e=oh(ii,e,t),t=Ne,(gt===null?t.memoizedState:gt.next)===null&&(t=t.alternate,L.H=t===null||t.memoizedState===null?Ph:Wh),e}function wr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return is(e);if(e.$$typeof===U)return zt(e)}throw Error(r(438,String(e)))}function Xo(e){var t=null,n=Ne.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=Ne.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=$o(),Ne.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=xe;return t.index++,n}function Al(e,t){return typeof t=="function"?t(e):t}function _r(e){var t=vt();return Zo(t,Xe,e)}function Zo(e,t,n){var i=e.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=n;var u=e.baseQueue,c=i.pending;if(c!==null){if(u!==null){var y=u.next;u.next=c.next,c.next=y}t.baseQueue=u=c,i.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var b=y=null,A=null,B=t,Z=!1;do{var J=B.lane&-536870913;if(J!==B.lane?(Be&J)===J:($l&J)===J){var q=B.revertLane;if(q===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null}),J===ti&&(Z=!0);else if(($l&q)===q){B=B.next,q===ti&&(Z=!0);continue}else J={lane:0,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},A===null?(b=A=J,y=c):A=A.next=J,Ne.lanes|=q,Wl|=q;J=B.action,Aa&&n(c,J),c=B.hasEagerState?B.eagerState:n(c,J)}else q={lane:J,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},A===null?(b=A=q,y=c):A=A.next=q,Ne.lanes|=J,Wl|=J;B=B.next}while(B!==null&&B!==t);if(A===null?y=c:A.next=b,!rn(c,e.memoizedState)&&(Rt=!0,Z&&(n=ni,n!==null)))throw n;e.memoizedState=c,e.baseState=y,e.baseQueue=A,i.lastRenderedState=c}return u===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Qo(e){var t=vt(),n=t.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=e;var i=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var y=u=u.next;do c=e(c,y.action),y=y.next;while(y!==u);rn(c,t.memoizedState)||(Rt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,i]}function gh(e,t,n){var i=Ne,u=vt(),c=qe;if(c){if(n===void 0)throw Error(r(407));n=n()}else n=t();var y=!rn((Xe||u).memoizedState,n);y&&(u.memoizedState=n,Rt=!0),u=u.queue;var b=Sh.bind(null,i,u,e);if(ss(2048,8,b,[e]),u.getSnapshot!==t||y||gt!==null&&gt.memoizedState.tag&1){if(i.flags|=2048,si(9,Ar(),bh.bind(null,i,u,n,t),null),et===null)throw Error(r(349));c||($l&124)!==0||vh(i,t,n)}return n}function vh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ne.updateQueue,t===null?(t=$o(),Ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bh(e,t,n,i){t.value=n,t.getSnapshot=i,xh(t)&&Eh(e)}function Sh(e,t,n){return n(function(){xh(t)&&Eh(e)})}function xh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!rn(e,n)}catch{return!0}}function Eh(e){var t=Pa(e,2);t!==null&&hn(t,e,2)}function Ko(e){var t=Kt();if(typeof e=="function"){var n=e;if(e=n(),Aa){ie(!0);try{n()}finally{ie(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Al,lastRenderedState:e},t}function wh(e,t,n,i){return e.baseState=n,Zo(e,Xe,typeof i=="function"?i:Al)}function xv(e,t,n,i,u){if(Rr(e))throw Error(r(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};L.T!==null?n(!0):c.isTransition=!1,i(c),n=t.pending,n===null?(c.next=t.pending=c,_h(t,c)):(c.next=n.next,t.pending=n.next=c)}}function _h(e,t){var n=t.action,i=t.payload,u=e.state;if(t.isTransition){var c=L.T,y={};L.T=y;try{var b=n(u,i),A=L.S;A!==null&&A(y,b),Ah(e,t,b)}catch(B){Jo(e,t,B)}finally{L.T=c}}else try{c=n(u,i),Ah(e,t,c)}catch(B){Jo(e,t,B)}}function Ah(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Th(e,t,i)},function(i){return Jo(e,t,i)}):Th(e,t,n)}function Th(e,t,n){t.status="fulfilled",t.value=n,Rh(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,_h(e,n)))}function Jo(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,Rh(t),t=t.next;while(t!==i)}e.action=null}function Rh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Oh(e,t){return t}function Ch(e,t){if(qe){var n=et.formState;if(n!==null){e:{var i=Ne;if(qe){if(st){t:{for(var u=st,c=Pn;u.nodeType!==8;){if(!c){u=null;break t}if(u=zn(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){st=zn(u.nextSibling),i=u.data==="F!";break e}}xa(i)}i=!1}i&&(t=n[0])}}return n=Kt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Oh,lastRenderedState:t},n.queue=i,n=Qh.bind(null,Ne,i),i.dispatch=n,i=Ko(!1),c=tc.bind(null,Ne,!1,i.queue),i=Kt(),u={state:t,dispatch:null,action:e,pending:null},i.queue=u,n=xv.bind(null,Ne,u,c,n),u.dispatch=n,i.memoizedState=e,[t,n,!1]}function Dh(e){var t=vt();return Nh(t,Xe,e)}function Nh(e,t,n){if(t=Zo(e,t,Oh)[0],e=_r(Al)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=is(t)}catch(y){throw y===Ii?vr:y}else i=t;t=vt();var u=t.queue,c=u.dispatch;return n!==t.memoizedState&&(Ne.flags|=2048,si(9,Ar(),Ev.bind(null,u,n),null)),[i,c,e]}function Ev(e,t){e.action=t}function jh(e){var t=vt(),n=Xe;if(n!==null)return Nh(t,n,e);vt(),t=t.memoizedState,n=vt();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function si(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=Ne.updateQueue,t===null&&(t=$o(),Ne.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function Ar(){return{destroy:void 0,resource:void 0}}function Uh(){return vt().memoizedState}function Tr(e,t,n,i){var u=Kt();i=i===void 0?null:i,Ne.flags|=e,u.memoizedState=si(1|t,Ar(),n,i)}function ss(e,t,n,i){var u=vt();i=i===void 0?null:i;var c=u.memoizedState.inst;Xe!==null&&i!==null&&qo(i,Xe.memoizedState.deps)?u.memoizedState=si(t,c,n,i):(Ne.flags|=e,u.memoizedState=si(1|t,c,n,i))}function Mh(e,t){Tr(8390656,8,e,t)}function Lh(e,t){ss(2048,8,e,t)}function zh(e,t){return ss(4,2,e,t)}function kh(e,t){return ss(4,4,e,t)}function Bh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Hh(e,t,n){n=n!=null?n.concat([e]):null,ss(4,4,Bh.bind(null,t,e),n)}function Po(){}function qh(e,t){var n=vt();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&qo(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function Vh(e,t){var n=vt();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&qo(t,i[1]))return i[0];if(i=e(),Aa){ie(!0);try{e()}finally{ie(!1)}}return n.memoizedState=[i,t],i}function Wo(e,t,n){return n===void 0||($l&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Gm(),Ne.lanes|=e,Wl|=e,n)}function Fh(e,t,n,i){return rn(n,t)?n:li.current!==null?(e=Wo(e,n,i),rn(e,t)||(Rt=!0),e):($l&42)===0?(Rt=!0,e.memoizedState=n):(e=Gm(),Ne.lanes|=e,Wl|=e,t)}function Yh(e,t,n,i,u){var c=X.p;X.p=c!==0&&8>c?c:8;var y=L.T,b={};L.T=b,tc(e,!1,t,n);try{var A=u(),B=L.S;if(B!==null&&B(b,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var Z=vv(A,i);rs(e,t,Z,dn(e))}else rs(e,t,i,dn(e))}catch(J){rs(e,t,{then:function(){},status:"rejected",reason:J},dn())}finally{X.p=c,L.T=y}}function wv(){}function Io(e,t,n,i){if(e.tag!==5)throw Error(r(476));var u=Gh(e).queue;Yh(e,u,t,I,n===null?wv:function(){return $h(e),n(i)})}function Gh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Al,lastRenderedState:I},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Al,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function $h(e){var t=Gh(e).next.queue;rs(e,t,{},dn())}function ec(){return zt(As)}function Xh(){return vt().memoizedState}function Zh(){return vt().memoizedState}function _v(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=dn();e=Yl(n);var i=Gl(t,e,n);i!==null&&(hn(i,t,n),ts(i,t,n)),t={cache:Do()},e.payload=t;return}t=t.return}}function Av(e,t,n){var i=dn();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Rr(e)?Kh(t,n):(n=So(e,t,n,i),n!==null&&(hn(n,e,i),Jh(n,t,i)))}function Qh(e,t,n){var i=dn();rs(e,t,n,i)}function rs(e,t,n,i){var u={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Rr(e))Kh(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var y=t.lastRenderedState,b=c(y,n);if(u.hasEagerState=!0,u.eagerState=b,rn(b,y))return cr(e,t,u,0),et===null&&or(),!1}catch{}finally{}if(n=So(e,t,u,i),n!==null)return hn(n,e,i),Jh(n,t,i),!0}return!1}function tc(e,t,n,i){if(i={lane:2,revertLane:Uc(),action:i,hasEagerState:!1,eagerState:null,next:null},Rr(e)){if(t)throw Error(r(479))}else t=So(e,n,i,2),t!==null&&hn(t,e,2)}function Rr(e){var t=e.alternate;return e===Ne||t!==null&&t===Ne}function Kh(e,t){ai=xr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Jh(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,qt(e,n)}}var Or={readContext:zt,use:wr,useCallback:ft,useContext:ft,useEffect:ft,useImperativeHandle:ft,useLayoutEffect:ft,useInsertionEffect:ft,useMemo:ft,useReducer:ft,useRef:ft,useState:ft,useDebugValue:ft,useDeferredValue:ft,useTransition:ft,useSyncExternalStore:ft,useId:ft,useHostTransitionStatus:ft,useFormState:ft,useActionState:ft,useOptimistic:ft,useMemoCache:ft,useCacheRefresh:ft},Ph={readContext:zt,use:wr,useCallback:function(e,t){return Kt().memoizedState=[e,t===void 0?null:t],e},useContext:zt,useEffect:Mh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Tr(4194308,4,Bh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Tr(4194308,4,e,t)},useInsertionEffect:function(e,t){Tr(4,2,e,t)},useMemo:function(e,t){var n=Kt();t=t===void 0?null:t;var i=e();if(Aa){ie(!0);try{e()}finally{ie(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=Kt();if(n!==void 0){var u=n(t);if(Aa){ie(!0);try{n(t)}finally{ie(!1)}}}else u=t;return i.memoizedState=i.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},i.queue=e,e=e.dispatch=Av.bind(null,Ne,e),[i.memoizedState,e]},useRef:function(e){var t=Kt();return e={current:e},t.memoizedState=e},useState:function(e){e=Ko(e);var t=e.queue,n=Qh.bind(null,Ne,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Po,useDeferredValue:function(e,t){var n=Kt();return Wo(n,e,t)},useTransition:function(){var e=Ko(!1);return e=Yh.bind(null,Ne,e.queue,!0,!1),Kt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=Ne,u=Kt();if(qe){if(n===void 0)throw Error(r(407));n=n()}else{if(n=t(),et===null)throw Error(r(349));(Be&124)!==0||vh(i,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,Mh(Sh.bind(null,i,c,e),[e]),i.flags|=2048,si(9,Ar(),bh.bind(null,i,c,n,t),null),n},useId:function(){var e=Kt(),t=et.identifierPrefix;if(qe){var n=El,i=xl;n=(i&~(1<<32-be(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=Er++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=bv++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ec,useFormState:Ch,useActionState:Ch,useOptimistic:function(e){var t=Kt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=tc.bind(null,Ne,!0,n),n.dispatch=t,[e,t]},useMemoCache:Xo,useCacheRefresh:function(){return Kt().memoizedState=_v.bind(null,Ne)}},Wh={readContext:zt,use:wr,useCallback:qh,useContext:zt,useEffect:Lh,useImperativeHandle:Hh,useInsertionEffect:zh,useLayoutEffect:kh,useMemo:Vh,useReducer:_r,useRef:Uh,useState:function(){return _r(Al)},useDebugValue:Po,useDeferredValue:function(e,t){var n=vt();return Fh(n,Xe.memoizedState,e,t)},useTransition:function(){var e=_r(Al)[0],t=vt().memoizedState;return[typeof e=="boolean"?e:is(e),t]},useSyncExternalStore:gh,useId:Xh,useHostTransitionStatus:ec,useFormState:Dh,useActionState:Dh,useOptimistic:function(e,t){var n=vt();return wh(n,Xe,e,t)},useMemoCache:Xo,useCacheRefresh:Zh},Tv={readContext:zt,use:wr,useCallback:qh,useContext:zt,useEffect:Lh,useImperativeHandle:Hh,useInsertionEffect:zh,useLayoutEffect:kh,useMemo:Vh,useReducer:Qo,useRef:Uh,useState:function(){return Qo(Al)},useDebugValue:Po,useDeferredValue:function(e,t){var n=vt();return Xe===null?Wo(n,e,t):Fh(n,Xe.memoizedState,e,t)},useTransition:function(){var e=Qo(Al)[0],t=vt().memoizedState;return[typeof e=="boolean"?e:is(e),t]},useSyncExternalStore:gh,useId:Xh,useHostTransitionStatus:ec,useFormState:jh,useActionState:jh,useOptimistic:function(e,t){var n=vt();return Xe!==null?wh(n,Xe,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Xo,useCacheRefresh:Zh},ri=null,us=0;function Cr(e){var t=us;return us+=1,ri===null&&(ri=[]),oh(ri,e,t)}function os(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Dr(e,t){throw t.$$typeof===S?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Ih(e){var t=e._init;return t(e._payload)}function em(e){function t(M,j){if(e){var z=M.deletions;z===null?(M.deletions=[j],M.flags|=16):z.push(j)}}function n(M,j){if(!e)return null;for(;j!==null;)t(M,j),j=j.sibling;return null}function i(M){for(var j=new Map;M!==null;)M.key!==null?j.set(M.key,M):j.set(M.index,M),M=M.sibling;return j}function u(M,j){return M=Sl(M,j),M.index=0,M.sibling=null,M}function c(M,j,z){return M.index=z,e?(z=M.alternate,z!==null?(z=z.index,z<j?(M.flags|=67108866,j):z):(M.flags|=67108866,j)):(M.flags|=1048576,j)}function y(M){return e&&M.alternate===null&&(M.flags|=67108866),M}function b(M,j,z,Q){return j===null||j.tag!==6?(j=Eo(z,M.mode,Q),j.return=M,j):(j=u(j,z),j.return=M,j)}function A(M,j,z,Q){var fe=z.type;return fe===_?Z(M,j,z.props.children,Q,z.key):j!==null&&(j.elementType===fe||typeof fe=="object"&&fe!==null&&fe.$$typeof===K&&Ih(fe)===j.type)?(j=u(j,z.props),os(j,z),j.return=M,j):(j=dr(z.type,z.key,z.props,null,M.mode,Q),os(j,z),j.return=M,j)}function B(M,j,z,Q){return j===null||j.tag!==4||j.stateNode.containerInfo!==z.containerInfo||j.stateNode.implementation!==z.implementation?(j=wo(z,M.mode,Q),j.return=M,j):(j=u(j,z.children||[]),j.return=M,j)}function Z(M,j,z,Q,fe){return j===null||j.tag!==7?(j=ga(z,M.mode,Q,fe),j.return=M,j):(j=u(j,z),j.return=M,j)}function J(M,j,z){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return j=Eo(""+j,M.mode,z),j.return=M,j;if(typeof j=="object"&&j!==null){switch(j.$$typeof){case x:return z=dr(j.type,j.key,j.props,null,M.mode,z),os(z,j),z.return=M,z;case R:return j=wo(j,M.mode,z),j.return=M,j;case K:var Q=j._init;return j=Q(j._payload),J(M,j,z)}if(we(j)||ae(j))return j=ga(j,M.mode,z,null),j.return=M,j;if(typeof j.then=="function")return J(M,Cr(j),z);if(j.$$typeof===U)return J(M,yr(M,j),z);Dr(M,j)}return null}function q(M,j,z,Q){var fe=j!==null?j.key:null;if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return fe!==null?null:b(M,j,""+z,Q);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case x:return z.key===fe?A(M,j,z,Q):null;case R:return z.key===fe?B(M,j,z,Q):null;case K:return fe=z._init,z=fe(z._payload),q(M,j,z,Q)}if(we(z)||ae(z))return fe!==null?null:Z(M,j,z,Q,null);if(typeof z.then=="function")return q(M,j,Cr(z),Q);if(z.$$typeof===U)return q(M,j,yr(M,z),Q);Dr(M,z)}return null}function V(M,j,z,Q,fe){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return M=M.get(z)||null,b(j,M,""+Q,fe);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case x:return M=M.get(Q.key===null?z:Q.key)||null,A(j,M,Q,fe);case R:return M=M.get(Q.key===null?z:Q.key)||null,B(j,M,Q,fe);case K:var Ue=Q._init;return Q=Ue(Q._payload),V(M,j,z,Q,fe)}if(we(Q)||ae(Q))return M=M.get(z)||null,Z(j,M,Q,fe,null);if(typeof Q.then=="function")return V(M,j,z,Cr(Q),fe);if(Q.$$typeof===U)return V(M,j,z,yr(j,Q),fe);Dr(j,Q)}return null}function Te(M,j,z,Q){for(var fe=null,Ue=null,ve=j,Ae=j=0,Ct=null;ve!==null&&Ae<z.length;Ae++){ve.index>Ae?(Ct=ve,ve=null):Ct=ve.sibling;var He=q(M,ve,z[Ae],Q);if(He===null){ve===null&&(ve=Ct);break}e&&ve&&He.alternate===null&&t(M,ve),j=c(He,j,Ae),Ue===null?fe=He:Ue.sibling=He,Ue=He,ve=Ct}if(Ae===z.length)return n(M,ve),qe&&ba(M,Ae),fe;if(ve===null){for(;Ae<z.length;Ae++)ve=J(M,z[Ae],Q),ve!==null&&(j=c(ve,j,Ae),Ue===null?fe=ve:Ue.sibling=ve,Ue=ve);return qe&&ba(M,Ae),fe}for(ve=i(ve);Ae<z.length;Ae++)Ct=V(ve,M,Ae,z[Ae],Q),Ct!==null&&(e&&Ct.alternate!==null&&ve.delete(Ct.key===null?Ae:Ct.key),j=c(Ct,j,Ae),Ue===null?fe=Ct:Ue.sibling=Ct,Ue=Ct);return e&&ve.forEach(function(ra){return t(M,ra)}),qe&&ba(M,Ae),fe}function Ee(M,j,z,Q){if(z==null)throw Error(r(151));for(var fe=null,Ue=null,ve=j,Ae=j=0,Ct=null,He=z.next();ve!==null&&!He.done;Ae++,He=z.next()){ve.index>Ae?(Ct=ve,ve=null):Ct=ve.sibling;var ra=q(M,ve,He.value,Q);if(ra===null){ve===null&&(ve=Ct);break}e&&ve&&ra.alternate===null&&t(M,ve),j=c(ra,j,Ae),Ue===null?fe=ra:Ue.sibling=ra,Ue=ra,ve=Ct}if(He.done)return n(M,ve),qe&&ba(M,Ae),fe;if(ve===null){for(;!He.done;Ae++,He=z.next())He=J(M,He.value,Q),He!==null&&(j=c(He,j,Ae),Ue===null?fe=He:Ue.sibling=He,Ue=He);return qe&&ba(M,Ae),fe}for(ve=i(ve);!He.done;Ae++,He=z.next())He=V(ve,M,Ae,He.value,Q),He!==null&&(e&&He.alternate!==null&&ve.delete(He.key===null?Ae:He.key),j=c(He,j,Ae),Ue===null?fe=He:Ue.sibling=He,Ue=He);return e&&ve.forEach(function(R1){return t(M,R1)}),qe&&ba(M,Ae),fe}function Qe(M,j,z,Q){if(typeof z=="object"&&z!==null&&z.type===_&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case x:e:{for(var fe=z.key;j!==null;){if(j.key===fe){if(fe=z.type,fe===_){if(j.tag===7){n(M,j.sibling),Q=u(j,z.props.children),Q.return=M,M=Q;break e}}else if(j.elementType===fe||typeof fe=="object"&&fe!==null&&fe.$$typeof===K&&Ih(fe)===j.type){n(M,j.sibling),Q=u(j,z.props),os(Q,z),Q.return=M,M=Q;break e}n(M,j);break}else t(M,j);j=j.sibling}z.type===_?(Q=ga(z.props.children,M.mode,Q,z.key),Q.return=M,M=Q):(Q=dr(z.type,z.key,z.props,null,M.mode,Q),os(Q,z),Q.return=M,M=Q)}return y(M);case R:e:{for(fe=z.key;j!==null;){if(j.key===fe)if(j.tag===4&&j.stateNode.containerInfo===z.containerInfo&&j.stateNode.implementation===z.implementation){n(M,j.sibling),Q=u(j,z.children||[]),Q.return=M,M=Q;break e}else{n(M,j);break}else t(M,j);j=j.sibling}Q=wo(z,M.mode,Q),Q.return=M,M=Q}return y(M);case K:return fe=z._init,z=fe(z._payload),Qe(M,j,z,Q)}if(we(z))return Te(M,j,z,Q);if(ae(z)){if(fe=ae(z),typeof fe!="function")throw Error(r(150));return z=fe.call(z),Ee(M,j,z,Q)}if(typeof z.then=="function")return Qe(M,j,Cr(z),Q);if(z.$$typeof===U)return Qe(M,j,yr(M,z),Q);Dr(M,z)}return typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint"?(z=""+z,j!==null&&j.tag===6?(n(M,j.sibling),Q=u(j,z),Q.return=M,M=Q):(n(M,j),Q=Eo(z,M.mode,Q),Q.return=M,M=Q),y(M)):n(M,j)}return function(M,j,z,Q){try{us=0;var fe=Qe(M,j,z,Q);return ri=null,fe}catch(ve){if(ve===Ii||ve===vr)throw ve;var Ue=un(29,ve,null,M.mode);return Ue.lanes=Q,Ue.return=M,Ue}finally{}}}var ui=em(!0),tm=em(!1),_n=Y(null),Wn=null;function Xl(e){var t=e.alternate;le(Et,Et.current&1),le(_n,e),Wn===null&&(t===null||li.current!==null||t.memoizedState!==null)&&(Wn=e)}function nm(e){if(e.tag===22){if(le(Et,Et.current),le(_n,e),Wn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Wn=e)}}else Zl()}function Zl(){le(Et,Et.current),le(_n,_n.current)}function Tl(e){re(_n),Wn===e&&(Wn=null),re(Et)}var Et=Y(0);function Nr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||$c(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function nc(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:g({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var lc={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=dn(),u=Yl(i);u.payload=t,n!=null&&(u.callback=n),t=Gl(e,u,i),t!==null&&(hn(t,e,i),ts(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=dn(),u=Yl(i);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=Gl(e,u,i),t!==null&&(hn(t,e,i),ts(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=dn(),i=Yl(n);i.tag=2,t!=null&&(i.callback=t),t=Gl(e,i,n),t!==null&&(hn(t,e,n),ts(t,e,n))}};function lm(e,t,n,i,u,c,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,y):t.prototype&&t.prototype.isPureReactComponent?!$i(n,i)||!$i(u,c):!0}function am(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&lc.enqueueReplaceState(t,t.state,null)}function Ta(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=g({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var jr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function im(e){jr(e)}function sm(e){console.error(e)}function rm(e){jr(e)}function Ur(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function um(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function ac(e,t,n){return n=Yl(n),n.tag=3,n.payload={element:null},n.callback=function(){Ur(e,t)},n}function om(e){return e=Yl(e),e.tag=3,e}function cm(e,t,n,i){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var c=i.value;e.payload=function(){return u(c)},e.callback=function(){um(t,n,i)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){um(t,n,i),typeof u!="function"&&(Il===null?Il=new Set([this]):Il.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function Rv(e,t,n,i,u){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&Ji(t,n,u,!0),n=_n.current,n!==null){switch(n.tag){case 13:return Wn===null?Oc():n.alternate===null&&rt===0&&(rt=3),n.flags&=-257,n.flags|=65536,n.lanes=u,i===Uo?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),Dc(e,i,u)),!1;case 22:return n.flags|=65536,i===Uo?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),Dc(e,i,u)),!1}throw Error(r(435,n.tag))}return Dc(e,i,u),Oc(),!1}if(qe)return t=_n.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,i!==To&&(e=Error(r(422),{cause:i}),Ki(Sn(e,n)))):(i!==To&&(t=Error(r(423),{cause:i}),Ki(Sn(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,i=Sn(i,n),u=ac(e.stateNode,i,u),zo(e,u),rt!==4&&(rt=2)),!1;var c=Error(r(520),{cause:i});if(c=Sn(c,n),ys===null?ys=[c]:ys.push(c),rt!==4&&(rt=2),t===null)return!0;i=Sn(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=ac(n.stateNode,i,e),zo(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Il===null||!Il.has(c))))return n.flags|=65536,u&=-u,n.lanes|=u,u=om(u),cm(u,e,n,i),zo(n,u),!1}n=n.return}while(n!==null);return!1}var fm=Error(r(461)),Rt=!1;function jt(e,t,n,i){t.child=e===null?tm(t,null,n,i):ui(t,e.child,n,i)}function dm(e,t,n,i,u){n=n.render;var c=t.ref;if("ref"in i){var y={};for(var b in i)b!=="ref"&&(y[b]=i[b])}else y=i;return wa(t),i=Vo(e,t,n,y,c,u),b=Fo(),e!==null&&!Rt?(Yo(e,t,u),Rl(e,t,u)):(qe&&b&&_o(t),t.flags|=1,jt(e,t,i,u),t.child)}function hm(e,t,n,i,u){if(e===null){var c=n.type;return typeof c=="function"&&!xo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,mm(e,t,c,i,u)):(e=dr(n.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!dc(e,u)){var y=c.memoizedProps;if(n=n.compare,n=n!==null?n:$i,n(y,i)&&e.ref===t.ref)return Rl(e,t,u)}return t.flags|=1,e=Sl(c,i),e.ref=t.ref,e.return=t,t.child=e}function mm(e,t,n,i,u){if(e!==null){var c=e.memoizedProps;if($i(c,i)&&e.ref===t.ref)if(Rt=!1,t.pendingProps=i=c,dc(e,u))(e.flags&131072)!==0&&(Rt=!0);else return t.lanes=e.lanes,Rl(e,t,u)}return ic(e,t,n,i,u)}function pm(e,t,n){var i=t.pendingProps,u=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|n:n,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return ym(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&gr(t,c!==null?c.cachePool:null),c!==null?mh(t,c):Bo(),nm(t);else return t.lanes=t.childLanes=536870912,ym(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(gr(t,c.cachePool),mh(t,c),Zl(),t.memoizedState=null):(e!==null&&gr(t,null),Bo(),Zl());return jt(e,t,u,n),t.child}function ym(e,t,n,i){var u=jo();return u=u===null?null:{parent:xt._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&gr(t,null),Bo(),nm(t),e!==null&&Ji(e,t,i,!0),null}function Mr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function ic(e,t,n,i,u){return wa(t),n=Vo(e,t,n,i,void 0,u),i=Fo(),e!==null&&!Rt?(Yo(e,t,u),Rl(e,t,u)):(qe&&i&&_o(t),t.flags|=1,jt(e,t,n,u),t.child)}function gm(e,t,n,i,u,c){return wa(t),t.updateQueue=null,n=yh(t,i,n,u),ph(e),i=Fo(),e!==null&&!Rt?(Yo(e,t,c),Rl(e,t,c)):(qe&&i&&_o(t),t.flags|=1,jt(e,t,n,c),t.child)}function vm(e,t,n,i,u){if(wa(t),t.stateNode===null){var c=Wa,y=n.contextType;typeof y=="object"&&y!==null&&(c=zt(y)),c=new n(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=lc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},Mo(t),y=n.contextType,c.context=typeof y=="object"&&y!==null?zt(y):Wa,c.state=t.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(nc(t,n,y,i),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&lc.enqueueReplaceState(c,c.state,null),ls(t,i,c,u),ns(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,A=Ta(n,b);c.props=A;var B=c.context,Z=n.contextType;y=Wa,typeof Z=="object"&&Z!==null&&(y=zt(Z));var J=n.getDerivedStateFromProps;Z=typeof J=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,Z||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||B!==y)&&am(t,c,i,y),Fl=!1;var q=t.memoizedState;c.state=q,ls(t,i,c,u),ns(),B=t.memoizedState,b||q!==B||Fl?(typeof J=="function"&&(nc(t,n,J,i),B=t.memoizedState),(A=Fl||lm(t,n,A,i,q,B,y))?(Z||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=B),c.props=i,c.state=B,c.context=y,i=A):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,Lo(e,t),y=t.memoizedProps,Z=Ta(n,y),c.props=Z,J=t.pendingProps,q=c.context,B=n.contextType,A=Wa,typeof B=="object"&&B!==null&&(A=zt(B)),b=n.getDerivedStateFromProps,(B=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==J||q!==A)&&am(t,c,i,A),Fl=!1,q=t.memoizedState,c.state=q,ls(t,i,c,u),ns();var V=t.memoizedState;y!==J||q!==V||Fl||e!==null&&e.dependencies!==null&&pr(e.dependencies)?(typeof b=="function"&&(nc(t,n,b,i),V=t.memoizedState),(Z=Fl||lm(t,n,Z,i,q,V,A)||e!==null&&e.dependencies!==null&&pr(e.dependencies))?(B||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,V,A),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,V,A)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&q===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&q===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=V),c.props=i,c.state=V,c.context=A,i=Z):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&q===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&q===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,Mr(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=ui(t,e.child,null,u),t.child=ui(t,null,n,u)):jt(e,t,n,u),t.memoizedState=c.state,e=t.child):e=Rl(e,t,u),e}function bm(e,t,n,i){return Qi(),t.flags|=256,jt(e,t,n,i),t.child}var sc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function rc(e){return{baseLanes:e,cachePool:sh()}}function uc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=An),e}function Sm(e,t,n){var i=t.pendingProps,u=!1,c=(t.flags&128)!==0,y;if((y=c)||(y=e!==null&&e.memoizedState===null?!1:(Et.current&2)!==0),y&&(u=!0,t.flags&=-129),y=(t.flags&32)!==0,t.flags&=-33,e===null){if(qe){if(u?Xl(t):Zl(),qe){var b=st,A;if(A=b){e:{for(A=b,b=Pn;A.nodeType!==8;){if(!b){b=null;break e}if(A=zn(A.nextSibling),A===null){b=null;break e}}b=A}b!==null?(t.memoizedState={dehydrated:b,treeContext:va!==null?{id:xl,overflow:El}:null,retryLane:536870912,hydrationErrors:null},A=un(18,null,null,0),A.stateNode=b,A.return=t,t.child=A,Vt=t,st=null,A=!0):A=!1}A||xa(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return $c(b)?t.lanes=32:t.lanes=536870912,null;Tl(t)}return b=i.children,i=i.fallback,u?(Zl(),u=t.mode,b=Lr({mode:"hidden",children:b},u),i=ga(i,u,n,null),b.return=t,i.return=t,b.sibling=i,t.child=b,u=t.child,u.memoizedState=rc(n),u.childLanes=uc(e,y,n),t.memoizedState=sc,i):(Xl(t),oc(t,b))}if(A=e.memoizedState,A!==null&&(b=A.dehydrated,b!==null)){if(c)t.flags&256?(Xl(t),t.flags&=-257,t=cc(e,t,n)):t.memoizedState!==null?(Zl(),t.child=e.child,t.flags|=128,t=null):(Zl(),u=i.fallback,b=t.mode,i=Lr({mode:"visible",children:i.children},b),u=ga(u,b,n,null),u.flags|=2,i.return=t,u.return=t,i.sibling=u,t.child=i,ui(t,e.child,null,n),i=t.child,i.memoizedState=rc(n),i.childLanes=uc(e,y,n),t.memoizedState=sc,t=u);else if(Xl(t),$c(b)){if(y=b.nextSibling&&b.nextSibling.dataset,y)var B=y.dgst;y=B,i=Error(r(419)),i.stack="",i.digest=y,Ki({value:i,source:null,stack:null}),t=cc(e,t,n)}else if(Rt||Ji(e,t,n,!1),y=(n&e.childLanes)!==0,Rt||y){if(y=et,y!==null&&(i=n&-n,i=(i&42)!==0?1:Se(i),i=(i&(y.suspendedLanes|n))!==0?0:i,i!==0&&i!==A.retryLane))throw A.retryLane=i,Pa(e,i),hn(y,e,i),fm;b.data==="$?"||Oc(),t=cc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=A.treeContext,st=zn(b.nextSibling),Vt=t,qe=!0,Sa=null,Pn=!1,e!==null&&(En[wn++]=xl,En[wn++]=El,En[wn++]=va,xl=e.id,El=e.overflow,va=t),t=oc(t,i.children),t.flags|=4096);return t}return u?(Zl(),u=i.fallback,b=t.mode,A=e.child,B=A.sibling,i=Sl(A,{mode:"hidden",children:i.children}),i.subtreeFlags=A.subtreeFlags&65011712,B!==null?u=Sl(B,u):(u=ga(u,b,n,null),u.flags|=2),u.return=t,i.return=t,i.sibling=u,t.child=i,i=u,u=t.child,b=e.child.memoizedState,b===null?b=rc(n):(A=b.cachePool,A!==null?(B=xt._currentValue,A=A.parent!==B?{parent:B,pool:B}:A):A=sh(),b={baseLanes:b.baseLanes|n,cachePool:A}),u.memoizedState=b,u.childLanes=uc(e,y,n),t.memoizedState=sc,i):(Xl(t),n=e.child,e=n.sibling,n=Sl(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(y=t.deletions,y===null?(t.deletions=[e],t.flags|=16):y.push(e)),t.child=n,t.memoizedState=null,n)}function oc(e,t){return t=Lr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Lr(e,t){return e=un(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function cc(e,t,n){return ui(t,e.child,null,n),e=oc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xm(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Oo(e.return,t,n)}function fc(e,t,n,i,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=n,c.tailMode=u)}function Em(e,t,n){var i=t.pendingProps,u=i.revealOrder,c=i.tail;if(jt(e,t,i.children,n),i=Et.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xm(e,n,t);else if(e.tag===19)xm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(le(Et,i),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&Nr(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),fc(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Nr(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}fc(t,!0,n,null,c);break;case"together":fc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Rl(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Wl|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Ji(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,n=Sl(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Sl(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function dc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&pr(e)))}function Ov(e,t,n){switch(t.tag){case 3:Re(t,t.stateNode.containerInfo),Vl(t,xt,e.memoizedState.cache),Qi();break;case 27:case 5:_t(t);break;case 4:Re(t,t.stateNode.containerInfo);break;case 10:Vl(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Xl(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Sm(e,t,n):(Xl(t),e=Rl(e,t,n),e!==null?e.sibling:null);Xl(t);break;case 19:var u=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(Ji(e,t,n,!1),i=(n&t.childLanes)!==0),u){if(i)return Em(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),le(Et,Et.current),i)break;return null;case 22:case 23:return t.lanes=0,pm(e,t,n);case 24:Vl(t,xt,e.memoizedState.cache)}return Rl(e,t,n)}function wm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Rt=!0;else{if(!dc(e,n)&&(t.flags&128)===0)return Rt=!1,Ov(e,t,n);Rt=(e.flags&131072)!==0}else Rt=!1,qe&&(t.flags&1048576)!==0&&Id(t,mr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,u=i._init;if(i=u(i._payload),t.type=i,typeof i=="function")xo(i)?(e=Ta(i,e),t.tag=1,t=vm(null,t,i,e,n)):(t.tag=0,t=ic(null,t,i,e,n));else{if(i!=null){if(u=i.$$typeof,u===G){t.tag=11,t=dm(null,t,i,e,n);break e}else if(u===W){t.tag=14,t=hm(null,t,i,e,n);break e}}throw t=he(i)||i,Error(r(306,t,""))}}return t;case 0:return ic(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,u=Ta(i,t.pendingProps),vm(e,t,i,u,n);case 3:e:{if(Re(t,t.stateNode.containerInfo),e===null)throw Error(r(387));i=t.pendingProps;var c=t.memoizedState;u=c.element,Lo(e,t),ls(t,i,null,n);var y=t.memoizedState;if(i=y.cache,Vl(t,xt,i),i!==c.cache&&Co(t,[xt],n,!0),ns(),i=y.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:y.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=bm(e,t,i,n);break e}else if(i!==u){u=Sn(Error(r(424)),t),Ki(u),t=bm(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(st=zn(e.firstChild),Vt=t,qe=!0,Sa=null,Pn=!0,n=tm(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Qi(),i===u){t=Rl(e,t,n);break e}jt(e,t,i,n)}t=t.child}return t;case 26:return Mr(e,t),e===null?(n=R0(t.type,null,t.pendingProps,null))?t.memoizedState=n:qe||(n=t.type,e=t.pendingProps,i=Kr(_e.current).createElement(n),i[ce]=t,i[ge]=e,Mt(i,n,e),ct(i),t.stateNode=i):t.memoizedState=R0(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return _t(t),e===null&&qe&&(i=t.stateNode=_0(t.type,t.pendingProps,_e.current),Vt=t,Pn=!0,u=st,na(t.type)?(Xc=u,st=zn(i.firstChild)):st=u),jt(e,t,t.pendingProps.children,n),Mr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&qe&&((u=i=st)&&(i=n1(i,t.type,t.pendingProps,Pn),i!==null?(t.stateNode=i,Vt=t,st=zn(i.firstChild),Pn=!1,u=!0):u=!1),u||xa(t)),_t(t),u=t.type,c=t.pendingProps,y=e!==null?e.memoizedProps:null,i=c.children,Fc(u,c)?i=null:y!==null&&Fc(u,y)&&(t.flags|=32),t.memoizedState!==null&&(u=Vo(e,t,Sv,null,null,n),As._currentValue=u),Mr(e,t),jt(e,t,i,n),t.child;case 6:return e===null&&qe&&((e=n=st)&&(n=l1(n,t.pendingProps,Pn),n!==null?(t.stateNode=n,Vt=t,st=null,e=!0):e=!1),e||xa(t)),null;case 13:return Sm(e,t,n);case 4:return Re(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=ui(t,null,i,n):jt(e,t,i,n),t.child;case 11:return dm(e,t,t.type,t.pendingProps,n);case 7:return jt(e,t,t.pendingProps,n),t.child;case 8:return jt(e,t,t.pendingProps.children,n),t.child;case 12:return jt(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,Vl(t,t.type,i.value),jt(e,t,i.children,n),t.child;case 9:return u=t.type._context,i=t.pendingProps.children,wa(t),u=zt(u),i=i(u),t.flags|=1,jt(e,t,i,n),t.child;case 14:return hm(e,t,t.type,t.pendingProps,n);case 15:return mm(e,t,t.type,t.pendingProps,n);case 19:return Em(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=Lr(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Sl(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return pm(e,t,n);case 24:return wa(t),i=zt(xt),e===null?(u=jo(),u===null&&(u=et,c=Do(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=n),u=c),t.memoizedState={parent:i,cache:u},Mo(t),Vl(t,xt,u)):((e.lanes&n)!==0&&(Lo(e,t),ls(t,null,null,n),ns()),u=e.memoizedState,c=t.memoizedState,u.parent!==i?(u={parent:i,cache:i},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Vl(t,xt,i)):(i=c.cache,Vl(t,xt,i),i!==u.cache&&Co(t,[xt],n,!0))),jt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Ol(e){e.flags|=4}function _m(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!j0(t)){if(t=_n.current,t!==null&&((Be&4194048)===Be?Wn!==null:(Be&62914560)!==Be&&(Be&536870912)===0||t!==Wn))throw es=Uo,rh;e.flags|=8192}}function zr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?pl():536870912,e.lanes|=t,di|=t)}function cs(e,t){if(!qe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function at(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags&65011712,i|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function Cv(e,t,n){var i=t.pendingProps;switch(Ao(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return at(t),null;case 1:return at(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),_l(xt),St(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Zi(t)?Ol(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,nh())),at(t),null;case 26:return n=t.memoizedState,e===null?(Ol(t),n!==null?(at(t),_m(t,n)):(at(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ol(t),at(t),_m(t,n)):(at(t),t.flags&=-16777217):(e.memoizedProps!==i&&Ol(t),at(t),t.flags&=-16777217),null;case 27:Dt(t),n=_e.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(!i){if(t.stateNode===null)throw Error(r(166));return at(t),null}e=ye.current,Zi(t)?eh(t):(e=_0(u,i,n),t.stateNode=e,Ol(t))}return at(t),null;case 5:if(Dt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(!i){if(t.stateNode===null)throw Error(r(166));return at(t),null}if(e=ye.current,Zi(t))eh(t);else{switch(u=Kr(_e.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?u.createElement("select",{is:i.is}):u.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?u.createElement(n,{is:i.is}):u.createElement(n)}}e[ce]=t,e[ge]=i;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Mt(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ol(t)}}return at(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&Ol(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(r(166));if(e=_e.current,Zi(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,u=Vt,u!==null)switch(u.tag){case 27:case 5:i=u.memoizedProps}e[ce]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||g0(e.nodeValue,n)),e||xa(t)}else e=Kr(e).createTextNode(i),e[ce]=t,t.stateNode=e}return at(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=Zi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!u)throw Error(r(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[ce]=t}else Qi(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;at(t),u=!1}else u=nh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Tl(t),t):(Tl(t),null)}if(Tl(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,u=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(u=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==u&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),zr(t,t.updateQueue),at(t),null;case 4:return St(),e===null&&kc(t.stateNode.containerInfo),at(t),null;case 10:return _l(t.type),at(t),null;case 19:if(re(Et),u=t.memoizedState,u===null)return at(t),null;if(i=(t.flags&128)!==0,c=u.rendering,c===null)if(i)cs(u,!1);else{if(rt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Nr(e),c!==null){for(t.flags|=128,cs(u,!1),e=c.updateQueue,t.updateQueue=e,zr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Wd(n,e),n=n.sibling;return le(Et,Et.current&1|2),t.child}e=e.sibling}u.tail!==null&&ut()>Hr&&(t.flags|=128,i=!0,cs(u,!1),t.lanes=4194304)}else{if(!i)if(e=Nr(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,zr(t,e),cs(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!qe)return at(t),null}else 2*ut()-u.renderingStartTime>Hr&&n!==536870912&&(t.flags|=128,i=!0,cs(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=ut(),t.sibling=null,e=Et.current,le(Et,i?e&1|2:e&1),t):(at(t),null);case 22:case 23:return Tl(t),Ho(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(at(t),t.subtreeFlags&6&&(t.flags|=8192)):at(t),n=t.updateQueue,n!==null&&zr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&re(_a),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),_l(xt),at(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function Dv(e,t){switch(Ao(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return _l(xt),St(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Dt(t),null;case 13:if(Tl(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));Qi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return re(Et),null;case 4:return St(),null;case 10:return _l(t.type),null;case 22:case 23:return Tl(t),Ho(),e!==null&&re(_a),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return _l(xt),null;case 25:return null;default:return null}}function Am(e,t){switch(Ao(t),t.tag){case 3:_l(xt),St();break;case 26:case 27:case 5:Dt(t);break;case 4:St();break;case 13:Tl(t);break;case 19:re(Et);break;case 10:_l(t.type);break;case 22:case 23:Tl(t),Ho(),e!==null&&re(_a);break;case 24:_l(xt)}}function fs(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var u=i.next;n=u;do{if((n.tag&e)===e){i=void 0;var c=n.create,y=n.inst;i=c(),y.destroy=i}n=n.next}while(n!==u)}}catch(b){Je(t,t.return,b)}}function Ql(e,t,n){try{var i=t.updateQueue,u=i!==null?i.lastEffect:null;if(u!==null){var c=u.next;i=c;do{if((i.tag&e)===e){var y=i.inst,b=y.destroy;if(b!==void 0){y.destroy=void 0,u=t;var A=n,B=b;try{B()}catch(Z){Je(u,A,Z)}}}i=i.next}while(i!==c)}}catch(Z){Je(t,t.return,Z)}}function Tm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{hh(t,n)}catch(i){Je(e,e.return,i)}}}function Rm(e,t,n){n.props=Ta(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){Je(e,t,i)}}function ds(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(u){Je(e,t,u)}}function In(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(u){Je(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){Je(e,t,u)}else n.current=null}function Om(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(u){Je(e,e.return,u)}}function hc(e,t,n){try{var i=e.stateNode;Pv(i,e.type,n,t),i[ge]=t}catch(u){Je(e,e.return,u)}}function Cm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&na(e.type)||e.tag===4}function mc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Cm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&na(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pc(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Qr));else if(i!==4&&(i===27&&na(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(pc(e,t,n),e=e.sibling;e!==null;)pc(e,t,n),e=e.sibling}function kr(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&na(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(kr(e,t,n),e=e.sibling;e!==null;)kr(e,t,n),e=e.sibling}function Dm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Mt(t,i,n),t[ce]=e,t[ge]=n}catch(c){Je(e,e.return,c)}}var Cl=!1,dt=!1,yc=!1,Nm=typeof WeakSet=="function"?WeakSet:Set,Ot=null;function Nv(e,t){if(e=e.containerInfo,qc=tu,e=Fd(e),mo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var u=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var y=0,b=-1,A=-1,B=0,Z=0,J=e,q=null;t:for(;;){for(var V;J!==n||u!==0&&J.nodeType!==3||(b=y+u),J!==c||i!==0&&J.nodeType!==3||(A=y+i),J.nodeType===3&&(y+=J.nodeValue.length),(V=J.firstChild)!==null;)q=J,J=V;for(;;){if(J===e)break t;if(q===n&&++B===u&&(b=y),q===c&&++Z===i&&(A=y),(V=J.nextSibling)!==null)break;J=q,q=J.parentNode}J=V}n=b===-1||A===-1?null:{start:b,end:A}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vc={focusedElem:e,selectionRange:n},tu=!1,Ot=t;Ot!==null;)if(t=Ot,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ot=e;else for(;Ot!==null;){switch(t=Ot,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,u=c.memoizedProps,c=c.memoizedState,i=n.stateNode;try{var Te=Ta(n.type,u,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(Te,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(Ee){Je(n,n.return,Ee)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Gc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Gc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Ot=e;break}Ot=t.return}}function jm(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Kl(e,n),i&4&&fs(5,n);break;case 1:if(Kl(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(y){Je(n,n.return,y)}else{var u=Ta(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Je(n,n.return,y)}}i&64&&Tm(n),i&512&&ds(n,n.return);break;case 3:if(Kl(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{hh(e,t)}catch(y){Je(n,n.return,y)}}break;case 27:t===null&&i&4&&Dm(n);case 26:case 5:Kl(e,n),t===null&&i&4&&Om(n),i&512&&ds(n,n.return);break;case 12:Kl(e,n);break;case 13:Kl(e,n),i&4&&Lm(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=qv.bind(null,n),a1(e,n))));break;case 22:if(i=n.memoizedState!==null||Cl,!i){t=t!==null&&t.memoizedState!==null||dt,u=Cl;var c=dt;Cl=i,(dt=t)&&!c?Jl(e,n,(n.subtreeFlags&8772)!==0):Kl(e,n),Cl=u,dt=c}break;case 30:break;default:Kl(e,n)}}function Um(e){var t=e.alternate;t!==null&&(e.alternate=null,Um(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&$e(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var nt=null,Jt=!1;function Dl(e,t,n){for(n=n.child;n!==null;)Mm(e,t,n),n=n.sibling}function Mm(e,t,n){if(P&&typeof P.onCommitFiberUnmount=="function")try{P.onCommitFiberUnmount(ee,n)}catch{}switch(n.tag){case 26:dt||In(n,t),Dl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:dt||In(n,t);var i=nt,u=Jt;na(n.type)&&(nt=n.stateNode,Jt=!1),Dl(e,t,n),xs(n.stateNode),nt=i,Jt=u;break;case 5:dt||In(n,t);case 6:if(i=nt,u=Jt,nt=null,Dl(e,t,n),nt=i,Jt=u,nt!==null)if(Jt)try{(nt.nodeType===9?nt.body:nt.nodeName==="HTML"?nt.ownerDocument.body:nt).removeChild(n.stateNode)}catch(c){Je(n,t,c)}else try{nt.removeChild(n.stateNode)}catch(c){Je(n,t,c)}break;case 18:nt!==null&&(Jt?(e=nt,E0(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Cs(e)):E0(nt,n.stateNode));break;case 4:i=nt,u=Jt,nt=n.stateNode.containerInfo,Jt=!0,Dl(e,t,n),nt=i,Jt=u;break;case 0:case 11:case 14:case 15:dt||Ql(2,n,t),dt||Ql(4,n,t),Dl(e,t,n);break;case 1:dt||(In(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Rm(n,t,i)),Dl(e,t,n);break;case 21:Dl(e,t,n);break;case 22:dt=(i=dt)||n.memoizedState!==null,Dl(e,t,n),dt=i;break;default:Dl(e,t,n)}}function Lm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Cs(e)}catch(n){Je(t,t.return,n)}}function jv(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Nm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Nm),t;default:throw Error(r(435,e.tag))}}function gc(e,t){var n=jv(e);t.forEach(function(i){var u=Vv.bind(null,e,i);n.has(i)||(n.add(i),i.then(u,u))})}function on(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var u=n[i],c=e,y=t,b=y;e:for(;b!==null;){switch(b.tag){case 27:if(na(b.type)){nt=b.stateNode,Jt=!1;break e}break;case 5:nt=b.stateNode,Jt=!1;break e;case 3:case 4:nt=b.stateNode.containerInfo,Jt=!0;break e}b=b.return}if(nt===null)throw Error(r(160));Mm(c,y,u),nt=null,Jt=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)zm(t,e),t=t.sibling}var Ln=null;function zm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:on(t,e),cn(e),i&4&&(Ql(3,e,e.return),fs(3,e),Ql(5,e,e.return));break;case 1:on(t,e),cn(e),i&512&&(dt||n===null||In(n,n.return)),i&64&&Cl&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var u=Ln;if(on(t,e),cn(e),i&512&&(dt||n===null||In(n,n.return)),i&4){var c=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(i){case"title":c=u.getElementsByTagName("title")[0],(!c||c[Tt]||c[ce]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(i),u.head.insertBefore(c,u.querySelector("head > title"))),Mt(c,i,n),c[ce]=e,ct(c),i=c;break e;case"link":var y=D0("link","href",u).get(i+(n.href||""));if(y){for(var b=0;b<y.length;b++)if(c=y[b],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;case"meta":if(y=D0("meta","content",u).get(i+(n.content||""))){for(b=0;b<y.length;b++)if(c=y[b],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;default:throw Error(r(468,i))}c[ce]=e,ct(c),i=c}e.stateNode=i}else N0(u,e.type,e.stateNode);else e.stateNode=C0(u,i,e.memoizedProps);else c!==i?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,i===null?N0(u,e.type,e.stateNode):C0(u,i,e.memoizedProps)):i===null&&e.stateNode!==null&&hc(e,e.memoizedProps,n.memoizedProps)}break;case 27:on(t,e),cn(e),i&512&&(dt||n===null||In(n,n.return)),n!==null&&i&4&&hc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(on(t,e),cn(e),i&512&&(dt||n===null||In(n,n.return)),e.flags&32){u=e.stateNode;try{Ga(u,"")}catch(V){Je(e,e.return,V)}}i&4&&e.stateNode!=null&&(u=e.memoizedProps,hc(e,u,n!==null?n.memoizedProps:u)),i&1024&&(yc=!0);break;case 6:if(on(t,e),cn(e),i&4){if(e.stateNode===null)throw Error(r(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(V){Je(e,e.return,V)}}break;case 3:if(Wr=null,u=Ln,Ln=Jr(t.containerInfo),on(t,e),Ln=u,cn(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Cs(t.containerInfo)}catch(V){Je(e,e.return,V)}yc&&(yc=!1,km(e));break;case 4:i=Ln,Ln=Jr(e.stateNode.containerInfo),on(t,e),cn(e),Ln=i;break;case 12:on(t,e),cn(e);break;case 13:on(t,e),cn(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(wc=ut()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,gc(e,i)));break;case 22:u=e.memoizedState!==null;var A=n!==null&&n.memoizedState!==null,B=Cl,Z=dt;if(Cl=B||u,dt=Z||A,on(t,e),dt=Z,Cl=B,cn(e),i&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||A||Cl||dt||Ra(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){A=n=t;try{if(c=A.stateNode,u)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{b=A.stateNode;var J=A.memoizedProps.style,q=J!=null&&J.hasOwnProperty("display")?J.display:null;b.style.display=q==null||typeof q=="boolean"?"":(""+q).trim()}}catch(V){Je(A,A.return,V)}}}else if(t.tag===6){if(n===null){A=t;try{A.stateNode.nodeValue=u?"":A.memoizedProps}catch(V){Je(A,A.return,V)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,gc(e,n))));break;case 19:on(t,e),cn(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,gc(e,i)));break;case 30:break;case 21:break;default:on(t,e),cn(e)}}function cn(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(Cm(i)){n=i;break}i=i.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var u=n.stateNode,c=mc(e);kr(e,c,u);break;case 5:var y=n.stateNode;n.flags&32&&(Ga(y,""),n.flags&=-33);var b=mc(e);kr(e,b,y);break;case 3:case 4:var A=n.stateNode.containerInfo,B=mc(e);pc(e,B,A);break;default:throw Error(r(161))}}catch(Z){Je(e,e.return,Z)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function km(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;km(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Kl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)jm(e,t.alternate,t),t=t.sibling}function Ra(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ql(4,t,t.return),Ra(t);break;case 1:In(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Rm(t,t.return,n),Ra(t);break;case 27:xs(t.stateNode);case 26:case 5:In(t,t.return),Ra(t);break;case 22:t.memoizedState===null&&Ra(t);break;case 30:Ra(t);break;default:Ra(t)}e=e.sibling}}function Jl(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,u=e,c=t,y=c.flags;switch(c.tag){case 0:case 11:case 15:Jl(u,c,n),fs(4,c);break;case 1:if(Jl(u,c,n),i=c,u=i.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(B){Je(i,i.return,B)}if(i=c,u=i.updateQueue,u!==null){var b=i.stateNode;try{var A=u.shared.hiddenCallbacks;if(A!==null)for(u.shared.hiddenCallbacks=null,u=0;u<A.length;u++)dh(A[u],b)}catch(B){Je(i,i.return,B)}}n&&y&64&&Tm(c),ds(c,c.return);break;case 27:Dm(c);case 26:case 5:Jl(u,c,n),n&&i===null&&y&4&&Om(c),ds(c,c.return);break;case 12:Jl(u,c,n);break;case 13:Jl(u,c,n),n&&y&4&&Lm(u,c);break;case 22:c.memoizedState===null&&Jl(u,c,n),ds(c,c.return);break;case 30:break;default:Jl(u,c,n)}t=t.sibling}}function vc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Pi(n))}function bc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Pi(e))}function el(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Bm(e,t,n,i),t=t.sibling}function Bm(e,t,n,i){var u=t.flags;switch(t.tag){case 0:case 11:case 15:el(e,t,n,i),u&2048&&fs(9,t);break;case 1:el(e,t,n,i);break;case 3:el(e,t,n,i),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Pi(e)));break;case 12:if(u&2048){el(e,t,n,i),e=t.stateNode;try{var c=t.memoizedProps,y=c.id,b=c.onPostCommit;typeof b=="function"&&b(y,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(A){Je(t,t.return,A)}}else el(e,t,n,i);break;case 13:el(e,t,n,i);break;case 23:break;case 22:c=t.stateNode,y=t.alternate,t.memoizedState!==null?c._visibility&2?el(e,t,n,i):hs(e,t):c._visibility&2?el(e,t,n,i):(c._visibility|=2,oi(e,t,n,i,(t.subtreeFlags&10256)!==0)),u&2048&&vc(y,t);break;case 24:el(e,t,n,i),u&2048&&bc(t.alternate,t);break;default:el(e,t,n,i)}}function oi(e,t,n,i,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,y=t,b=n,A=i,B=y.flags;switch(y.tag){case 0:case 11:case 15:oi(c,y,b,A,u),fs(8,y);break;case 23:break;case 22:var Z=y.stateNode;y.memoizedState!==null?Z._visibility&2?oi(c,y,b,A,u):hs(c,y):(Z._visibility|=2,oi(c,y,b,A,u)),u&&B&2048&&vc(y.alternate,y);break;case 24:oi(c,y,b,A,u),u&&B&2048&&bc(y.alternate,y);break;default:oi(c,y,b,A,u)}t=t.sibling}}function hs(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,u=i.flags;switch(i.tag){case 22:hs(n,i),u&2048&&vc(i.alternate,i);break;case 24:hs(n,i),u&2048&&bc(i.alternate,i);break;default:hs(n,i)}t=t.sibling}}var ms=8192;function ci(e){if(e.subtreeFlags&ms)for(e=e.child;e!==null;)Hm(e),e=e.sibling}function Hm(e){switch(e.tag){case 26:ci(e),e.flags&ms&&e.memoizedState!==null&&g1(Ln,e.memoizedState,e.memoizedProps);break;case 5:ci(e);break;case 3:case 4:var t=Ln;Ln=Jr(e.stateNode.containerInfo),ci(e),Ln=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=ms,ms=16777216,ci(e),ms=t):ci(e));break;default:ci(e)}}function qm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ps(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Ot=i,Fm(i,e)}qm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Vm(e),e=e.sibling}function Vm(e){switch(e.tag){case 0:case 11:case 15:ps(e),e.flags&2048&&Ql(9,e,e.return);break;case 3:ps(e);break;case 12:ps(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Br(e)):ps(e);break;default:ps(e)}}function Br(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Ot=i,Fm(i,e)}qm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ql(8,t,t.return),Br(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Br(t));break;default:Br(t)}e=e.sibling}}function Fm(e,t){for(;Ot!==null;){var n=Ot;switch(n.tag){case 0:case 11:case 15:Ql(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Pi(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Ot=i;else e:for(n=e;Ot!==null;){i=Ot;var u=i.sibling,c=i.return;if(Um(i),i===n){Ot=null;break e}if(u!==null){u.return=c,Ot=u;break e}Ot=c}}}var Uv={getCacheForType:function(e){var t=zt(xt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Mv=typeof WeakMap=="function"?WeakMap:Map,Fe=0,et=null,Me=null,Be=0,Ye=0,fn=null,Pl=!1,fi=!1,Sc=!1,Nl=0,rt=0,Wl=0,Oa=0,xc=0,An=0,di=0,ys=null,Pt=null,Ec=!1,wc=0,Hr=1/0,qr=null,Il=null,Ut=0,ea=null,hi=null,mi=0,_c=0,Ac=null,Ym=null,gs=0,Tc=null;function dn(){if((Fe&2)!==0&&Be!==0)return Be&-Be;if(L.T!==null){var e=ti;return e!==0?e:Uc()}return Lt()}function Gm(){An===0&&(An=(Be&536870912)===0||qe?ml():536870912);var e=_n.current;return e!==null&&(e.flags|=32),An}function hn(e,t,n){(e===et&&(Ye===2||Ye===9)||e.cancelPendingCommit!==null)&&(pi(e,0),ta(e,Be,An,!1)),jn(e,n),((Fe&2)===0||e!==et)&&(e===et&&((Fe&2)===0&&(Oa|=n),rt===4&&ta(e,Be,An,!1)),tl(e))}function $m(e,t,n){if((Fe&6)!==0)throw Error(r(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Zt(e,t),u=i?kv(e,t):Cc(e,t,!0),c=i;do{if(u===0){fi&&!i&&ta(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Lv(n)){u=Cc(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){t=y;e:{var b=e;u=ys;var A=b.current.memoizedState.isDehydrated;if(A&&(pi(b,y).flags|=256),y=Cc(b,y,!1),y!==2){if(Sc&&!A){b.errorRecoveryDisabledLanes|=c,Oa|=c,u=4;break e}c=Pt,Pt=u,c!==null&&(Pt===null?Pt=c:Pt.push.apply(Pt,c))}u=y}if(c=!1,u!==2)continue}}if(u===1){pi(e,0),ta(e,t,0,!0);break}e:{switch(i=e,c=u,c){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:ta(i,t,An,!Pl);break e;case 2:Pt=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(u=wc+300-ut(),10<u)){if(ta(i,t,An,!Pl),an(i,0,!0)!==0)break e;i.timeoutHandle=S0(Xm.bind(null,i,n,Pt,qr,Ec,t,An,Oa,di,Pl,c,2,-0,0),u);break e}Xm(i,n,Pt,qr,Ec,t,An,Oa,di,Pl,c,0,-0,0)}}break}while(!0);tl(e)}function Xm(e,t,n,i,u,c,y,b,A,B,Z,J,q,V){if(e.timeoutHandle=-1,J=t.subtreeFlags,(J&8192||(J&16785408)===16785408)&&(_s={stylesheets:null,count:0,unsuspend:y1},Hm(t),J=v1(),J!==null)){e.cancelPendingCommit=J(Im.bind(null,e,t,c,n,i,u,y,b,A,Z,1,q,V)),ta(e,c,y,!B);return}Im(e,t,c,n,i,u,y,b,A)}function Lv(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var u=n[i],c=u.getSnapshot;u=u.value;try{if(!rn(c(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ta(e,t,n,i){t&=~xc,t&=~Oa,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var u=t;0<u;){var c=31-be(u),y=1<<c;i[c]=-1,u&=~y}n!==0&&ot(e,n,t)}function Vr(){return(Fe&6)===0?(vs(0),!1):!0}function Rc(){if(Me!==null){if(Ye===0)var e=Me.return;else e=Me,wl=Ea=null,Go(e),ri=null,us=0,e=Me;for(;e!==null;)Am(e.alternate,e),e=e.return;Me=null}}function pi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Iv(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Rc(),et=e,Me=n=Sl(e.current,null),Be=t,Ye=0,fn=null,Pl=!1,fi=Zt(e,t),Sc=!1,di=An=xc=Oa=Wl=rt=0,Pt=ys=null,Ec=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var u=31-be(i),c=1<<u;t|=e[u],i&=~c}return Nl=t,or(),n}function Zm(e,t){Ne=null,L.H=Or,t===Ii||t===vr?(t=ch(),Ye=3):t===rh?(t=ch(),Ye=4):Ye=t===fm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,fn=t,Me===null&&(rt=1,Ur(e,Sn(t,e.current)))}function Qm(){var e=L.H;return L.H=Or,e===null?Or:e}function Km(){var e=L.A;return L.A=Uv,e}function Oc(){rt=4,Pl||(Be&4194048)!==Be&&_n.current!==null||(fi=!0),(Wl&134217727)===0&&(Oa&134217727)===0||et===null||ta(et,Be,An,!1)}function Cc(e,t,n){var i=Fe;Fe|=2;var u=Qm(),c=Km();(et!==e||Be!==t)&&(qr=null,pi(e,t)),t=!1;var y=rt;e:do try{if(Ye!==0&&Me!==null){var b=Me,A=fn;switch(Ye){case 8:Rc(),y=6;break e;case 3:case 2:case 9:case 6:_n.current===null&&(t=!0);var B=Ye;if(Ye=0,fn=null,yi(e,b,A,B),n&&fi){y=0;break e}break;default:B=Ye,Ye=0,fn=null,yi(e,b,A,B)}}zv(),y=rt;break}catch(Z){Zm(e,Z)}while(!0);return t&&e.shellSuspendCounter++,wl=Ea=null,Fe=i,L.H=u,L.A=c,Me===null&&(et=null,Be=0,or()),y}function zv(){for(;Me!==null;)Jm(Me)}function kv(e,t){var n=Fe;Fe|=2;var i=Qm(),u=Km();et!==e||Be!==t?(qr=null,Hr=ut()+500,pi(e,t)):fi=Zt(e,t);e:do try{if(Ye!==0&&Me!==null){t=Me;var c=fn;t:switch(Ye){case 1:Ye=0,fn=null,yi(e,t,c,1);break;case 2:case 9:if(uh(c)){Ye=0,fn=null,Pm(t);break}t=function(){Ye!==2&&Ye!==9||et!==e||(Ye=7),tl(e)},c.then(t,t);break e;case 3:Ye=7;break e;case 4:Ye=5;break e;case 7:uh(c)?(Ye=0,fn=null,Pm(t)):(Ye=0,fn=null,yi(e,t,c,7));break;case 5:var y=null;switch(Me.tag){case 26:y=Me.memoizedState;case 5:case 27:var b=Me;if(!y||j0(y)){Ye=0,fn=null;var A=b.sibling;if(A!==null)Me=A;else{var B=b.return;B!==null?(Me=B,Fr(B)):Me=null}break t}}Ye=0,fn=null,yi(e,t,c,5);break;case 6:Ye=0,fn=null,yi(e,t,c,6);break;case 8:Rc(),rt=6;break e;default:throw Error(r(462))}}Bv();break}catch(Z){Zm(e,Z)}while(!0);return wl=Ea=null,L.H=i,L.A=u,Fe=n,Me!==null?0:(et=null,Be=0,or(),rt)}function Bv(){for(;Me!==null&&!Nt();)Jm(Me)}function Jm(e){var t=wm(e.alternate,e,Nl);e.memoizedProps=e.pendingProps,t===null?Fr(e):Me=t}function Pm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=gm(n,t,t.pendingProps,t.type,void 0,Be);break;case 11:t=gm(n,t,t.pendingProps,t.type.render,t.ref,Be);break;case 5:Go(t);default:Am(n,t),t=Me=Wd(t,Nl),t=wm(n,t,Nl)}e.memoizedProps=e.pendingProps,t===null?Fr(e):Me=t}function yi(e,t,n,i){wl=Ea=null,Go(t),ri=null,us=0;var u=t.return;try{if(Rv(e,u,t,n,Be)){rt=1,Ur(e,Sn(n,e.current)),Me=null;return}}catch(c){if(u!==null)throw Me=u,c;rt=1,Ur(e,Sn(n,e.current)),Me=null;return}t.flags&32768?(qe||i===1?e=!0:fi||(Be&536870912)!==0?e=!1:(Pl=e=!0,(i===2||i===9||i===3||i===6)&&(i=_n.current,i!==null&&i.tag===13&&(i.flags|=16384))),Wm(t,e)):Fr(t)}function Fr(e){var t=e;do{if((t.flags&32768)!==0){Wm(t,Pl);return}e=t.return;var n=Cv(t.alternate,t,Nl);if(n!==null){Me=n;return}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);rt===0&&(rt=5)}function Wm(e,t){do{var n=Dv(e.alternate,e);if(n!==null){n.flags&=32767,Me=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Me=e;return}Me=e=n}while(e!==null);rt=6,Me=null}function Im(e,t,n,i,u,c,y,b,A){e.cancelPendingCommit=null;do Yr();while(Ut!==0);if((Fe&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(c=t.lanes|t.childLanes,c|=bo,qa(e,n,c,y,b,A),e===et&&(Me=et=null,Be=0),hi=t,ea=e,mi=n,_c=c,Ac=u,Ym=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Fv(ln,function(){return a0(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=L.T,L.T=null,u=X.p,X.p=2,y=Fe,Fe|=4;try{Nv(e,t,n)}finally{Fe=y,X.p=u,L.T=i}}Ut=1,e0(),t0(),n0()}}function e0(){if(Ut===1){Ut=0;var e=ea,t=hi,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=L.T,L.T=null;var i=X.p;X.p=2;var u=Fe;Fe|=4;try{zm(t,e);var c=Vc,y=Fd(e.containerInfo),b=c.focusedElem,A=c.selectionRange;if(y!==b&&b&&b.ownerDocument&&Vd(b.ownerDocument.documentElement,b)){if(A!==null&&mo(b)){var B=A.start,Z=A.end;if(Z===void 0&&(Z=B),"selectionStart"in b)b.selectionStart=B,b.selectionEnd=Math.min(Z,b.value.length);else{var J=b.ownerDocument||document,q=J&&J.defaultView||window;if(q.getSelection){var V=q.getSelection(),Te=b.textContent.length,Ee=Math.min(A.start,Te),Qe=A.end===void 0?Ee:Math.min(A.end,Te);!V.extend&&Ee>Qe&&(y=Qe,Qe=Ee,Ee=y);var M=qd(b,Ee),j=qd(b,Qe);if(M&&j&&(V.rangeCount!==1||V.anchorNode!==M.node||V.anchorOffset!==M.offset||V.focusNode!==j.node||V.focusOffset!==j.offset)){var z=J.createRange();z.setStart(M.node,M.offset),V.removeAllRanges(),Ee>Qe?(V.addRange(z),V.extend(j.node,j.offset)):(z.setEnd(j.node,j.offset),V.addRange(z))}}}}for(J=[],V=b;V=V.parentNode;)V.nodeType===1&&J.push({element:V,left:V.scrollLeft,top:V.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<J.length;b++){var Q=J[b];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}tu=!!qc,Vc=qc=null}finally{Fe=u,X.p=i,L.T=n}}e.current=t,Ut=2}}function t0(){if(Ut===2){Ut=0;var e=ea,t=hi,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=L.T,L.T=null;var i=X.p;X.p=2;var u=Fe;Fe|=4;try{jm(e,t.alternate,t)}finally{Fe=u,X.p=i,L.T=n}}Ut=3}}function n0(){if(Ut===4||Ut===3){Ut=0,dl();var e=ea,t=hi,n=mi,i=Ym;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ut=5:(Ut=0,hi=ea=null,l0(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Il=null),je(n),t=t.stateNode,P&&typeof P.onCommitFiberRoot=="function")try{P.onCommitFiberRoot(ee,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=L.T,u=X.p,X.p=2,L.T=null;try{for(var c=e.onRecoverableError,y=0;y<i.length;y++){var b=i[y];c(b.value,{componentStack:b.stack})}}finally{L.T=t,X.p=u}}(mi&3)!==0&&Yr(),tl(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Tc?gs++:(gs=0,Tc=e):gs=0,vs(0)}}function l0(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Pi(t)))}function Yr(e){return e0(),t0(),n0(),a0()}function a0(){if(Ut!==5)return!1;var e=ea,t=_c;_c=0;var n=je(mi),i=L.T,u=X.p;try{X.p=32>n?32:n,L.T=null,n=Ac,Ac=null;var c=ea,y=mi;if(Ut=0,hi=ea=null,mi=0,(Fe&6)!==0)throw Error(r(331));var b=Fe;if(Fe|=4,Vm(c.current),Bm(c,c.current,y,n),Fe=b,vs(0,!1),P&&typeof P.onPostCommitFiberRoot=="function")try{P.onPostCommitFiberRoot(ee,c)}catch{}return!0}finally{X.p=u,L.T=i,l0(e,t)}}function i0(e,t,n){t=Sn(n,t),t=ac(e.stateNode,t,2),e=Gl(e,t,2),e!==null&&(jn(e,2),tl(e))}function Je(e,t,n){if(e.tag===3)i0(e,e,n);else for(;t!==null;){if(t.tag===3){i0(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Il===null||!Il.has(i))){e=Sn(n,e),n=om(2),i=Gl(t,n,2),i!==null&&(cm(n,i,t,e),jn(i,2),tl(i));break}}t=t.return}}function Dc(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new Mv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(n)||(Sc=!0,u.add(n),e=Hv.bind(null,e,t,n),t.then(e,e))}function Hv(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,et===e&&(Be&n)===n&&(rt===4||rt===3&&(Be&62914560)===Be&&300>ut()-wc?(Fe&2)===0&&pi(e,0):xc|=n,di===Be&&(di=0)),tl(e)}function s0(e,t){t===0&&(t=pl()),e=Pa(e,t),e!==null&&(jn(e,t),tl(e))}function qv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),s0(e,n)}function Vv(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(r(314))}i!==null&&i.delete(t),s0(e,n)}function Fv(e,t){return $t(e,t)}var Gr=null,gi=null,Nc=!1,$r=!1,jc=!1,Ca=0;function tl(e){e!==gi&&e.next===null&&(gi===null?Gr=gi=e:gi=gi.next=e),$r=!0,Nc||(Nc=!0,Gv())}function vs(e,t){if(!jc&&$r){jc=!0;do for(var n=!1,i=Gr;i!==null;){if(e!==0){var u=i.pendingLanes;if(u===0)var c=0;else{var y=i.suspendedLanes,b=i.pingedLanes;c=(1<<31-be(42|e)+1)-1,c&=u&~(y&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,c0(i,c))}else c=Be,c=an(i,i===et?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||Zt(i,c)||(n=!0,c0(i,c));i=i.next}while(n);jc=!1}}function Yv(){r0()}function r0(){$r=Nc=!1;var e=0;Ca!==0&&(Wv()&&(e=Ca),Ca=0);for(var t=ut(),n=null,i=Gr;i!==null;){var u=i.next,c=u0(i,t);c===0?(i.next=null,n===null?Gr=u:n.next=u,u===null&&(gi=n)):(n=i,(e!==0||(c&3)!==0)&&($r=!0)),i=u}vs(e)}function u0(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var y=31-be(c),b=1<<y,A=u[y];A===-1?((b&n)===0||(b&i)!==0)&&(u[y]=Qn(b,t)):A<=t&&(e.expiredLanes|=b),c&=~b}if(t=et,n=Be,n=an(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Ye===2||Ye===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Zn(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Zt(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Zn(i),je(n)){case 2:case 8:n=hl;break;case 32:n=ln;break;case 268435456:n=H;break;default:n=ln}return i=o0.bind(null,e),n=$t(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Zn(i),e.callbackPriority=2,e.callbackNode=null,2}function o0(e,t){if(Ut!==0&&Ut!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Yr()&&e.callbackNode!==n)return null;var i=Be;return i=an(e,e===et?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:($m(e,i,t),u0(e,ut()),e.callbackNode!=null&&e.callbackNode===n?o0.bind(null,e):null)}function c0(e,t){if(Yr())return null;$m(e,t,!0)}function Gv(){e1(function(){(Fe&6)!==0?$t(Ge,Yv):r0()})}function Uc(){return Ca===0&&(Ca=ml()),Ca}function f0(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:nr(""+e)}function d0(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function $v(e,t,n,i,u){if(t==="submit"&&n&&n.stateNode===u){var c=f0((u[ge]||null).action),y=i.submitter;y&&(t=(t=y[ge]||null)?f0(t.formAction):y.getAttribute("formAction"),t!==null&&(c=t,y=null));var b=new sr("action","action",null,i,u);e.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ca!==0){var A=y?d0(u,y):new FormData(u);Io(n,{pending:!0,data:A,method:u.method,action:c},null,A)}}else typeof c=="function"&&(b.preventDefault(),A=y?d0(u,y):new FormData(u),Io(n,{pending:!0,data:A,method:u.method,action:c},c,A))},currentTarget:u}]})}}for(var Mc=0;Mc<vo.length;Mc++){var Lc=vo[Mc],Xv=Lc.toLowerCase(),Zv=Lc[0].toUpperCase()+Lc.slice(1);Mn(Xv,"on"+Zv)}Mn($d,"onAnimationEnd"),Mn(Xd,"onAnimationIteration"),Mn(Zd,"onAnimationStart"),Mn("dblclick","onDoubleClick"),Mn("focusin","onFocus"),Mn("focusout","onBlur"),Mn(cv,"onTransitionRun"),Mn(fv,"onTransitionStart"),Mn(dv,"onTransitionCancel"),Mn(Qd,"onTransitionEnd"),ze("onMouseEnter",["mouseout","mouseover"]),ze("onMouseLeave",["mouseout","mouseover"]),ze("onPointerEnter",["pointerout","pointerover"]),ze("onPointerLeave",["pointerout","pointerover"]),Jn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Jn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Jn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Jn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Jn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Jn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var bs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(bs));function h0(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],u=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var y=i.length-1;0<=y;y--){var b=i[y],A=b.instance,B=b.currentTarget;if(b=b.listener,A!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=B;try{c(u)}catch(Z){jr(Z)}u.currentTarget=null,c=A}else for(y=0;y<i.length;y++){if(b=i[y],A=b.instance,B=b.currentTarget,b=b.listener,A!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=B;try{c(u)}catch(Z){jr(Z)}u.currentTarget=null,c=A}}}}function Le(e,t){var n=t[yt];n===void 0&&(n=t[yt]=new Set);var i=e+"__bubble";n.has(i)||(m0(t,e,2,!1),n.add(i))}function zc(e,t,n){var i=0;t&&(i|=4),m0(n,e,i,t)}var Xr="_reactListening"+Math.random().toString(36).slice(2);function kc(e){if(!e[Xr]){e[Xr]=!0,zi.forEach(function(n){n!=="selectionchange"&&(Qv.has(n)||zc(n,!1,e),zc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xr]||(t[Xr]=!0,zc("selectionchange",!1,t))}}function m0(e,t,n,i){switch(B0(t)){case 2:var u=x1;break;case 8:u=E1;break;default:u=Pc}n=u.bind(null,t,n,e),u=void 0,!ao||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Bc(e,t,n,i,u){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var y=i.tag;if(y===3||y===4){var b=i.stateNode.containerInfo;if(b===u)break;if(y===4)for(y=i.return;y!==null;){var A=y.tag;if((A===3||A===4)&&y.stateNode.containerInfo===u)return;y=y.return}for(;b!==null;){if(y=sn(b),y===null)return;if(A=y.tag,A===5||A===6||A===26||A===27){i=c=y;continue e}b=b.parentNode}}i=i.return}xd(function(){var B=c,Z=no(n),J=[];e:{var q=Kd.get(e);if(q!==void 0){var V=sr,Te=e;switch(e){case"keypress":if(ar(n)===0)break e;case"keydown":case"keyup":V=Fg;break;case"focusin":Te="focus",V=uo;break;case"focusout":Te="blur",V=uo;break;case"beforeblur":case"afterblur":V=uo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":V=_d;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":V=Dg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":V=$g;break;case $d:case Xd:case Zd:V=Ug;break;case Qd:V=Zg;break;case"scroll":case"scrollend":V=Og;break;case"wheel":V=Kg;break;case"copy":case"cut":case"paste":V=Lg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":V=Td;break;case"toggle":case"beforetoggle":V=Pg}var Ee=(t&4)!==0,Qe=!Ee&&(e==="scroll"||e==="scrollend"),M=Ee?q!==null?q+"Capture":null:q;Ee=[];for(var j=B,z;j!==null;){var Q=j;if(z=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||z===null||M===null||(Q=Bi(j,M),Q!=null&&Ee.push(Ss(j,Q,z))),Qe)break;j=j.return}0<Ee.length&&(q=new V(q,Te,null,n,Z),J.push({event:q,listeners:Ee}))}}if((t&7)===0){e:{if(q=e==="mouseover"||e==="pointerover",V=e==="mouseout"||e==="pointerout",q&&n!==to&&(Te=n.relatedTarget||n.fromElement)&&(sn(Te)||Te[Ie]))break e;if((V||q)&&(q=Z.window===Z?Z:(q=Z.ownerDocument)?q.defaultView||q.parentWindow:window,V?(Te=n.relatedTarget||n.toElement,V=B,Te=Te?sn(Te):null,Te!==null&&(Qe=f(Te),Ee=Te.tag,Te!==Qe||Ee!==5&&Ee!==27&&Ee!==6)&&(Te=null)):(V=null,Te=B),V!==Te)){if(Ee=_d,Q="onMouseLeave",M="onMouseEnter",j="mouse",(e==="pointerout"||e==="pointerover")&&(Ee=Td,Q="onPointerLeave",M="onPointerEnter",j="pointer"),Qe=V==null?q:ha(V),z=Te==null?q:ha(Te),q=new Ee(Q,j+"leave",V,n,Z),q.target=Qe,q.relatedTarget=z,Q=null,sn(Z)===B&&(Ee=new Ee(M,j+"enter",Te,n,Z),Ee.target=z,Ee.relatedTarget=Qe,Q=Ee),Qe=Q,V&&Te)t:{for(Ee=V,M=Te,j=0,z=Ee;z;z=vi(z))j++;for(z=0,Q=M;Q;Q=vi(Q))z++;for(;0<j-z;)Ee=vi(Ee),j--;for(;0<z-j;)M=vi(M),z--;for(;j--;){if(Ee===M||M!==null&&Ee===M.alternate)break t;Ee=vi(Ee),M=vi(M)}Ee=null}else Ee=null;V!==null&&p0(J,q,V,Ee,!1),Te!==null&&Qe!==null&&p0(J,Qe,Te,Ee,!0)}}e:{if(q=B?ha(B):window,V=q.nodeName&&q.nodeName.toLowerCase(),V==="select"||V==="input"&&q.type==="file")var fe=Md;else if(jd(q))if(Ld)fe=rv;else{fe=iv;var Ue=av}else V=q.nodeName,!V||V.toLowerCase()!=="input"||q.type!=="checkbox"&&q.type!=="radio"?B&&eo(B.elementType)&&(fe=Md):fe=sv;if(fe&&(fe=fe(e,B))){Ud(J,fe,n,Z);break e}Ue&&Ue(e,q,B),e==="focusout"&&B&&q.type==="number"&&B.memoizedProps.value!=null&&Iu(q,"number",q.value)}switch(Ue=B?ha(B):window,e){case"focusin":(jd(Ue)||Ue.contentEditable==="true")&&(Qa=Ue,po=B,Xi=null);break;case"focusout":Xi=po=Qa=null;break;case"mousedown":yo=!0;break;case"contextmenu":case"mouseup":case"dragend":yo=!1,Yd(J,n,Z);break;case"selectionchange":if(ov)break;case"keydown":case"keyup":Yd(J,n,Z)}var ve;if(co)e:{switch(e){case"compositionstart":var Ae="onCompositionStart";break e;case"compositionend":Ae="onCompositionEnd";break e;case"compositionupdate":Ae="onCompositionUpdate";break e}Ae=void 0}else Za?Dd(e,n)&&(Ae="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Ae="onCompositionStart");Ae&&(Rd&&n.locale!=="ko"&&(Za||Ae!=="onCompositionStart"?Ae==="onCompositionEnd"&&Za&&(ve=Ed()):(ql=Z,io="value"in ql?ql.value:ql.textContent,Za=!0)),Ue=Zr(B,Ae),0<Ue.length&&(Ae=new Ad(Ae,e,null,n,Z),J.push({event:Ae,listeners:Ue}),ve?Ae.data=ve:(ve=Nd(n),ve!==null&&(Ae.data=ve)))),(ve=Ig?ev(e,n):tv(e,n))&&(Ae=Zr(B,"onBeforeInput"),0<Ae.length&&(Ue=new Ad("onBeforeInput","beforeinput",null,n,Z),J.push({event:Ue,listeners:Ae}),Ue.data=ve)),$v(J,e,B,n,Z)}h0(J,t)})}function Ss(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Zr(e,t){for(var n=t+"Capture",i=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=Bi(e,n),u!=null&&i.unshift(Ss(e,u,c)),u=Bi(e,t),u!=null&&i.push(Ss(e,u,c))),e.tag===3)return i;e=e.return}return[]}function vi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function p0(e,t,n,i,u){for(var c=t._reactName,y=[];n!==null&&n!==i;){var b=n,A=b.alternate,B=b.stateNode;if(b=b.tag,A!==null&&A===i)break;b!==5&&b!==26&&b!==27||B===null||(A=B,u?(B=Bi(n,c),B!=null&&y.unshift(Ss(n,B,A))):u||(B=Bi(n,c),B!=null&&y.push(Ss(n,B,A)))),n=n.return}y.length!==0&&e.push({event:t,listeners:y})}var Kv=/\r\n?/g,Jv=/\u0000|\uFFFD/g;function y0(e){return(typeof e=="string"?e:""+e).replace(Kv,`
`).replace(Jv,"")}function g0(e,t){return t=y0(t),y0(e)===t}function Qr(){}function Ze(e,t,n,i,u,c){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||Ga(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&Ga(e,""+i);break;case"className":Is(e,"class",i);break;case"tabIndex":Is(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Is(e,n,i);break;case"style":bd(e,i,c);break;case"data":if(t!=="object"){Is(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=nr(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Ze(e,t,"name",u.name,u,null),Ze(e,t,"formEncType",u.formEncType,u,null),Ze(e,t,"formMethod",u.formMethod,u,null),Ze(e,t,"formTarget",u.formTarget,u,null)):(Ze(e,t,"encType",u.encType,u,null),Ze(e,t,"method",u.method,u,null),Ze(e,t,"target",u.target,u,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=nr(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=Qr);break;case"onScroll":i!=null&&Le("scroll",e);break;case"onScrollEnd":i!=null&&Le("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=nr(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":Le("beforetoggle",e),Le("toggle",e),Hl(e,"popover",i);break;case"xlinkActuate":vl(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":vl(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":vl(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":vl(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":vl(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":vl(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":vl(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":vl(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":vl(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Hl(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Tg.get(n)||n,Hl(e,n,i))}}function Hc(e,t,n,i,u,c){switch(n){case"style":bd(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=n}}break;case"children":typeof i=="string"?Ga(e,i):(typeof i=="number"||typeof i=="bigint")&&Ga(e,""+i);break;case"onScroll":i!=null&&Le("scroll",e);break;case"onScrollEnd":i!=null&&Le("scrollend",e);break;case"onClick":i!=null&&(e.onclick=Qr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ki.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),c=e[ge]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof i=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,u);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):Hl(e,n,i)}}}function Mt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Le("error",e),Le("load",e);var i=!1,u=!1,c;for(c in n)if(n.hasOwnProperty(c)){var y=n[c];if(y!=null)switch(c){case"src":i=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Ze(e,t,c,y,n,null)}}u&&Ze(e,t,"srcSet",n.srcSet,n,null),i&&Ze(e,t,"src",n.src,n,null);return;case"input":Le("invalid",e);var b=c=y=u=null,A=null,B=null;for(i in n)if(n.hasOwnProperty(i)){var Z=n[i];if(Z!=null)switch(i){case"name":u=Z;break;case"type":y=Z;break;case"checked":A=Z;break;case"defaultChecked":B=Z;break;case"value":c=Z;break;case"defaultValue":b=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(r(137,t));break;default:Ze(e,t,i,Z,n,null)}}pd(e,c,b,A,B,y,u,!1),er(e);return;case"select":Le("invalid",e),i=y=c=null;for(u in n)if(n.hasOwnProperty(u)&&(b=n[u],b!=null))switch(u){case"value":c=b;break;case"defaultValue":y=b;break;case"multiple":i=b;default:Ze(e,t,u,b,n,null)}t=c,n=y,e.multiple=!!i,t!=null?Ya(e,!!i,t,!1):n!=null&&Ya(e,!!i,n,!0);return;case"textarea":Le("invalid",e),c=u=i=null;for(y in n)if(n.hasOwnProperty(y)&&(b=n[y],b!=null))switch(y){case"value":i=b;break;case"defaultValue":u=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(r(91));break;default:Ze(e,t,y,b,n,null)}gd(e,i,u,c),er(e);return;case"option":for(A in n)if(n.hasOwnProperty(A)&&(i=n[A],i!=null))switch(A){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Ze(e,t,A,i,n,null)}return;case"dialog":Le("beforetoggle",e),Le("toggle",e),Le("cancel",e),Le("close",e);break;case"iframe":case"object":Le("load",e);break;case"video":case"audio":for(i=0;i<bs.length;i++)Le(bs[i],e);break;case"image":Le("error",e),Le("load",e);break;case"details":Le("toggle",e);break;case"embed":case"source":case"link":Le("error",e),Le("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(B in n)if(n.hasOwnProperty(B)&&(i=n[B],i!=null))switch(B){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Ze(e,t,B,i,n,null)}return;default:if(eo(t)){for(Z in n)n.hasOwnProperty(Z)&&(i=n[Z],i!==void 0&&Hc(e,t,Z,i,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(i=n[b],i!=null&&Ze(e,t,b,i,n,null))}function Pv(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,y=null,b=null,A=null,B=null,Z=null;for(V in n){var J=n[V];if(n.hasOwnProperty(V)&&J!=null)switch(V){case"checked":break;case"value":break;case"defaultValue":A=J;default:i.hasOwnProperty(V)||Ze(e,t,V,null,i,J)}}for(var q in i){var V=i[q];if(J=n[q],i.hasOwnProperty(q)&&(V!=null||J!=null))switch(q){case"type":c=V;break;case"name":u=V;break;case"checked":B=V;break;case"defaultChecked":Z=V;break;case"value":y=V;break;case"defaultValue":b=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(r(137,t));break;default:V!==J&&Ze(e,t,q,V,i,J)}}Wu(e,y,b,A,B,Z,c,u);return;case"select":V=y=b=q=null;for(c in n)if(A=n[c],n.hasOwnProperty(c)&&A!=null)switch(c){case"value":break;case"multiple":V=A;default:i.hasOwnProperty(c)||Ze(e,t,c,null,i,A)}for(u in i)if(c=i[u],A=n[u],i.hasOwnProperty(u)&&(c!=null||A!=null))switch(u){case"value":q=c;break;case"defaultValue":b=c;break;case"multiple":y=c;default:c!==A&&Ze(e,t,u,c,i,A)}t=b,n=y,i=V,q!=null?Ya(e,!!n,q,!1):!!i!=!!n&&(t!=null?Ya(e,!!n,t,!0):Ya(e,!!n,n?[]:"",!1));return;case"textarea":V=q=null;for(b in n)if(u=n[b],n.hasOwnProperty(b)&&u!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Ze(e,t,b,null,i,u)}for(y in i)if(u=i[y],c=n[y],i.hasOwnProperty(y)&&(u!=null||c!=null))switch(y){case"value":q=u;break;case"defaultValue":V=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==c&&Ze(e,t,y,u,i,c)}yd(e,q,V);return;case"option":for(var Te in n)if(q=n[Te],n.hasOwnProperty(Te)&&q!=null&&!i.hasOwnProperty(Te))switch(Te){case"selected":e.selected=!1;break;default:Ze(e,t,Te,null,i,q)}for(A in i)if(q=i[A],V=n[A],i.hasOwnProperty(A)&&q!==V&&(q!=null||V!=null))switch(A){case"selected":e.selected=q&&typeof q!="function"&&typeof q!="symbol";break;default:Ze(e,t,A,q,i,V)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Ee in n)q=n[Ee],n.hasOwnProperty(Ee)&&q!=null&&!i.hasOwnProperty(Ee)&&Ze(e,t,Ee,null,i,q);for(B in i)if(q=i[B],V=n[B],i.hasOwnProperty(B)&&q!==V&&(q!=null||V!=null))switch(B){case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(r(137,t));break;default:Ze(e,t,B,q,i,V)}return;default:if(eo(t)){for(var Qe in n)q=n[Qe],n.hasOwnProperty(Qe)&&q!==void 0&&!i.hasOwnProperty(Qe)&&Hc(e,t,Qe,void 0,i,q);for(Z in i)q=i[Z],V=n[Z],!i.hasOwnProperty(Z)||q===V||q===void 0&&V===void 0||Hc(e,t,Z,q,i,V);return}}for(var M in n)q=n[M],n.hasOwnProperty(M)&&q!=null&&!i.hasOwnProperty(M)&&Ze(e,t,M,null,i,q);for(J in i)q=i[J],V=n[J],!i.hasOwnProperty(J)||q===V||q==null&&V==null||Ze(e,t,J,q,i,V)}var qc=null,Vc=null;function Kr(e){return e.nodeType===9?e:e.ownerDocument}function v0(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function b0(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Fc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yc=null;function Wv(){var e=window.event;return e&&e.type==="popstate"?e===Yc?!1:(Yc=e,!0):(Yc=null,!1)}var S0=typeof setTimeout=="function"?setTimeout:void 0,Iv=typeof clearTimeout=="function"?clearTimeout:void 0,x0=typeof Promise=="function"?Promise:void 0,e1=typeof queueMicrotask=="function"?queueMicrotask:typeof x0<"u"?function(e){return x0.resolve(null).then(e).catch(t1)}:S0;function t1(e){setTimeout(function(){throw e})}function na(e){return e==="head"}function E0(e,t){var n=t,i=0,u=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<i&&8>i){n=i;var y=e.ownerDocument;if(n&1&&xs(y.documentElement),n&2&&xs(y.body),n&4)for(n=y.head,xs(n),y=n.firstChild;y;){var b=y.nextSibling,A=y.nodeName;y[Tt]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=b}}if(u===0){e.removeChild(c),Cs(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:i=n.charCodeAt(0)-48;else i=0;n=c}while(n);Cs(t)}function Gc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Gc(n),$e(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function n1(e,t,n,i){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Tt])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=zn(e.nextSibling),e===null)break}return null}function l1(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=zn(e.nextSibling),e===null))return null;return e}function $c(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function a1(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function zn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xc=null;function w0(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function _0(e,t,n){switch(t=Kr(n),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function xs(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);$e(e)}var Tn=new Map,A0=new Set;function Jr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var jl=X.d;X.d={f:i1,r:s1,D:r1,C:u1,L:o1,m:c1,X:d1,S:f1,M:h1};function i1(){var e=jl.f(),t=Vr();return e||t}function s1(e){var t=kl(e);t!==null&&t.tag===5&&t.type==="form"?$h(t):jl.r(e)}var bi=typeof document>"u"?null:document;function T0(e,t,n){var i=bi;if(i&&typeof t=="string"&&t){var u=bn(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),A0.has(u)||(A0.add(u),e={rel:e,crossOrigin:n,href:t},i.querySelector(u)===null&&(t=i.createElement("link"),Mt(t,"link",e),ct(t),i.head.appendChild(t)))}}function r1(e){jl.D(e),T0("dns-prefetch",e,null)}function u1(e,t){jl.C(e,t),T0("preconnect",e,t)}function o1(e,t,n){jl.L(e,t,n);var i=bi;if(i&&e&&t){var u='link[rel="preload"][as="'+bn(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+bn(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+bn(n.imageSizes)+'"]')):u+='[href="'+bn(e)+'"]';var c=u;switch(t){case"style":c=Si(e);break;case"script":c=xi(e)}Tn.has(c)||(e=g({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Tn.set(c,e),i.querySelector(u)!==null||t==="style"&&i.querySelector(Es(c))||t==="script"&&i.querySelector(ws(c))||(t=i.createElement("link"),Mt(t,"link",e),ct(t),i.head.appendChild(t)))}}function c1(e,t){jl.m(e,t);var n=bi;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+bn(i)+'"][href="'+bn(e)+'"]',c=u;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=xi(e)}if(!Tn.has(c)&&(e=g({rel:"modulepreload",href:e},t),Tn.set(c,e),n.querySelector(u)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ws(c)))return}i=n.createElement("link"),Mt(i,"link",e),ct(i),n.head.appendChild(i)}}}function f1(e,t,n){jl.S(e,t,n);var i=bi;if(i&&e){var u=gl(i).hoistableStyles,c=Si(e);t=t||"default";var y=u.get(c);if(!y){var b={loading:0,preload:null};if(y=i.querySelector(Es(c)))b.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Tn.get(c))&&Zc(e,n);var A=y=i.createElement("link");ct(A),Mt(A,"link",e),A._p=new Promise(function(B,Z){A.onload=B,A.onerror=Z}),A.addEventListener("load",function(){b.loading|=1}),A.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Pr(y,t,i)}y={type:"stylesheet",instance:y,count:1,state:b},u.set(c,y)}}}function d1(e,t){jl.X(e,t);var n=bi;if(n&&e){var i=gl(n).hoistableScripts,u=xi(e),c=i.get(u);c||(c=n.querySelector(ws(u)),c||(e=g({src:e,async:!0},t),(t=Tn.get(u))&&Qc(e,t),c=n.createElement("script"),ct(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function h1(e,t){jl.M(e,t);var n=bi;if(n&&e){var i=gl(n).hoistableScripts,u=xi(e),c=i.get(u);c||(c=n.querySelector(ws(u)),c||(e=g({src:e,async:!0,type:"module"},t),(t=Tn.get(u))&&Qc(e,t),c=n.createElement("script"),ct(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function R0(e,t,n,i){var u=(u=_e.current)?Jr(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Si(n.href),n=gl(u).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Si(n.href);var c=gl(u).hoistableStyles,y=c.get(e);if(y||(u=u.ownerDocument||u,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,y),(c=u.querySelector(Es(e)))&&!c._p&&(y.instance=c,y.state.loading=5),Tn.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Tn.set(e,n),c||m1(u,e,n,y.state))),t&&i===null)throw Error(r(528,""));return y}if(t&&i!==null)throw Error(r(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=xi(n),n=gl(u).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Si(e){return'href="'+bn(e)+'"'}function Es(e){return'link[rel="stylesheet"]['+e+"]"}function O0(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function m1(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),Mt(t,"link",n),ct(t),e.head.appendChild(t))}function xi(e){return'[src="'+bn(e)+'"]'}function ws(e){return"script[async]"+e}function C0(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+bn(n.href)+'"]');if(i)return t.instance=i,ct(i),i;var u=g({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),ct(i),Mt(i,"style",u),Pr(i,n.precedence,e),t.instance=i;case"stylesheet":u=Si(n.href);var c=e.querySelector(Es(u));if(c)return t.state.loading|=4,t.instance=c,ct(c),c;i=O0(n),(u=Tn.get(u))&&Zc(i,u),c=(e.ownerDocument||e).createElement("link"),ct(c);var y=c;return y._p=new Promise(function(b,A){y.onload=b,y.onerror=A}),Mt(c,"link",i),t.state.loading|=4,Pr(c,n.precedence,e),t.instance=c;case"script":return c=xi(n.src),(u=e.querySelector(ws(c)))?(t.instance=u,ct(u),u):(i=n,(u=Tn.get(c))&&(i=g({},n),Qc(i,u)),e=e.ownerDocument||e,u=e.createElement("script"),ct(u),Mt(u,"link",i),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,Pr(i,n.precedence,e));return t.instance}function Pr(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=i.length?i[i.length-1]:null,c=u,y=0;y<i.length;y++){var b=i[y];if(b.dataset.precedence===t)c=b;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Zc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Wr=null;function D0(e,t,n){if(Wr===null){var i=new Map,u=Wr=new Map;u.set(n,i)}else u=Wr,i=u.get(n),i||(i=new Map,u.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var c=n[u];if(!(c[Tt]||c[ce]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(t)||"";y=e+y;var b=i.get(y);b?b.push(c):i.set(y,[c])}}return i}function N0(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function p1(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function j0(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var _s=null;function y1(){}function g1(e,t,n){if(_s===null)throw Error(r(475));var i=_s;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Si(n.href),c=e.querySelector(Es(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=Ir.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,ct(c);return}c=e.ownerDocument||e,n=O0(n),(u=Tn.get(u))&&Zc(n,u),c=c.createElement("link"),ct(c);var y=c;y._p=new Promise(function(b,A){y.onload=b,y.onerror=A}),Mt(c,"link",n),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=Ir.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function v1(){if(_s===null)throw Error(r(475));var e=_s;return e.stylesheets&&e.count===0&&Kc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Kc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Ir(){if(this.count--,this.count===0){if(this.stylesheets)Kc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var eu=null;function Kc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,eu=new Map,t.forEach(b1,e),eu=null,Ir.call(e))}function b1(e,t){if(!(t.state.loading&4)){var n=eu.get(e);if(n)var i=n.get(null);else{n=new Map,eu.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var y=u[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),i=y)}i&&n.set(null,i)}u=t.instance,y=u.getAttribute("data-precedence"),c=n.get(y)||i,c===i&&n.set(null,u),n.set(y,u),this.count++,i=Ir.bind(this),u.addEventListener("load",i),u.addEventListener("error",i),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var As={$$typeof:U,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function S1(e,t,n,i,u,c,y,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Kn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Kn(0),this.hiddenUpdates=Kn(null),this.identifierPrefix=i,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function U0(e,t,n,i,u,c,y,b,A,B,Z,J){return e=new S1(e,t,n,y,b,A,B,J),t=1,c===!0&&(t|=24),c=un(3,null,null,t),e.current=c,c.stateNode=e,t=Do(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:n,cache:t},Mo(c),e}function M0(e){return e?(e=Wa,e):Wa}function L0(e,t,n,i,u,c){u=M0(u),i.context===null?i.context=u:i.pendingContext=u,i=Yl(t),i.payload={element:n},c=c===void 0?null:c,c!==null&&(i.callback=c),n=Gl(e,i,t),n!==null&&(hn(n,e,t),ts(n,e,t))}function z0(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Jc(e,t){z0(e,t),(e=e.alternate)&&z0(e,t)}function k0(e){if(e.tag===13){var t=Pa(e,67108864);t!==null&&hn(t,e,67108864),Jc(e,67108864)}}var tu=!0;function x1(e,t,n,i){var u=L.T;L.T=null;var c=X.p;try{X.p=2,Pc(e,t,n,i)}finally{X.p=c,L.T=u}}function E1(e,t,n,i){var u=L.T;L.T=null;var c=X.p;try{X.p=8,Pc(e,t,n,i)}finally{X.p=c,L.T=u}}function Pc(e,t,n,i){if(tu){var u=Wc(i);if(u===null)Bc(e,t,i,nu,n),H0(e,i);else if(_1(u,e,t,n,i))i.stopPropagation();else if(H0(e,i),t&4&&-1<w1.indexOf(e)){for(;u!==null;){var c=kl(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=Xt(c.pendingLanes);if(y!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;y;){var A=1<<31-be(y);b.entanglements[1]|=A,y&=~A}tl(c),(Fe&6)===0&&(Hr=ut()+500,vs(0))}}break;case 13:b=Pa(c,2),b!==null&&hn(b,c,2),Vr(),Jc(c,2)}if(c=Wc(i),c===null&&Bc(e,t,i,nu,n),c===u)break;u=c}u!==null&&i.stopPropagation()}else Bc(e,t,i,null,n)}}function Wc(e){return e=no(e),Ic(e)}var nu=null;function Ic(e){if(nu=null,e=sn(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return nu=e,null}function B0(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Dn()){case Ge:return 2;case hl:return 8;case ln:case C:return 32;case H:return 268435456;default:return 32}default:return 32}}var ef=!1,la=null,aa=null,ia=null,Ts=new Map,Rs=new Map,sa=[],w1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function H0(e,t){switch(e){case"focusin":case"focusout":la=null;break;case"dragenter":case"dragleave":aa=null;break;case"mouseover":case"mouseout":ia=null;break;case"pointerover":case"pointerout":Ts.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rs.delete(t.pointerId)}}function Os(e,t,n,i,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:c,targetContainers:[u]},t!==null&&(t=kl(t),t!==null&&k0(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function _1(e,t,n,i,u){switch(t){case"focusin":return la=Os(la,e,t,n,i,u),!0;case"dragenter":return aa=Os(aa,e,t,n,i,u),!0;case"mouseover":return ia=Os(ia,e,t,n,i,u),!0;case"pointerover":var c=u.pointerId;return Ts.set(c,Os(Ts.get(c)||null,e,t,n,i,u)),!0;case"gotpointercapture":return c=u.pointerId,Rs.set(c,Os(Rs.get(c)||null,e,t,n,i,u)),!0}return!1}function q0(e){var t=sn(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,yl(e.priority,function(){if(n.tag===13){var i=dn();i=Se(i);var u=Pa(n,i);u!==null&&hn(u,n,i),Jc(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function lu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wc(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);to=i,n.target.dispatchEvent(i),to=null}else return t=kl(n),t!==null&&k0(t),e.blockedOn=n,!1;t.shift()}return!0}function V0(e,t,n){lu(e)&&n.delete(t)}function A1(){ef=!1,la!==null&&lu(la)&&(la=null),aa!==null&&lu(aa)&&(aa=null),ia!==null&&lu(ia)&&(ia=null),Ts.forEach(V0),Rs.forEach(V0)}function au(e,t){e.blockedOn===t&&(e.blockedOn=null,ef||(ef=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,A1)))}var iu=null;function F0(e){iu!==e&&(iu=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){iu===e&&(iu=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],u=e[t+2];if(typeof i!="function"){if(Ic(i||n)===null)continue;break}var c=kl(n);c!==null&&(e.splice(t,3),t-=3,Io(c,{pending:!0,data:u,method:n.method,action:i},i,u))}}))}function Cs(e){function t(A){return au(A,e)}la!==null&&au(la,e),aa!==null&&au(aa,e),ia!==null&&au(ia,e),Ts.forEach(t),Rs.forEach(t);for(var n=0;n<sa.length;n++){var i=sa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<sa.length&&(n=sa[0],n.blockedOn===null);)q0(n),n.blockedOn===null&&sa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var u=n[i],c=n[i+1],y=u[ge]||null;if(typeof c=="function")y||F0(n);else if(y){var b=null;if(c&&c.hasAttribute("formAction")){if(u=c,y=c[ge]||null)b=y.formAction;else if(Ic(u)!==null)continue}else b=y.action;typeof b=="function"?n[i+1]=b:(n.splice(i,3),i-=3),F0(n)}}}function tf(e){this._internalRoot=e}su.prototype.render=tf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var n=t.current,i=dn();L0(n,i,e,t,null,null)},su.prototype.unmount=tf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;L0(e.current,2,null,e,null,null),Vr(),t[Ie]=null}};function su(e){this._internalRoot=e}su.prototype.unstable_scheduleHydration=function(e){if(e){var t=Lt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sa.length&&t!==0&&t<sa[n].priority;n++);sa.splice(n,0,e),n===0&&q0(e)}};var Y0=a.version;if(Y0!=="19.1.0")throw Error(r(527,Y0,"19.1.0"));X.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var T1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{ee=ru.inject(T1),P=ru}catch{}}return Ns.createRoot=function(e,t){if(!o(e))throw Error(r(299));var n=!1,i="",u=im,c=sm,y=rm,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(y=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=U0(e,1,!1,null,null,n,i,u,c,y,b,null),e[Ie]=t.current,kc(e),new tf(t)},Ns.hydrateRoot=function(e,t,n){if(!o(e))throw Error(r(299));var i=!1,u="",c=im,y=sm,b=rm,A=null,B=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(A=n.unstable_transitionCallbacks),n.formState!==void 0&&(B=n.formState)),t=U0(e,1,!0,t,n??null,i,u,c,y,b,A,B),t.context=M0(null),n=t.current,i=dn(),i=Se(i),u=Yl(i),u.callback=null,Gl(n,u,i),n=i,t.current.lanes=n,jn(t,n),tl(t),e[Ie]=t.current,kc(e),new su(t)},Ns.version="19.1.0",Ns}var I0;function z1(){if(I0)return af.exports;I0=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),af.exports=L1(),af.exports}var k1=z1(),js={},ep;function B1(){if(ep)return js;ep=1,Object.defineProperty(js,"__esModule",{value:!0}),js.parse=d,js.serialize=m;const l=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,a=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const x=function(){};return x.prototype=Object.create(null),x})();function d(x,R){const _=new f,N=x.length;if(N<2)return _;const O=(R==null?void 0:R.decode)||g;let E=0;do{const D=x.indexOf("=",E);if(D===-1)break;const U=x.indexOf(";",E),G=U===-1?N:U;if(D>G){E=x.lastIndexOf(";",D-1)+1;continue}const k=h(x,E,D),te=p(x,D,k),W=x.slice(k,te);if(_[W]===void 0){let K=h(x,D+1,G),ne=p(x,G,K);const xe=O(x.slice(K,ne));_[W]=xe}E=G+1}while(E<N);return _}function h(x,R,_){do{const N=x.charCodeAt(R);if(N!==32&&N!==9)return R}while(++R<_);return _}function p(x,R,_){for(;R>_;){const N=x.charCodeAt(--R);if(N!==32&&N!==9)return R+1}return _}function m(x,R,_){const N=(_==null?void 0:_.encode)||encodeURIComponent;if(!l.test(x))throw new TypeError(`argument name is invalid: ${x}`);const O=N(R);if(!a.test(O))throw new TypeError(`argument val is invalid: ${R}`);let E=x+"="+O;if(!_)return E;if(_.maxAge!==void 0){if(!Number.isInteger(_.maxAge))throw new TypeError(`option maxAge is invalid: ${_.maxAge}`);E+="; Max-Age="+_.maxAge}if(_.domain){if(!s.test(_.domain))throw new TypeError(`option domain is invalid: ${_.domain}`);E+="; Domain="+_.domain}if(_.path){if(!r.test(_.path))throw new TypeError(`option path is invalid: ${_.path}`);E+="; Path="+_.path}if(_.expires){if(!S(_.expires)||!Number.isFinite(_.expires.valueOf()))throw new TypeError(`option expires is invalid: ${_.expires}`);E+="; Expires="+_.expires.toUTCString()}if(_.httpOnly&&(E+="; HttpOnly"),_.secure&&(E+="; Secure"),_.partitioned&&(E+="; Partitioned"),_.priority)switch(typeof _.priority=="string"?_.priority.toLowerCase():void 0){case"low":E+="; Priority=Low";break;case"medium":E+="; Priority=Medium";break;case"high":E+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${_.priority}`)}if(_.sameSite)switch(typeof _.sameSite=="string"?_.sameSite.toLowerCase():_.sameSite){case!0:case"strict":E+="; SameSite=Strict";break;case"lax":E+="; SameSite=Lax";break;case"none":E+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${_.sameSite}`)}return E}function g(x){if(x.indexOf("%")===-1)return x;try{return decodeURIComponent(x)}catch{return x}}function S(x){return o.call(x)==="[object Date]"}return js}B1();var tp="popstate";function H1(l={}){function a(r,o){let{pathname:f,search:d,hash:h}=r.location;return _f("",{pathname:f,search:d,hash:h},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function s(r,o){return typeof o=="string"?o:qs(o)}return V1(a,s,null,l)}function lt(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}function Vn(l,a){if(!l){typeof console<"u"&&console.warn(a);try{throw new Error(a)}catch{}}}function q1(){return Math.random().toString(36).substring(2,10)}function np(l,a){return{usr:l.state,key:l.key,idx:a}}function _f(l,a,s=null,r){return{pathname:typeof l=="string"?l:l.pathname,search:"",hash:"",...typeof a=="string"?Di(a):a,state:s,key:a&&a.key||r||q1()}}function qs({pathname:l="/",search:a="",hash:s=""}){return a&&a!=="?"&&(l+=a.charAt(0)==="?"?a:"?"+a),s&&s!=="#"&&(l+=s.charAt(0)==="#"?s:"#"+s),l}function Di(l){let a={};if(l){let s=l.indexOf("#");s>=0&&(a.hash=l.substring(s),l=l.substring(0,s));let r=l.indexOf("?");r>=0&&(a.search=l.substring(r),l=l.substring(0,r)),l&&(a.pathname=l)}return a}function V1(l,a,s,r={}){let{window:o=document.defaultView,v5Compat:f=!1}=r,d=o.history,h="POP",p=null,m=g();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function g(){return(d.state||{idx:null}).idx}function S(){h="POP";let O=g(),E=O==null?null:O-m;m=O,p&&p({action:h,location:N.location,delta:E})}function x(O,E){h="PUSH";let D=_f(N.location,O,E);m=g()+1;let U=np(D,m),G=N.createHref(D);try{d.pushState(U,"",G)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;o.location.assign(G)}f&&p&&p({action:h,location:N.location,delta:1})}function R(O,E){h="REPLACE";let D=_f(N.location,O,E);m=g();let U=np(D,m),G=N.createHref(D);d.replaceState(U,"",G),f&&p&&p({action:h,location:N.location,delta:0})}function _(O){let E=o.location.origin!=="null"?o.location.origin:o.location.href,D=typeof O=="string"?O:qs(O);return D=D.replace(/ $/,"%20"),lt(E,`No window.location.(origin|href) available to create URL for href: ${D}`),new URL(D,E)}let N={get action(){return h},get location(){return l(o,d)},listen(O){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(tp,S),p=O,()=>{o.removeEventListener(tp,S),p=null}},createHref(O){return a(o,O)},createURL:_,encodeLocation(O){let E=_(O);return{pathname:E.pathname,search:E.search,hash:E.hash}},push:x,replace:R,go(O){return d.go(O)}};return N}function ny(l,a,s="/"){return F1(l,a,s,!1)}function F1(l,a,s,r){let o=typeof a=="string"?Di(a):a,f=zl(o.pathname||"/",s);if(f==null)return null;let d=ly(l);Y1(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=eb(f);h=W1(d[p],m,r)}return h}function ly(l,a=[],s=[],r=""){let o=(f,d,h)=>{let p={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};p.relativePath.startsWith("/")&&(lt(p.relativePath.startsWith(r),`Absolute route path "${p.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(r.length));let m=Ll([r,p.relativePath]),g=s.concat(p);f.children&&f.children.length>0&&(lt(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),ly(f.children,a,g,m)),!(f.path==null&&!f.index)&&a.push({path:m,score:J1(m,f.index),routesMeta:g})};return l.forEach((f,d)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))o(f,d);else for(let p of ay(f.path))o(f,d,p)}),a}function ay(l){let a=l.split("/");if(a.length===0)return[];let[s,...r]=a,o=s.endsWith("?"),f=s.replace(/\?$/,"");if(r.length===0)return o?[f,""]:[f];let d=ay(r.join("/")),h=[];return h.push(...d.map(p=>p===""?f:[f,p].join("/"))),o&&h.push(...d),h.map(p=>l.startsWith("/")&&p===""?"/":p)}function Y1(l){l.sort((a,s)=>a.score!==s.score?s.score-a.score:P1(a.routesMeta.map(r=>r.childrenIndex),s.routesMeta.map(r=>r.childrenIndex)))}var G1=/^:[\w-]+$/,$1=3,X1=2,Z1=1,Q1=10,K1=-2,lp=l=>l==="*";function J1(l,a){let s=l.split("/"),r=s.length;return s.some(lp)&&(r+=K1),a&&(r+=X1),s.filter(o=>!lp(o)).reduce((o,f)=>o+(G1.test(f)?$1:f===""?Z1:Q1),r)}function P1(l,a){return l.length===a.length&&l.slice(0,-1).every((r,o)=>r===a[o])?l[l.length-1]-a[a.length-1]:0}function W1(l,a,s=!1){let{routesMeta:r}=l,o={},f="/",d=[];for(let h=0;h<r.length;++h){let p=r[h],m=h===r.length-1,g=f==="/"?a:a.slice(f.length)||"/",S=Su({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},g),x=p.route;if(!S&&m&&s&&!r[r.length-1].route.index&&(S=Su({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},g)),!S)return null;Object.assign(o,S.params),d.push({params:o,pathname:Ll([f,S.pathname]),pathnameBase:ab(Ll([f,S.pathnameBase])),route:x}),S.pathnameBase!=="/"&&(f=Ll([f,S.pathnameBase]))}return d}function Su(l,a){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[s,r]=I1(l.path,l.caseSensitive,l.end),o=a.match(s);if(!o)return null;let f=o[0],d=f.replace(/(.)\/+$/,"$1"),h=o.slice(1);return{params:r.reduce((m,{paramName:g,isOptional:S},x)=>{if(g==="*"){let _=h[x]||"";d=f.slice(0,f.length-_.length).replace(/(.)\/+$/,"$1")}const R=h[x];return S&&!R?m[g]=void 0:m[g]=(R||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:l}}function I1(l,a=!1,s=!0){Vn(l==="*"||!l.endsWith("*")||l.endsWith("/*"),`Route path "${l}" will be treated as if it were "${l.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${l.replace(/\*$/,"/*")}".`);let r=[],o="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(r.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(r.push({paramName:"*"}),o+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?o+="\\/*$":l!==""&&l!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,a?void 0:"i"),r]}function eb(l){try{return l.split("/").map(a=>decodeURIComponent(a).replace(/\//g,"%2F")).join("/")}catch(a){return Vn(!1,`The URL path "${l}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${a}).`),l}}function zl(l,a){if(a==="/")return l;if(!l.toLowerCase().startsWith(a.toLowerCase()))return null;let s=a.endsWith("/")?a.length-1:a.length,r=l.charAt(s);return r&&r!=="/"?null:l.slice(s)||"/"}function tb(l,a="/"){let{pathname:s,search:r="",hash:o=""}=typeof l=="string"?Di(l):l;return{pathname:s?s.startsWith("/")?s:nb(s,a):a,search:ib(r),hash:sb(o)}}function nb(l,a){let s=a.replace(/\/+$/,"").split("/");return l.split("/").forEach(o=>{o===".."?s.length>1&&s.pop():o!=="."&&s.push(o)}),s.length>1?s.join("/"):"/"}function of(l,a,s,r){return`Cannot include a '${l}' character in a manually specified \`to.${a}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function lb(l){return l.filter((a,s)=>s===0||a.route.path&&a.route.path.length>0)}function Ff(l){let a=lb(l);return a.map((s,r)=>r===a.length-1?s.pathname:s.pathnameBase)}function Yf(l,a,s,r=!1){let o;typeof l=="string"?o=Di(l):(o={...l},lt(!o.pathname||!o.pathname.includes("?"),of("?","pathname","search",o)),lt(!o.pathname||!o.pathname.includes("#"),of("#","pathname","hash",o)),lt(!o.search||!o.search.includes("#"),of("#","search","hash",o)));let f=l===""||o.pathname==="",d=f?"/":o.pathname,h;if(d==null)h=s;else{let S=a.length-1;if(!r&&d.startsWith("..")){let x=d.split("/");for(;x[0]==="..";)x.shift(),S-=1;o.pathname=x.join("/")}h=S>=0?a[S]:"/"}let p=tb(o,h),m=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&s.endsWith("/");return!p.pathname.endsWith("/")&&(m||g)&&(p.pathname+="/"),p}var Ll=l=>l.join("/").replace(/\/\/+/g,"/"),ab=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),ib=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,sb=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function rb(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}var iy=["POST","PUT","PATCH","DELETE"];new Set(iy);var ub=["GET",...iy];new Set(ub);var Ni=w.createContext(null);Ni.displayName="DataRouter";var Uu=w.createContext(null);Uu.displayName="DataRouterState";var sy=w.createContext({isTransitioning:!1});sy.displayName="ViewTransition";var ob=w.createContext(new Map);ob.displayName="Fetchers";var cb=w.createContext(null);cb.displayName="Await";var Gn=w.createContext(null);Gn.displayName="Navigation";var Ys=w.createContext(null);Ys.displayName="Location";var Cn=w.createContext({outlet:null,matches:[],isDataRoute:!1});Cn.displayName="Route";var Gf=w.createContext(null);Gf.displayName="RouteError";function fb(l,{relative:a}={}){lt(ji(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:r}=w.useContext(Gn),{hash:o,pathname:f,search:d}=Gs(l,{relative:a}),h=f;return s!=="/"&&(h=f==="/"?s:Ll([s,f])),r.createHref({pathname:h,search:d,hash:o})}function ji(){return w.useContext(Ys)!=null}function cl(){return lt(ji(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(Ys).location}var ry="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function uy(l){w.useContext(Gn).static||w.useLayoutEffect(l)}function $n(){let{isDataRoute:l}=w.useContext(Cn);return l?Rb():db()}function db(){lt(ji(),"useNavigate() may be used only in the context of a <Router> component.");let l=w.useContext(Ni),{basename:a,navigator:s}=w.useContext(Gn),{matches:r}=w.useContext(Cn),{pathname:o}=cl(),f=JSON.stringify(Ff(r)),d=w.useRef(!1);return uy(()=>{d.current=!0}),w.useCallback((p,m={})=>{if(Vn(d.current,ry),!d.current)return;if(typeof p=="number"){s.go(p);return}let g=Yf(p,JSON.parse(f),o,m.relative==="path");l==null&&a!=="/"&&(g.pathname=g.pathname==="/"?a:Ll([a,g.pathname])),(m.replace?s.replace:s.push)(g,m.state,m)},[a,s,f,o,l])}var hb=w.createContext(null);function mb(l){let a=w.useContext(Cn).outlet;return a&&w.createElement(hb.Provider,{value:l},a)}function pb(){let{matches:l}=w.useContext(Cn),a=l[l.length-1];return a?a.params:{}}function Gs(l,{relative:a}={}){let{matches:s}=w.useContext(Cn),{pathname:r}=cl(),o=JSON.stringify(Ff(s));return w.useMemo(()=>Yf(l,JSON.parse(o),r,a==="path"),[l,o,r,a])}function yb(l,a){return oy(l,a)}function oy(l,a,s,r){var D;lt(ji(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:f}=w.useContext(Gn),{matches:d}=w.useContext(Cn),h=d[d.length-1],p=h?h.params:{},m=h?h.pathname:"/",g=h?h.pathnameBase:"/",S=h&&h.route;{let U=S&&S.path||"";cy(m,!S||U.endsWith("*")||U.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${U}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${U}"> to <Route path="${U==="/"?"*":`${U}/*`}">.`)}let x=cl(),R;if(a){let U=typeof a=="string"?Di(a):a;lt(g==="/"||((D=U.pathname)==null?void 0:D.startsWith(g)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${U.pathname}" was given in the \`location\` prop.`),R=U}else R=x;let _=R.pathname||"/",N=_;if(g!=="/"){let U=g.replace(/^\//,"").split("/");N="/"+_.replace(/^\//,"").split("/").slice(U.length).join("/")}let O=!f&&s&&s.matches&&s.matches.length>0?s.matches:ny(l,{pathname:N});Vn(S||O!=null,`No routes matched location "${R.pathname}${R.search}${R.hash}" `),Vn(O==null||O[O.length-1].route.element!==void 0||O[O.length-1].route.Component!==void 0||O[O.length-1].route.lazy!==void 0,`Matched leaf route at location "${R.pathname}${R.search}${R.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let E=xb(O&&O.map(U=>Object.assign({},U,{params:Object.assign({},p,U.params),pathname:Ll([g,o.encodeLocation?o.encodeLocation(U.pathname).pathname:U.pathname]),pathnameBase:U.pathnameBase==="/"?g:Ll([g,o.encodeLocation?o.encodeLocation(U.pathnameBase).pathname:U.pathnameBase])})),d,s,r);return a&&E?w.createElement(Ys.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...R},navigationType:"POP"}},E):E}function gb(){let l=Tb(),a=rb(l)?`${l.status} ${l.statusText}`:l instanceof Error?l.message:JSON.stringify(l),s=l instanceof Error?l.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},f={padding:"2px 4px",backgroundColor:r},d=null;return console.error("Error handled by React Router default ErrorBoundary:",l),d=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:f},"ErrorBoundary")," or"," ",w.createElement("code",{style:f},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},a),s?w.createElement("pre",{style:o},s):null,d)}var vb=w.createElement(gb,null),bb=class extends w.Component{constructor(l){super(l),this.state={location:l.location,revalidation:l.revalidation,error:l.error}}static getDerivedStateFromError(l){return{error:l}}static getDerivedStateFromProps(l,a){return a.location!==l.location||a.revalidation!=="idle"&&l.revalidation==="idle"?{error:l.error,location:l.location,revalidation:l.revalidation}:{error:l.error!==void 0?l.error:a.error,location:a.location,revalidation:l.revalidation||a.revalidation}}componentDidCatch(l,a){console.error("React Router caught the following error during render",l,a)}render(){return this.state.error!==void 0?w.createElement(Cn.Provider,{value:this.props.routeContext},w.createElement(Gf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Sb({routeContext:l,match:a,children:s}){let r=w.useContext(Ni);return r&&r.static&&r.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=a.route.id),w.createElement(Cn.Provider,{value:l},s)}function xb(l,a=[],s=null,r=null){if(l==null){if(!s)return null;if(s.errors)l=s.matches;else if(a.length===0&&!s.initialized&&s.matches.length>0)l=s.matches;else return null}let o=l,f=s==null?void 0:s.errors;if(f!=null){let p=o.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);lt(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let d=!1,h=-1;if(s)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:g,errors:S}=s,x=m.route.loader&&!g.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||x){d=!0,h>=0?o=o.slice(0,h+1):o=[o[0]];break}}}return o.reduceRight((p,m,g)=>{let S,x=!1,R=null,_=null;s&&(S=f&&m.route.id?f[m.route.id]:void 0,R=m.route.errorElement||vb,d&&(h<0&&g===0?(cy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,_=null):h===g&&(x=!0,_=m.route.hydrateFallbackElement||null)));let N=a.concat(o.slice(0,g+1)),O=()=>{let E;return S?E=R:x?E=_:m.route.Component?E=w.createElement(m.route.Component,null):m.route.element?E=m.route.element:E=p,w.createElement(Sb,{match:m,routeContext:{outlet:p,matches:N,isDataRoute:s!=null},children:E})};return s&&(m.route.ErrorBoundary||m.route.errorElement||g===0)?w.createElement(bb,{location:s.location,revalidation:s.revalidation,component:R,error:S,children:O(),routeContext:{outlet:null,matches:N,isDataRoute:!0}}):O()},null)}function $f(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Eb(l){let a=w.useContext(Ni);return lt(a,$f(l)),a}function wb(l){let a=w.useContext(Uu);return lt(a,$f(l)),a}function _b(l){let a=w.useContext(Cn);return lt(a,$f(l)),a}function Xf(l){let a=_b(l),s=a.matches[a.matches.length-1];return lt(s.route.id,`${l} can only be used on routes that contain a unique "id"`),s.route.id}function Ab(){return Xf("useRouteId")}function Tb(){var r;let l=w.useContext(Gf),a=wb("useRouteError"),s=Xf("useRouteError");return l!==void 0?l:(r=a.errors)==null?void 0:r[s]}function Rb(){let{router:l}=Eb("useNavigate"),a=Xf("useNavigate"),s=w.useRef(!1);return uy(()=>{s.current=!0}),w.useCallback(async(o,f={})=>{Vn(s.current,ry),s.current&&(typeof o=="number"?l.navigate(o):await l.navigate(o,{fromRouteId:a,...f}))},[l,a])}var ap={};function cy(l,a,s){!a&&!ap[l]&&(ap[l]=!0,Vn(!1,s))}w.memo(Ob);function Ob({routes:l,future:a,state:s}){return oy(l,void 0,s,a)}function Cb({to:l,replace:a,state:s,relative:r}){lt(ji(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=w.useContext(Gn);Vn(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=w.useContext(Cn),{pathname:d}=cl(),h=$n(),p=Yf(l,Ff(f),d,r==="path"),m=JSON.stringify(p);return w.useEffect(()=>{h(JSON.parse(m),{replace:a,state:s,relative:r})},[h,m,r,a,s]),null}function Db(l){return mb(l.context)}function ll(l){lt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Nb({basename:l="/",children:a=null,location:s,navigationType:r="POP",navigator:o,static:f=!1}){lt(!ji(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=l.replace(/^\/*/,"/"),h=w.useMemo(()=>({basename:d,navigator:o,static:f,future:{}}),[d,o,f]);typeof s=="string"&&(s=Di(s));let{pathname:p="/",search:m="",hash:g="",state:S=null,key:x="default"}=s,R=w.useMemo(()=>{let _=zl(p,d);return _==null?null:{location:{pathname:_,search:m,hash:g,state:S,key:x},navigationType:r}},[d,p,m,g,S,x,r]);return Vn(R!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${g}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:w.createElement(Gn.Provider,{value:h},w.createElement(Ys.Provider,{children:a,value:R}))}function jb({children:l,location:a}){return yb(Af(l),a)}function Af(l,a=[]){let s=[];return w.Children.forEach(l,(r,o)=>{if(!w.isValidElement(r))return;let f=[...a,o];if(r.type===w.Fragment){s.push.apply(s,Af(r.props.children,f));return}lt(r.type===ll,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),lt(!r.props.index||!r.props.children,"An index route cannot have child routes.");let d={id:r.props.id||f.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(d.children=Af(r.props.children,f)),s.push(d)}),s}var du="get",hu="application/x-www-form-urlencoded";function Mu(l){return l!=null&&typeof l.tagName=="string"}function Ub(l){return Mu(l)&&l.tagName.toLowerCase()==="button"}function Mb(l){return Mu(l)&&l.tagName.toLowerCase()==="form"}function Lb(l){return Mu(l)&&l.tagName.toLowerCase()==="input"}function zb(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function kb(l,a){return l.button===0&&(!a||a==="_self")&&!zb(l)}var uu=null;function Bb(){if(uu===null)try{new FormData(document.createElement("form"),0),uu=!1}catch{uu=!0}return uu}var Hb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function cf(l){return l!=null&&!Hb.has(l)?(Vn(!1,`"${l}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${hu}"`),null):l}function qb(l,a){let s,r,o,f,d;if(Mb(l)){let h=l.getAttribute("action");r=h?zl(h,a):null,s=l.getAttribute("method")||du,o=cf(l.getAttribute("enctype"))||hu,f=new FormData(l)}else if(Ub(l)||Lb(l)&&(l.type==="submit"||l.type==="image")){let h=l.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=l.getAttribute("formaction")||h.getAttribute("action");if(r=p?zl(p,a):null,s=l.getAttribute("formmethod")||h.getAttribute("method")||du,o=cf(l.getAttribute("formenctype"))||cf(h.getAttribute("enctype"))||hu,f=new FormData(h,l),!Bb()){let{name:m,type:g,value:S}=l;if(g==="image"){let x=m?`${m}.`:"";f.append(`${x}x`,"0"),f.append(`${x}y`,"0")}else m&&f.append(m,S)}}else{if(Mu(l))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=du,r=null,o=hu,d=l}return f&&o==="text/plain"&&(d=f,f=void 0),{action:r,method:s.toLowerCase(),encType:o,formData:f,body:d}}function Zf(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}async function Vb(l,a){if(l.id in a)return a[l.id];try{let s=await import(l.module);return a[l.id]=s,s}catch(s){return console.error(`Error loading route module \`${l.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Fb(l){return l==null?!1:l.href==null?l.rel==="preload"&&typeof l.imageSrcSet=="string"&&typeof l.imageSizes=="string":typeof l.rel=="string"&&typeof l.href=="string"}async function Yb(l,a,s){let r=await Promise.all(l.map(async o=>{let f=a.routes[o.route.id];if(f){let d=await Vb(f,s);return d.links?d.links():[]}return[]}));return Zb(r.flat(1).filter(Fb).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function ip(l,a,s,r,o,f){let d=(p,m)=>s[m]?p.route.id!==s[m].route.id:!0,h=(p,m)=>{var g;return s[m].pathname!==p.pathname||((g=s[m].route.path)==null?void 0:g.endsWith("*"))&&s[m].params["*"]!==p.params["*"]};return f==="assets"?a.filter((p,m)=>d(p,m)||h(p,m)):f==="data"?a.filter((p,m)=>{var S;let g=r.routes[p.route.id];if(!g||!g.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let x=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((S=s[0])==null?void 0:S.params)||{},nextUrl:new URL(l,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof x=="boolean")return x}return!0}):[]}function Gb(l,a,{includeHydrateFallback:s}={}){return $b(l.map(r=>{let o=a.routes[r.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),s&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function $b(l){return[...new Set(l)]}function Xb(l){let a={},s=Object.keys(l).sort();for(let r of s)a[r]=l[r];return a}function Zb(l,a){let s=new Set;return new Set(a),l.reduce((r,o)=>{let f=JSON.stringify(Xb(o));return s.has(f)||(s.add(f),r.push({key:f,link:o})),r},[])}var Qb=new Set([100,101,204,205]);function Kb(l,a){let s=typeof l=="string"?new URL(l,typeof window>"u"?"server://singlefetch/":window.location.origin):l;return s.pathname==="/"?s.pathname="_root.data":a&&zl(s.pathname,a)==="/"?s.pathname=`${a.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function fy(){let l=w.useContext(Ni);return Zf(l,"You must render this element inside a <DataRouterContext.Provider> element"),l}function Jb(){let l=w.useContext(Uu);return Zf(l,"You must render this element inside a <DataRouterStateContext.Provider> element"),l}var Qf=w.createContext(void 0);Qf.displayName="FrameworkContext";function dy(){let l=w.useContext(Qf);return Zf(l,"You must render this element inside a <HydratedRouter> element"),l}function Pb(l,a){let s=w.useContext(Qf),[r,o]=w.useState(!1),[f,d]=w.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:g,onTouchStart:S}=a,x=w.useRef(null);w.useEffect(()=>{if(l==="render"&&d(!0),l==="viewport"){let N=E=>{E.forEach(D=>{d(D.isIntersecting)})},O=new IntersectionObserver(N,{threshold:.5});return x.current&&O.observe(x.current),()=>{O.disconnect()}}},[l]),w.useEffect(()=>{if(r){let N=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(N)}}},[r]);let R=()=>{o(!0)},_=()=>{o(!1),d(!1)};return s?l!=="intent"?[f,x,{}]:[f,x,{onFocus:Us(h,R),onBlur:Us(p,_),onMouseEnter:Us(m,R),onMouseLeave:Us(g,_),onTouchStart:Us(S,R)}]:[!1,x,{}]}function Us(l,a){return s=>{l&&l(s),s.defaultPrevented||a(s)}}function Wb({page:l,...a}){let{router:s}=fy(),r=w.useMemo(()=>ny(s.routes,l,s.basename),[s.routes,l,s.basename]);return r?w.createElement(e2,{page:l,matches:r,...a}):null}function Ib(l){let{manifest:a,routeModules:s}=dy(),[r,o]=w.useState([]);return w.useEffect(()=>{let f=!1;return Yb(l,a,s).then(d=>{f||o(d)}),()=>{f=!0}},[l,a,s]),r}function e2({page:l,matches:a,...s}){let r=cl(),{manifest:o,routeModules:f}=dy(),{basename:d}=fy(),{loaderData:h,matches:p}=Jb(),m=w.useMemo(()=>ip(l,a,p,o,r,"data"),[l,a,p,o,r]),g=w.useMemo(()=>ip(l,a,p,o,r,"assets"),[l,a,p,o,r]),S=w.useMemo(()=>{if(l===r.pathname+r.search+r.hash)return[];let _=new Set,N=!1;if(a.forEach(E=>{var U;let D=o.routes[E.route.id];!D||!D.hasLoader||(!m.some(G=>G.route.id===E.route.id)&&E.route.id in h&&((U=f[E.route.id])!=null&&U.shouldRevalidate)||D.hasClientLoader?N=!0:_.add(E.route.id))}),_.size===0)return[];let O=Kb(l,d);return N&&_.size>0&&O.searchParams.set("_routes",a.filter(E=>_.has(E.route.id)).map(E=>E.route.id).join(",")),[O.pathname+O.search]},[d,h,r,o,m,a,l,f]),x=w.useMemo(()=>Gb(g,o),[g,o]),R=Ib(g);return w.createElement(w.Fragment,null,S.map(_=>w.createElement("link",{key:_,rel:"prefetch",as:"fetch",href:_,...s})),x.map(_=>w.createElement("link",{key:_,rel:"modulepreload",href:_,...s})),R.map(({key:_,link:N})=>w.createElement("link",{key:_,...N})))}function t2(...l){return a=>{l.forEach(s=>{typeof s=="function"?s(a):s!=null&&(s.current=a)})}}var hy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{hy&&(window.__reactRouterVersion="7.5.2")}catch{}function n2({basename:l,children:a,window:s}){let r=w.useRef();r.current==null&&(r.current=H1({window:s,v5Compat:!0}));let o=r.current,[f,d]=w.useState({action:o.action,location:o.location}),h=w.useCallback(p=>{w.startTransition(()=>d(p))},[d]);return w.useLayoutEffect(()=>o.listen(h),[o,h]),w.createElement(Nb,{basename:l,children:a,location:f.location,navigationType:f.action,navigator:o})}var my=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,py=w.forwardRef(function({onClick:a,discover:s="render",prefetch:r="none",relative:o,reloadDocument:f,replace:d,state:h,target:p,to:m,preventScrollReset:g,viewTransition:S,...x},R){let{basename:_}=w.useContext(Gn),N=typeof m=="string"&&my.test(m),O,E=!1;if(typeof m=="string"&&N&&(O=m,hy))try{let ne=new URL(window.location.href),xe=m.startsWith("//")?new URL(ne.protocol+m):new URL(m),oe=zl(xe.pathname,_);xe.origin===ne.origin&&oe!=null?m=oe+xe.search+xe.hash:E=!0}catch{Vn(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let D=fb(m,{relative:o}),[U,G,k]=Pb(r,x),te=s2(m,{replace:d,state:h,target:p,preventScrollReset:g,relative:o,viewTransition:S});function W(ne){a&&a(ne),ne.defaultPrevented||te(ne)}let K=w.createElement("a",{...x,...k,href:O||D,onClick:E||f?a:W,ref:t2(R,G),target:p,"data-discover":!N&&s==="render"?"true":void 0});return U&&!N?w.createElement(w.Fragment,null,K,w.createElement(Wb,{page:D})):K});py.displayName="Link";var l2=w.forwardRef(function({"aria-current":a="page",caseSensitive:s=!1,className:r="",end:o=!1,style:f,to:d,viewTransition:h,children:p,...m},g){let S=Gs(d,{relative:m.relative}),x=cl(),R=w.useContext(Uu),{navigator:_,basename:N}=w.useContext(Gn),O=R!=null&&f2(S)&&h===!0,E=_.encodeLocation?_.encodeLocation(S).pathname:S.pathname,D=x.pathname,U=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;s||(D=D.toLowerCase(),U=U?U.toLowerCase():null,E=E.toLowerCase()),U&&N&&(U=zl(U,N)||U);const G=E!=="/"&&E.endsWith("/")?E.length-1:E.length;let k=D===E||!o&&D.startsWith(E)&&D.charAt(G)==="/",te=U!=null&&(U===E||!o&&U.startsWith(E)&&U.charAt(E.length)==="/"),W={isActive:k,isPending:te,isTransitioning:O},K=k?a:void 0,ne;typeof r=="function"?ne=r(W):ne=[r,k?"active":null,te?"pending":null,O?"transitioning":null].filter(Boolean).join(" ");let xe=typeof f=="function"?f(W):f;return w.createElement(py,{...m,"aria-current":K,className:ne,ref:g,style:xe,to:d,viewTransition:h},typeof p=="function"?p(W):p)});l2.displayName="NavLink";var a2=w.forwardRef(({discover:l="render",fetcherKey:a,navigate:s,reloadDocument:r,replace:o,state:f,method:d=du,action:h,onSubmit:p,relative:m,preventScrollReset:g,viewTransition:S,...x},R)=>{let _=o2(),N=c2(h,{relative:m}),O=d.toLowerCase()==="get"?"get":"post",E=typeof h=="string"&&my.test(h),D=U=>{if(p&&p(U),U.defaultPrevented)return;U.preventDefault();let G=U.nativeEvent.submitter,k=(G==null?void 0:G.getAttribute("formmethod"))||d;_(G||U.currentTarget,{fetcherKey:a,method:k,navigate:s,replace:o,state:f,relative:m,preventScrollReset:g,viewTransition:S})};return w.createElement("form",{ref:R,method:O,action:N,onSubmit:r?p:D,...x,"data-discover":!E&&l==="render"?"true":void 0})});a2.displayName="Form";function i2(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function yy(l){let a=w.useContext(Ni);return lt(a,i2(l)),a}function s2(l,{target:a,replace:s,state:r,preventScrollReset:o,relative:f,viewTransition:d}={}){let h=$n(),p=cl(),m=Gs(l,{relative:f});return w.useCallback(g=>{if(kb(g,a)){g.preventDefault();let S=s!==void 0?s:qs(p)===qs(m);h(l,{replace:S,state:r,preventScrollReset:o,relative:f,viewTransition:d})}},[p,h,m,s,r,a,l,o,f,d])}var r2=0,u2=()=>`__${String(++r2)}__`;function o2(){let{router:l}=yy("useSubmit"),{basename:a}=w.useContext(Gn),s=Ab();return w.useCallback(async(r,o={})=>{let{action:f,method:d,encType:h,formData:p,body:m}=qb(r,a);if(o.navigate===!1){let g=o.fetcherKey||u2();await l.fetch(g,s,o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,flushSync:o.flushSync})}else await l.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,replace:o.replace,state:o.state,fromRouteId:s,flushSync:o.flushSync,viewTransition:o.viewTransition})},[l,a,s])}function c2(l,{relative:a}={}){let{basename:s}=w.useContext(Gn),r=w.useContext(Cn);lt(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),f={...Gs(l||".",{relative:a})},d=cl();if(l==null){f.search=d.search;let h=new URLSearchParams(f.search),p=h.getAll("index");if(p.some(g=>g==="")){h.delete("index"),p.filter(S=>S).forEach(S=>h.append("index",S));let g=h.toString();f.search=g?`?${g}`:""}}return(!l||l===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(f.pathname=f.pathname==="/"?s:Ll([s,f.pathname])),qs(f)}function f2(l,a={}){let s=w.useContext(sy);lt(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=yy("useViewTransitionState"),o=Gs(l,{relative:a.relative});if(!s.isTransitioning)return!1;let f=zl(s.currentLocation.pathname,r)||s.currentLocation.pathname,d=zl(s.nextLocation.pathname,r)||s.nextLocation.pathname;return Su(o.pathname,d)!=null||Su(o.pathname,f)!=null}new TextEncoder;[...Qb];const d2="/assets/illustration1-BZ2qBqb5.png",h2="/assets/illustration2-D_JRMXuN.png",m2="/assets/illustration3-0vw7lghX.png",p2="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAhCAYAAAC803lsAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANlSURBVHgBxVc9T9tAGH7vLqm6VErExGaIkBgDVaWO4RcUtm6EXwAZOwG/oHTrBv0FpL+AZENqoWFDype3jpgNNdjX57XPcDjBoc5HH+l057vz3eP324JGYHl5fR+dEwRBzXVbHs0BueREqbR+TCSqPFZKOeg2aA5Q9gNLQgixZ005xeLi7c3N73OaMUSCxMHjknax7GDg+b6/BhW5NEPkkiSEoLrWtKm1aGB8i6ldKdUp+jXKCMcpF5QSRzj9Az/j/JNe77Jm71Gl0ttNkPgakRA1rfUVRpt4bAWB/0lK+RGEVqEiARU1KAMWFhZxvtzGua+54Z73yfMkJj1D4rDT+XlkH8AeAyJbPETL5D0sjYgEke/nl/L5/JK5b9fel8PlDbJsJYl2+0cL3dKotdXVsjMYKHgZVSBJFz2L/PCZo/Ahd2aohhYlTYDBQJ4xCR7jCx22M9ib7XVk4lADDXai+iDeN0vfpkIEIq+wV8HwXN9/UxRCG+MT28m98Lot69FjM+h2L54QzlFG5PPkBUHoZbABrxAEksdo+ja5VynJxs/e4vZ6FyPVnFki7XarhYPZfkKR4ys/m8v6I7ZzymCSz9nPZDYiRHBlPbItsNirCAkP6kHKqMYq7HYvT2jaRNhjbLeEzovWFx/BhhwzHiuNiYgMBlEk5ijpuucujzudS45D7A1Ql/wFadwYabTSpJGZiC2NIMg/+VJ4CHsD1CQKUQsNugy3PqBpExkljQSYgMcqg83EZcR22pkvdl87isZzSWlYJAzu6P4+9CqMRWqKeLFE7CgaQ6k/leQ+Uy40yLg12ml4kRTfaVIiySiKqS/RSpilh4DEtkOPIdwzCfUg7Y6xqjG1RDU+lKOo1tIVHEZJF0a9c30d2k3VtBchlQgnMNy3b1s/i9vackJTwrOqwaXVKGwzCd3E1JbpGSzu2rjY8C9IlQjbBAjxhXUzVacJgADXx5nwtmAjWQOnGKtucqZE6J7o8qcQYd2Cqu/MSgHpRFA892nKgDdtcLiPyaysvCuPJTILsDehIOdIW2cyUNFZnKmHalWTto9pjkC9WxuSiBDSpTkD0qkI+g/gSI0y4Tj+k0TbmSsRjtIwUuv/Wjd9/1WVM3jm4jkLuFiKpRDln4uHH7q5Eol8QzfxL71nftwe8Bfkx1ujnHc2/QAAAABJRU5ErkJggg==";function y2(){const l=$n();return v.jsxs("main",{className:"landing-page",children:[v.jsxs("section",{className:"left-panel",children:[v.jsxs("h1",{children:["BIZTREND",v.jsx("br",{}),"F",v.jsx("img",{src:p2,alt:"system icon"}),v.jsx("span",{children:"RECAST"})]}),v.jsx("h4",{children:"STAY AHEAD IN THE BUSINESS GAME!"}),v.jsx("br",{}),v.jsx("br",{}),v.jsx("p",{children:"Ready to explore?"}),v.jsx("p",{children:"Select your user type and we'll guide the way!"}),v.jsx("br",{}),v.jsxs("div",{className:"buttons-group extra-style",children:[v.jsx("button",{className:"admin-btn",onClick:()=>l("/login"),children:"Registered User"}),v.jsx("button",{className:"guest-btn",onClick:()=>l("/home"),children:"Guest User"})]})]}),v.jsxs("section",{className:"right-panel",children:[v.jsx("div",{className:"big-circle"}),v.jsx("div",{className:"small-circle-top"}),v.jsx("div",{className:"small-circle-bottom"}),v.jsx("img",{className:"illustration-top",src:m2,alt:"illustration1"}),v.jsx("img",{className:"illustration-middle",src:h2,alt:"illustration2"}),v.jsx("img",{className:"illustration-bottom",src:d2,alt:"illustration3"})]})]})}const g2="/assets/login-DtlAx1r0.png",v2="/assets/Logo-black-color-CEzmO1Lh.svg";var $s=l=>l.type==="checkbox",Da=l=>l instanceof Date,Gt=l=>l==null;const gy=l=>typeof l=="object";var ht=l=>!Gt(l)&&!Array.isArray(l)&&gy(l)&&!Da(l),b2=l=>ht(l)&&l.target?$s(l.target)?l.target.checked:l.target.value:l,S2=l=>l.substring(0,l.search(/\.\d+(\.|$)/))||l,x2=(l,a)=>l.has(S2(a)),E2=l=>{const a=l.constructor&&l.constructor.prototype;return ht(a)&&a.hasOwnProperty("isPrototypeOf")},Kf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Bt(l){let a;const s=Array.isArray(l),r=typeof FileList<"u"?l instanceof FileList:!1;if(l instanceof Date)a=new Date(l);else if(l instanceof Set)a=new Set(l);else if(!(Kf&&(l instanceof Blob||r))&&(s||ht(l)))if(a=s?[]:{},!s&&!E2(l))a=l;else for(const o in l)l.hasOwnProperty(o)&&(a[o]=Bt(l[o]));else return l;return a}var Lu=l=>Array.isArray(l)?l.filter(Boolean):[],bt=l=>l===void 0,de=(l,a,s)=>{if(!a||!ht(l))return s;const r=Lu(a.split(/[,[\].]+?/)).reduce((o,f)=>Gt(o)?o:o[f],l);return bt(r)||r===l?bt(l[a])?s:l[a]:r},al=l=>typeof l=="boolean",Jf=l=>/^\w*$/.test(l),vy=l=>Lu(l.replace(/["|']|\]/g,"").split(/\.|\[/)),Ke=(l,a,s)=>{let r=-1;const o=Jf(a)?[a]:vy(a),f=o.length,d=f-1;for(;++r<f;){const h=o[r];let p=s;if(r!==d){const m=l[h];p=ht(m)||Array.isArray(m)?m:isNaN(+o[r+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;l[h]=p,l=l[h]}};const sp={BLUR:"blur",FOCUS_OUT:"focusout"},kn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Ul={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};Pe.createContext(null);var w2=(l,a,s,r=!0)=>{const o={defaultValues:a._defaultValues};for(const f in l)Object.defineProperty(o,f,{get:()=>{const d=f;return a._proxyFormState[d]!==kn.all&&(a._proxyFormState[d]=!r||kn.all),l[d]}});return o},Tf=l=>Gt(l)||!gy(l);function ua(l,a){if(Tf(l)||Tf(a))return l===a;if(Da(l)&&Da(a))return l.getTime()===a.getTime();const s=Object.keys(l),r=Object.keys(a);if(s.length!==r.length)return!1;for(const o of s){const f=l[o];if(!r.includes(o))return!1;if(o!=="ref"){const d=a[o];if(Da(f)&&Da(d)||ht(f)&&ht(d)||Array.isArray(f)&&Array.isArray(d)?!ua(f,d):f!==d)return!1}}return!0}var sl=l=>typeof l=="string",_2=(l,a,s,r,o)=>sl(l)?(r&&a.watch.add(l),de(s,l,o)):Array.isArray(l)?l.map(f=>(r&&a.watch.add(f),de(s,f))):(r&&(a.watchAll=!0),s),by=(l,a,s,r,o)=>a?{...s[l],types:{...s[l]&&s[l].types?s[l].types:{},[r]:o||!0}}:{},Bs=l=>Array.isArray(l)?l:[l],rp=()=>{let l=[];return{get observers(){return l},next:o=>{for(const f of l)f.next&&f.next(o)},subscribe:o=>(l.push(o),{unsubscribe:()=>{l=l.filter(f=>f!==o)}}),unsubscribe:()=>{l=[]}}},Yt=l=>ht(l)&&!Object.keys(l).length,Pf=l=>l.type==="file",Bn=l=>typeof l=="function",xu=l=>{if(!Kf)return!1;const a=l?l.ownerDocument:0;return l instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},Sy=l=>l.type==="select-multiple",Wf=l=>l.type==="radio",A2=l=>Wf(l)||$s(l),ff=l=>xu(l)&&l.isConnected;function T2(l,a){const s=a.slice(0,-1).length;let r=0;for(;r<s;)l=bt(l)?r++:l[a[r++]];return l}function R2(l){for(const a in l)if(l.hasOwnProperty(a)&&!bt(l[a]))return!1;return!0}function wt(l,a){const s=Array.isArray(a)?a:Jf(a)?[a]:vy(a),r=s.length===1?l:T2(l,s),o=s.length-1,f=s[o];return r&&delete r[f],o!==0&&(ht(r)&&Yt(r)||Array.isArray(r)&&R2(r))&&wt(l,s.slice(0,-1)),l}var xy=l=>{for(const a in l)if(Bn(l[a]))return!0;return!1};function Eu(l,a={}){const s=Array.isArray(l);if(ht(l)||s)for(const r in l)Array.isArray(l[r])||ht(l[r])&&!xy(l[r])?(a[r]=Array.isArray(l[r])?[]:{},Eu(l[r],a[r])):Gt(l[r])||(a[r]=!0);return a}function Ey(l,a,s){const r=Array.isArray(l);if(ht(l)||r)for(const o in l)Array.isArray(l[o])||ht(l[o])&&!xy(l[o])?bt(a)||Tf(s[o])?s[o]=Array.isArray(l[o])?Eu(l[o],[]):{...Eu(l[o])}:Ey(l[o],Gt(a)?{}:a[o],s[o]):s[o]=!ua(l[o],a[o]);return s}var Ms=(l,a)=>Ey(l,a,Eu(a));const up={value:!1,isValid:!1},op={value:!0,isValid:!0};var wy=l=>{if(Array.isArray(l)){if(l.length>1){const a=l.filter(s=>s&&s.checked&&!s.disabled).map(s=>s.value);return{value:a,isValid:!!a.length}}return l[0].checked&&!l[0].disabled?l[0].attributes&&!bt(l[0].attributes.value)?bt(l[0].value)||l[0].value===""?op:{value:l[0].value,isValid:!0}:op:up}return up},_y=(l,{valueAsNumber:a,valueAsDate:s,setValueAs:r})=>bt(l)?l:a?l===""?NaN:l&&+l:s&&sl(l)?new Date(l):r?r(l):l;const cp={isValid:!1,value:null};var Ay=l=>Array.isArray(l)?l.reduce((a,s)=>s&&s.checked&&!s.disabled?{isValid:!0,value:s.value}:a,cp):cp;function fp(l){const a=l.ref;return Pf(a)?a.files:Wf(a)?Ay(l.refs).value:Sy(a)?[...a.selectedOptions].map(({value:s})=>s):$s(a)?wy(l.refs).value:_y(bt(a.value)?l.ref.value:a.value,l)}var O2=(l,a,s,r)=>{const o={};for(const f of l){const d=de(a,f);d&&Ke(o,f,d._f)}return{criteriaMode:s,names:[...l],fields:o,shouldUseNativeValidation:r}},wu=l=>l instanceof RegExp,Ls=l=>bt(l)?l:wu(l)?l.source:ht(l)?wu(l.value)?l.value.source:l.value:l,dp=l=>({isOnSubmit:!l||l===kn.onSubmit,isOnBlur:l===kn.onBlur,isOnChange:l===kn.onChange,isOnAll:l===kn.all,isOnTouch:l===kn.onTouched});const hp="AsyncFunction";var C2=l=>!!l&&!!l.validate&&!!(Bn(l.validate)&&l.validate.constructor.name===hp||ht(l.validate)&&Object.values(l.validate).find(a=>a.constructor.name===hp)),D2=l=>l.mount&&(l.required||l.min||l.max||l.maxLength||l.minLength||l.pattern||l.validate),mp=(l,a,s)=>!s&&(a.watchAll||a.watch.has(l)||[...a.watch].some(r=>l.startsWith(r)&&/^\.\w+/.test(l.slice(r.length))));const Hs=(l,a,s,r)=>{for(const o of s||Object.keys(l)){const f=de(l,o);if(f){const{_f:d,...h}=f;if(d){if(d.refs&&d.refs[0]&&a(d.refs[0],o)&&!r)return!0;if(d.ref&&a(d.ref,d.name)&&!r)return!0;if(Hs(h,a))break}else if(ht(h)&&Hs(h,a))break}}};function pp(l,a,s){const r=de(l,s);if(r||Jf(s))return{error:r,name:s};const o=s.split(".");for(;o.length;){const f=o.join("."),d=de(a,f),h=de(l,f);if(d&&!Array.isArray(d)&&s!==f)return{name:s};if(h&&h.type)return{name:f,error:h};o.pop()}return{name:s}}var N2=(l,a,s,r)=>{s(l);const{name:o,...f}=l;return Yt(f)||Object.keys(f).length>=Object.keys(a).length||Object.keys(f).find(d=>a[d]===(!r||kn.all))},j2=(l,a,s)=>!l||!a||l===a||Bs(l).some(r=>r&&(s?r===a:r.startsWith(a)||a.startsWith(r))),U2=(l,a,s,r,o)=>o.isOnAll?!1:!s&&o.isOnTouch?!(a||l):(s?r.isOnBlur:o.isOnBlur)?!l:(s?r.isOnChange:o.isOnChange)?l:!0,M2=(l,a)=>!Lu(de(l,a)).length&&wt(l,a),L2=(l,a,s)=>{const r=Bs(de(l,s));return Ke(r,"root",a[s]),Ke(l,s,r),l},mu=l=>sl(l);function yp(l,a,s="validate"){if(mu(l)||Array.isArray(l)&&l.every(mu)||al(l)&&!l)return{type:s,message:mu(l)?l:"",ref:a}}var Ei=l=>ht(l)&&!wu(l)?l:{value:l,message:""},gp=async(l,a,s,r,o,f)=>{const{ref:d,refs:h,required:p,maxLength:m,minLength:g,min:S,max:x,pattern:R,validate:_,name:N,valueAsNumber:O,mount:E}=l._f,D=de(s,N);if(!E||a.has(N))return{};const U=h?h[0]:d,G=ae=>{o&&U.reportValidity&&(U.setCustomValidity(al(ae)?"":ae||""),U.reportValidity())},k={},te=Wf(d),W=$s(d),K=te||W,ne=(O||Pf(d))&&bt(d.value)&&bt(D)||xu(d)&&d.value===""||D===""||Array.isArray(D)&&!D.length,xe=by.bind(null,N,r,k),oe=(ae,pe,he,we=Ul.maxLength,L=Ul.minLength)=>{const X=ae?pe:he;k[N]={type:ae?we:L,message:X,ref:d,...xe(ae?we:L,X)}};if(f?!Array.isArray(D)||!D.length:p&&(!K&&(ne||Gt(D))||al(D)&&!D||W&&!wy(h).isValid||te&&!Ay(h).isValid)){const{value:ae,message:pe}=mu(p)?{value:!!p,message:p}:Ei(p);if(ae&&(k[N]={type:Ul.required,message:pe,ref:U,...xe(Ul.required,pe)},!r))return G(pe),k}if(!ne&&(!Gt(S)||!Gt(x))){let ae,pe;const he=Ei(x),we=Ei(S);if(!Gt(D)&&!isNaN(D)){const L=d.valueAsNumber||D&&+D;Gt(he.value)||(ae=L>he.value),Gt(we.value)||(pe=L<we.value)}else{const L=d.valueAsDate||new Date(D),X=T=>new Date(new Date().toDateString()+" "+T),I=d.type=="time",me=d.type=="week";sl(he.value)&&D&&(ae=I?X(D)>X(he.value):me?D>he.value:L>new Date(he.value)),sl(we.value)&&D&&(pe=I?X(D)<X(we.value):me?D<we.value:L<new Date(we.value))}if((ae||pe)&&(oe(!!ae,he.message,we.message,Ul.max,Ul.min),!r))return G(k[N].message),k}if((m||g)&&!ne&&(sl(D)||f&&Array.isArray(D))){const ae=Ei(m),pe=Ei(g),he=!Gt(ae.value)&&D.length>+ae.value,we=!Gt(pe.value)&&D.length<+pe.value;if((he||we)&&(oe(he,ae.message,pe.message),!r))return G(k[N].message),k}if(R&&!ne&&sl(D)){const{value:ae,message:pe}=Ei(R);if(wu(ae)&&!D.match(ae)&&(k[N]={type:Ul.pattern,message:pe,ref:d,...xe(Ul.pattern,pe)},!r))return G(pe),k}if(_){if(Bn(_)){const ae=await _(D,s),pe=yp(ae,U);if(pe&&(k[N]={...pe,...xe(Ul.validate,pe.message)},!r))return G(pe.message),k}else if(ht(_)){let ae={};for(const pe in _){if(!Yt(ae)&&!r)break;const he=yp(await _[pe](D,s),U,pe);he&&(ae={...he,...xe(pe,he.message)},G(he.message),r&&(k[N]=ae))}if(!Yt(ae)&&(k[N]={ref:U,...ae},!r))return k}}return G(!0),k};const z2={mode:kn.onSubmit,reValidateMode:kn.onChange,shouldFocusError:!0};function k2(l={}){let a={...z2,...l},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:Bn(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1};const r={};let o=ht(a.defaultValues)||ht(a.values)?Bt(a.values||a.defaultValues)||{}:{},f=a.shouldUnregister?{}:Bt(o),d={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},p,m=0;const g={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...g};const x={array:rp(),state:rp()},R=dp(a.mode),_=dp(a.reValidateMode),N=a.criteriaMode===kn.all,O=C=>H=>{clearTimeout(m),m=setTimeout(C,H)},E=async C=>{if(!a.disabled&&(g.isValid||S.isValid||C)){const H=a.resolver?Yt((await ne()).errors):await oe(r,!0);H!==s.isValid&&x.state.next({isValid:H})}},D=(C,H)=>{!a.disabled&&(g.isValidating||g.validatingFields||S.isValidating||S.validatingFields)&&((C||Array.from(h.mount)).forEach($=>{$&&(H?Ke(s.validatingFields,$,H):wt(s.validatingFields,$))}),x.state.next({validatingFields:s.validatingFields,isValidating:!Yt(s.validatingFields)}))},U=(C,H=[],$,ue,ee=!0,P=!0)=>{if(ue&&$&&!a.disabled){if(d.action=!0,P&&Array.isArray(de(r,C))){const ie=$(de(r,C),ue.argA,ue.argB);ee&&Ke(r,C,ie)}if(P&&Array.isArray(de(s.errors,C))){const ie=$(de(s.errors,C),ue.argA,ue.argB);ee&&Ke(s.errors,C,ie),M2(s.errors,C)}if((g.touchedFields||S.touchedFields)&&P&&Array.isArray(de(s.touchedFields,C))){const ie=$(de(s.touchedFields,C),ue.argA,ue.argB);ee&&Ke(s.touchedFields,C,ie)}(g.dirtyFields||S.dirtyFields)&&(s.dirtyFields=Ms(o,f)),x.state.next({name:C,isDirty:pe(C,H),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else Ke(f,C,H)},G=(C,H)=>{Ke(s.errors,C,H),x.state.next({errors:s.errors})},k=C=>{s.errors=C,x.state.next({errors:s.errors,isValid:!1})},te=(C,H,$,ue)=>{const ee=de(r,C);if(ee){const P=de(f,C,bt($)?de(o,C):$);bt(P)||ue&&ue.defaultChecked||H?Ke(f,C,H?P:fp(ee._f)):L(C,P),d.mount&&E()}},W=(C,H,$,ue,ee)=>{let P=!1,ie=!1;const be={name:C};if(!a.disabled){if(!$||ue){(g.isDirty||S.isDirty)&&(ie=s.isDirty,s.isDirty=be.isDirty=pe(),P=ie!==be.isDirty);const We=ua(de(o,C),H);ie=!!de(s.dirtyFields,C),We?wt(s.dirtyFields,C):Ke(s.dirtyFields,C,!0),be.dirtyFields=s.dirtyFields,P=P||(g.dirtyFields||S.dirtyFields)&&ie!==!We}if($){const We=de(s.touchedFields,C);We||(Ke(s.touchedFields,C,$),be.touchedFields=s.touchedFields,P=P||(g.touchedFields||S.touchedFields)&&We!==$)}P&&ee&&x.state.next(be)}return P?be:{}},K=(C,H,$,ue)=>{const ee=de(s.errors,C),P=(g.isValid||S.isValid)&&al(H)&&s.isValid!==H;if(a.delayError&&$?(p=O(()=>G(C,$)),p(a.delayError)):(clearTimeout(m),p=null,$?Ke(s.errors,C,$):wt(s.errors,C)),($?!ua(ee,$):ee)||!Yt(ue)||P){const ie={...ue,...P&&al(H)?{isValid:H}:{},errors:s.errors,name:C};s={...s,...ie},x.state.next(ie)}},ne=async C=>{D(C,!0);const H=await a.resolver(f,a.context,O2(C||h.mount,r,a.criteriaMode,a.shouldUseNativeValidation));return D(C),H},xe=async C=>{const{errors:H}=await ne(C);if(C)for(const $ of C){const ue=de(H,$);ue?Ke(s.errors,$,ue):wt(s.errors,$)}else s.errors=H;return H},oe=async(C,H,$={valid:!0})=>{for(const ue in C){const ee=C[ue];if(ee){const{_f:P,...ie}=ee;if(P){const be=h.array.has(P.name),We=ee._f&&C2(ee._f);We&&g.validatingFields&&D([ue],!0);const tt=await gp(ee,h.disabled,f,N,a.shouldUseNativeValidation&&!H,be);if(We&&g.validatingFields&&D([ue]),tt[P.name]&&($.valid=!1,H))break;!H&&(de(tt,P.name)?be?L2(s.errors,tt,P.name):Ke(s.errors,P.name,tt[P.name]):wt(s.errors,P.name))}!Yt(ie)&&await oe(ie,H,$)}}return $.valid},ae=()=>{for(const C of h.unMount){const H=de(r,C);H&&(H._f.refs?H._f.refs.every($=>!ff($)):!ff(H._f.ref))&&St(C)}h.unMount=new Set},pe=(C,H)=>!a.disabled&&(C&&H&&Ke(f,C,H),!ua(re(),o)),he=(C,H,$)=>_2(C,h,{...d.mount?f:bt(H)?o:sl(C)?{[C]:H}:H},$,H),we=C=>Lu(de(d.mount?f:o,C,a.shouldUnregister?de(o,C,[]):[])),L=(C,H,$={})=>{const ue=de(r,C);let ee=H;if(ue){const P=ue._f;P&&(!P.disabled&&Ke(f,C,_y(H,P)),ee=xu(P.ref)&&Gt(H)?"":H,Sy(P.ref)?[...P.ref.options].forEach(ie=>ie.selected=ee.includes(ie.value)):P.refs?$s(P.ref)?P.refs.length>1?P.refs.forEach(ie=>(!ie.defaultChecked||!ie.disabled)&&(ie.checked=Array.isArray(ee)?!!ee.find(be=>be===ie.value):ee===ie.value)):P.refs[0]&&(P.refs[0].checked=!!ee):P.refs.forEach(ie=>ie.checked=ie.value===ee):Pf(P.ref)?P.ref.value="":(P.ref.value=ee,P.ref.type||x.state.next({name:C,values:Bt(f)})))}($.shouldDirty||$.shouldTouch)&&W(C,ee,$.shouldTouch,$.shouldDirty,!0),$.shouldValidate&&Y(C)},X=(C,H,$)=>{for(const ue in H){const ee=H[ue],P=`${C}.${ue}`,ie=de(r,P);(h.array.has(C)||ht(ee)||ie&&!ie._f)&&!Da(ee)?X(P,ee,$):L(P,ee,$)}},I=(C,H,$={})=>{const ue=de(r,C),ee=h.array.has(C),P=Bt(H);Ke(f,C,P),ee?(x.array.next({name:C,values:Bt(f)}),(g.isDirty||g.dirtyFields||S.isDirty||S.dirtyFields)&&$.shouldDirty&&x.state.next({name:C,dirtyFields:Ms(o,f),isDirty:pe(C,P)})):ue&&!ue._f&&!Gt(P)?X(C,P,$):L(C,P,$),mp(C,h)&&x.state.next({...s}),x.state.next({name:d.mount?C:void 0,values:Bt(f)})},me=async C=>{d.mount=!0;const H=C.target;let $=H.name,ue=!0;const ee=de(r,$),P=ie=>{ue=Number.isNaN(ie)||Da(ie)&&isNaN(ie.getTime())||ua(ie,de(f,$,ie))};if(ee){let ie,be;const We=H.type?fp(ee._f):b2(C),tt=C.type===sp.BLUR||C.type===sp.FOCUS_OUT,Nn=!D2(ee._f)&&!a.resolver&&!de(s.errors,$)&&!ee._f.deps||U2(tt,de(s.touchedFields,$),s.isSubmitted,_,R),pt=mp($,h,tt);Ke(f,$,We),tt?(ee._f.onBlur&&ee._f.onBlur(C),p&&p(0)):ee._f.onChange&&ee._f.onChange(C);const Ve=W($,We,tt),Xt=!Yt(Ve)||pt;if(!tt&&x.state.next({name:$,type:C.type,values:Bt(f)}),Nn)return(g.isValid||S.isValid)&&(a.mode==="onBlur"?tt&&E():tt||E()),Xt&&x.state.next({name:$,...pt?{}:Ve});if(!tt&&pt&&x.state.next({...s}),a.resolver){const{errors:an}=await ne([$]);if(P(We),ue){const Zt=pp(s.errors,r,$),Qn=pp(an,r,Zt.name||$);ie=Qn.error,$=Qn.name,be=Yt(an)}}else D([$],!0),ie=(await gp(ee,h.disabled,f,N,a.shouldUseNativeValidation))[$],D([$]),P(We),ue&&(ie?be=!1:(g.isValid||S.isValid)&&(be=await oe(r,!0)));ue&&(ee._f.deps&&Y(ee._f.deps),K($,be,ie,Ve))}},T=(C,H)=>{if(de(s.errors,H)&&C.focus)return C.focus(),1},Y=async(C,H={})=>{let $,ue;const ee=Bs(C);if(a.resolver){const P=await xe(bt(C)?C:ee);$=Yt(P),ue=C?!ee.some(ie=>de(P,ie)):$}else C?(ue=(await Promise.all(ee.map(async P=>{const ie=de(r,P);return await oe(ie&&ie._f?{[P]:ie}:ie)}))).every(Boolean),!(!ue&&!s.isValid)&&E()):ue=$=await oe(r);return x.state.next({...!sl(C)||(g.isValid||S.isValid)&&$!==s.isValid?{}:{name:C},...a.resolver||!C?{isValid:$}:{},errors:s.errors}),H.shouldFocus&&!ue&&Hs(r,T,C?ee:h.mount),ue},re=C=>{const H={...d.mount?f:o};return bt(C)?H:sl(C)?de(H,C):C.map($=>de(H,$))},le=(C,H)=>({invalid:!!de((H||s).errors,C),isDirty:!!de((H||s).dirtyFields,C),error:de((H||s).errors,C),isValidating:!!de(s.validatingFields,C),isTouched:!!de((H||s).touchedFields,C)}),ye=C=>{C&&Bs(C).forEach(H=>wt(s.errors,H)),x.state.next({errors:C?s.errors:{}})},De=(C,H,$)=>{const ue=(de(r,C,{_f:{}})._f||{}).ref,ee=de(s.errors,C)||{},{ref:P,message:ie,type:be,...We}=ee;Ke(s.errors,C,{...We,...H,ref:ue}),x.state.next({name:C,errors:s.errors,isValid:!1}),$&&$.shouldFocus&&ue&&ue.focus&&ue.focus()},_e=(C,H)=>Bn(C)?x.state.subscribe({next:$=>C(he(void 0,H),$)}):he(C,H,!0),it=C=>x.state.subscribe({next:H=>{j2(C.name,H.name,C.exact)&&N2(H,C.formState||g,Ge,C.reRenderRoot)&&C.callback({values:{...f},...s,...H})}}).unsubscribe,Re=C=>(d.mount=!0,S={...S,...C.formState},it({...C,formState:S})),St=(C,H={})=>{for(const $ of C?Bs(C):h.mount)h.mount.delete($),h.array.delete($),H.keepValue||(wt(r,$),wt(f,$)),!H.keepError&&wt(s.errors,$),!H.keepDirty&&wt(s.dirtyFields,$),!H.keepTouched&&wt(s.touchedFields,$),!H.keepIsValidating&&wt(s.validatingFields,$),!a.shouldUnregister&&!H.keepDefaultValue&&wt(o,$);x.state.next({values:Bt(f)}),x.state.next({...s,...H.keepDirty?{isDirty:pe()}:{}}),!H.keepIsValid&&E()},_t=({disabled:C,name:H})=>{(al(C)&&d.mount||C||h.disabled.has(H))&&(C?h.disabled.add(H):h.disabled.delete(H))},Dt=(C,H={})=>{let $=de(r,C);const ue=al(H.disabled)||al(a.disabled);return Ke(r,C,{...$||{},_f:{...$&&$._f?$._f:{ref:{name:C}},name:C,mount:!0,...H}}),h.mount.add(C),$?_t({disabled:al(H.disabled)?H.disabled:a.disabled,name:C}):te(C,!0,H.value),{...ue?{disabled:H.disabled||a.disabled}:{},...a.progressive?{required:!!H.required,min:Ls(H.min),max:Ls(H.max),minLength:Ls(H.minLength),maxLength:Ls(H.maxLength),pattern:Ls(H.pattern)}:{},name:C,onChange:me,onBlur:me,ref:ee=>{if(ee){Dt(C,H),$=de(r,C);const P=bt(ee.value)&&ee.querySelectorAll&&ee.querySelectorAll("input,select,textarea")[0]||ee,ie=A2(P),be=$._f.refs||[];if(ie?be.find(We=>We===P):P===$._f.ref)return;Ke(r,C,{_f:{...$._f,...ie?{refs:[...be.filter(ff),P,...Array.isArray(de(o,C))?[{}]:[]],ref:{type:P.type,name:C}}:{ref:P}}}),te(C,!1,void 0,P)}else $=de(r,C,{}),$._f&&($._f.mount=!1),(a.shouldUnregister||H.shouldUnregister)&&!(x2(h.array,C)&&d.action)&&h.unMount.add(C)}}},pn=()=>a.shouldFocusError&&Hs(r,T,h.mount),$t=C=>{al(C)&&(x.state.next({disabled:C}),Hs(r,(H,$)=>{const ue=de(r,$);ue&&(H.disabled=ue._f.disabled||C,Array.isArray(ue._f.refs)&&ue._f.refs.forEach(ee=>{ee.disabled=ue._f.disabled||C}))},0,!1))},Zn=(C,H)=>async $=>{let ue;$&&($.preventDefault&&$.preventDefault(),$.persist&&$.persist());let ee=Bt(f);if(x.state.next({isSubmitting:!0}),a.resolver){const{errors:P,values:ie}=await ne();s.errors=P,ee=ie}else await oe(r);if(h.disabled.size)for(const P of h.disabled)Ke(ee,P,void 0);if(wt(s.errors,"root"),Yt(s.errors)){x.state.next({errors:{}});try{await C(ee,$)}catch(P){ue=P}}else H&&await H({...s.errors},$),pn(),setTimeout(pn);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Yt(s.errors)&&!ue,submitCount:s.submitCount+1,errors:s.errors}),ue)throw ue},Nt=(C,H={})=>{de(r,C)&&(bt(H.defaultValue)?I(C,Bt(de(o,C))):(I(C,H.defaultValue),Ke(o,C,Bt(H.defaultValue))),H.keepTouched||wt(s.touchedFields,C),H.keepDirty||(wt(s.dirtyFields,C),s.isDirty=H.defaultValue?pe(C,Bt(de(o,C))):pe()),H.keepError||(wt(s.errors,C),g.isValid&&E()),x.state.next({...s}))},dl=(C,H={})=>{const $=C?Bt(C):o,ue=Bt($),ee=Yt(C),P=ee?o:ue;if(H.keepDefaultValues||(o=$),!H.keepValues){if(H.keepDirtyValues){const ie=new Set([...h.mount,...Object.keys(Ms(o,f))]);for(const be of Array.from(ie))de(s.dirtyFields,be)?Ke(P,be,de(f,be)):I(be,de(P,be))}else{if(Kf&&bt(C))for(const ie of h.mount){const be=de(r,ie);if(be&&be._f){const We=Array.isArray(be._f.refs)?be._f.refs[0]:be._f.ref;if(xu(We)){const tt=We.closest("form");if(tt){tt.reset();break}}}}for(const ie of h.mount)I(ie,de(P,ie))}f=Bt(P),x.array.next({values:{...P}}),x.state.next({values:{...P}})}h={mount:H.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!g.isValid||!!H.keepIsValid||!!H.keepDirtyValues,d.watch=!!a.shouldUnregister,x.state.next({submitCount:H.keepSubmitCount?s.submitCount:0,isDirty:ee?!1:H.keepDirty?s.isDirty:!!(H.keepDefaultValues&&!ua(C,o)),isSubmitted:H.keepIsSubmitted?s.isSubmitted:!1,dirtyFields:ee?{}:H.keepDirtyValues?H.keepDefaultValues&&f?Ms(o,f):s.dirtyFields:H.keepDefaultValues&&C?Ms(o,C):H.keepDirty?s.dirtyFields:{},touchedFields:H.keepTouched?s.touchedFields:{},errors:H.keepErrors?s.errors:{},isSubmitSuccessful:H.keepIsSubmitSuccessful?s.isSubmitSuccessful:!1,isSubmitting:!1})},ut=(C,H)=>dl(Bn(C)?C(f):C,H),Dn=(C,H={})=>{const $=de(r,C),ue=$&&$._f;if(ue){const ee=ue.refs?ue.refs[0]:ue.ref;ee.focus&&(ee.focus(),H.shouldSelect&&Bn(ee.select)&&ee.select())}},Ge=C=>{s={...s,...C}},ln={control:{register:Dt,unregister:St,getFieldState:le,handleSubmit:Zn,setError:De,_subscribe:it,_runSchema:ne,_getWatch:he,_getDirty:pe,_setValid:E,_setFieldArray:U,_setDisabledField:_t,_setErrors:k,_getFieldArray:we,_reset:dl,_resetDefaultValues:()=>Bn(a.defaultValues)&&a.defaultValues().then(C=>{ut(C,a.resetOptions),x.state.next({isLoading:!1})}),_removeUnmounted:ae,_disableForm:$t,_subjects:x,_proxyFormState:g,get _fields(){return r},get _formValues(){return f},get _state(){return d},set _state(C){d=C},get _defaultValues(){return o},get _names(){return h},set _names(C){h=C},get _formState(){return s},get _options(){return a},set _options(C){a={...a,...C}}},subscribe:Re,trigger:Y,register:Dt,handleSubmit:Zn,watch:_e,setValue:I,getValues:re,reset:ut,resetField:Nt,clearErrors:ye,unregister:St,setError:De,setFocus:Dn,getFieldState:le};return{...ln,formControl:ln}}const B2=typeof window<"u"?Pe.useLayoutEffect:Pe.useEffect;function Xs(l={}){const a=Pe.useRef(void 0),s=Pe.useRef(void 0),[r,o]=Pe.useState({isDirty:!1,isValidating:!1,isLoading:Bn(l.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1,isReady:!1,defaultValues:Bn(l.defaultValues)?void 0:l.defaultValues});a.current||(a.current={...l.formControl?l.formControl:k2(l),formState:r},l.formControl&&l.defaultValues&&!Bn(l.defaultValues)&&l.formControl.reset(l.defaultValues,l.resetOptions));const f=a.current.control;return f._options=l,B2(()=>{const d=f._subscribe({formState:f._proxyFormState,callback:()=>o({...f._formState}),reRenderRoot:!0});return o(h=>({...h,isReady:!0})),f._formState.isReady=!0,d},[f]),Pe.useEffect(()=>f._disableForm(l.disabled),[f,l.disabled]),Pe.useEffect(()=>{l.mode&&(f._options.mode=l.mode),l.reValidateMode&&(f._options.reValidateMode=l.reValidateMode),l.errors&&!Yt(l.errors)&&f._setErrors(l.errors)},[f,l.errors,l.mode,l.reValidateMode]),Pe.useEffect(()=>{l.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,l.shouldUnregister]),Pe.useEffect(()=>{if(f._proxyFormState.isDirty){const d=f._getDirty();d!==r.isDirty&&f._subjects.state.next({isDirty:d})}},[f,r.isDirty]),Pe.useEffect(()=>{l.values&&!ua(l.values,s.current)?(f._reset(l.values,f._options.resetOptions),s.current=l.values,o(d=>({...d}))):f._resetDefaultValues()},[f,l.values]),Pe.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),a.current.formState=w2(r,f),a.current}var df,vp;function H2(){if(vp)return df;vp=1;function l(E){this._maxSize=E,this.clear()}l.prototype.clear=function(){this._size=0,this._values=Object.create(null)},l.prototype.get=function(E){return this._values[E]},l.prototype.set=function(E,D){return this._size>=this._maxSize&&this.clear(),E in this._values||this._size++,this._values[E]=D};var a=/[^.^\]^[]+|(?=\[\]|\.\.)/g,s=/^\d+$/,r=/^\d/,o=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,f=/^\s*(['"]?)(.*?)(\1)\s*$/,d=512,h=new l(d),p=new l(d),m=new l(d);df={Cache:l,split:S,normalizePath:g,setter:function(E){var D=g(E);return p.get(E)||p.set(E,function(G,k){for(var te=0,W=D.length,K=G;te<W-1;){var ne=D[te];if(ne==="__proto__"||ne==="constructor"||ne==="prototype")return G;K=K[D[te++]]}K[D[te]]=k})},getter:function(E,D){var U=g(E);return m.get(E)||m.set(E,function(k){for(var te=0,W=U.length;te<W;)if(k!=null||!D)k=k[U[te++]];else return;return k})},join:function(E){return E.reduce(function(D,U){return D+(R(U)||s.test(U)?"["+U+"]":(D?".":"")+U)},"")},forEach:function(E,D,U){x(Array.isArray(E)?E:S(E),D,U)}};function g(E){return h.get(E)||h.set(E,S(E).map(function(D){return D.replace(f,"$2")}))}function S(E){return E.match(a)||[""]}function x(E,D,U){var G=E.length,k,te,W,K;for(te=0;te<G;te++)k=E[te],k&&(O(k)&&(k='"'+k+'"'),K=R(k),W=!K&&/^\d+$/.test(k),D.call(U,k,K,W,te,E))}function R(E){return typeof E=="string"&&E&&["'",'"'].indexOf(E.charAt(0))!==-1}function _(E){return E.match(r)&&!E.match(s)}function N(E){return o.test(E)}function O(E){return!R(E)&&(_(E)||N(E))}return df}var Ua=H2(),hf,bp;function q2(){if(bp)return hf;bp=1;const l=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,a=g=>g.match(l)||[],s=g=>g[0].toUpperCase()+g.slice(1),r=(g,S)=>a(g).join(S).toLowerCase(),o=g=>a(g).reduce((S,x)=>`${S}${S?x[0].toUpperCase()+x.slice(1).toLowerCase():x.toLowerCase()}`,"");return hf={words:a,upperFirst:s,camelCase:o,pascalCase:g=>s(o(g)),snakeCase:g=>r(g,"_"),kebabCase:g=>r(g,"-"),sentenceCase:g=>s(r(g," ")),titleCase:g=>a(g).map(s).join(" ")},hf}var mf=q2(),ou={exports:{}},Sp;function V2(){if(Sp)return ou.exports;Sp=1,ou.exports=function(o){return l(a(o),o)},ou.exports.array=l;function l(o,f){var d=o.length,h=new Array(d),p={},m=d,g=s(f),S=r(o);for(f.forEach(function(R){if(!S.has(R[0])||!S.has(R[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});m--;)p[m]||x(o[m],m,new Set);return h;function x(R,_,N){if(N.has(R)){var O;try{O=", node was:"+JSON.stringify(R)}catch{O=""}throw new Error("Cyclic dependency"+O)}if(!S.has(R))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(R));if(!p[_]){p[_]=!0;var E=g.get(R)||new Set;if(E=Array.from(E),_=E.length){N.add(R);do{var D=E[--_];x(D,S.get(D),N)}while(_);N.delete(R)}h[--d]=R}}}function a(o){for(var f=new Set,d=0,h=o.length;d<h;d++){var p=o[d];f.add(p[0]),f.add(p[1])}return Array.from(f)}function s(o){for(var f=new Map,d=0,h=o.length;d<h;d++){var p=o[d];f.has(p[0])||f.set(p[0],new Set),f.has(p[1])||f.set(p[1],new Set),f.get(p[0]).add(p[1])}return f}function r(o){for(var f=new Map,d=0,h=o.length;d<h;d++)f.set(o[d],d);return f}return ou.exports}var F2=V2();const Y2=qf(F2),G2=Object.prototype.toString,$2=Error.prototype.toString,X2=RegExp.prototype.toString,Z2=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",Q2=/^Symbol\((.*)\)(.*)$/;function K2(l){return l!=+l?"NaN":l===0&&1/l<0?"-0":""+l}function xp(l,a=!1){if(l==null||l===!0||l===!1)return""+l;const s=typeof l;if(s==="number")return K2(l);if(s==="string")return a?`"${l}"`:l;if(s==="function")return"[Function "+(l.name||"anonymous")+"]";if(s==="symbol")return Z2.call(l).replace(Q2,"Symbol($1)");const r=G2.call(l).slice(8,-1);return r==="Date"?isNaN(l.getTime())?""+l:l.toISOString(l):r==="Error"||l instanceof Error?"["+$2.call(l)+"]":r==="RegExp"?X2.call(l):null}function oa(l,a){let s=xp(l,a);return s!==null?s:JSON.stringify(l,function(r,o){let f=xp(this[r],a);return f!==null?f:o},2)}function Ty(l){return l==null?[]:[].concat(l)}let Ry,Oy,Cy,J2=/\$\{\s*(\w+)\s*\}/g;Ry=Symbol.toStringTag;class Ep{constructor(a,s,r,o){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[Ry]="Error",this.name="ValidationError",this.value=s,this.path=r,this.type=o,this.errors=[],this.inner=[],Ty(a).forEach(f=>{if(It.isError(f)){this.errors.push(...f.errors);const d=f.inner.length?f.inner:[f];this.inner.push(...d)}else this.errors.push(f)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}Oy=Symbol.hasInstance;Cy=Symbol.toStringTag;class It extends Error{static formatError(a,s){const r=s.label||s.path||"this";return s=Object.assign({},s,{path:r,originalPath:s.path}),typeof a=="string"?a.replace(J2,(o,f)=>oa(s[f])):typeof a=="function"?a(s):a}static isError(a){return a&&a.name==="ValidationError"}constructor(a,s,r,o,f){const d=new Ep(a,s,r,o);if(f)return d;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[Cy]="Error",this.name=d.name,this.message=d.message,this.type=d.type,this.value=d.value,this.path=d.path,this.errors=d.errors,this.inner=d.inner,Error.captureStackTrace&&Error.captureStackTrace(this,It)}static[Oy](a){return Ep[Symbol.hasInstance](a)||super[Symbol.hasInstance](a)}}let il={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:l,type:a,value:s,originalValue:r})=>{const o=r!=null&&r!==s?` (cast from the value \`${oa(r,!0)}\`).`:".";return a!=="mixed"?`${l} must be a \`${a}\` type, but the final value was: \`${oa(s,!0)}\``+o:`${l} must match the configured type. The validated value was: \`${oa(s,!0)}\``+o}},Wt={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},P2={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},Rf={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},W2={isValue:"${path} field must be ${value}"},pu={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},I2={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},eS={notType:l=>{const{path:a,value:s,spec:r}=l,o=r.types.length;if(Array.isArray(s)){if(s.length<o)return`${a} tuple value has too few items, expected a length of ${o} but got ${s.length} for value: \`${oa(s,!0)}\``;if(s.length>o)return`${a} tuple value has too many items, expected a length of ${o} but got ${s.length} for value: \`${oa(s,!0)}\``}return It.formatError(il.notType,l)}};Object.assign(Object.create(null),{mixed:il,string:Wt,number:P2,date:Rf,object:pu,array:I2,boolean:W2,tuple:eS});const If=l=>l&&l.__isYupSchema__;class _u{static fromOptions(a,s){if(!s.then&&!s.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:r,then:o,otherwise:f}=s,d=typeof r=="function"?r:(...h)=>h.every(p=>p===r);return new _u(a,(h,p)=>{var m;let g=d(...h)?o:f;return(m=g==null?void 0:g(p))!=null?m:p})}constructor(a,s){this.fn=void 0,this.refs=a,this.refs=a,this.fn=s}resolve(a,s){let r=this.refs.map(f=>f.getValue(s==null?void 0:s.value,s==null?void 0:s.parent,s==null?void 0:s.context)),o=this.fn(r,a,s);if(o===void 0||o===a)return a;if(!If(o))throw new TypeError("conditions must return a schema object");return o.resolve(s)}}const cu={context:"$",value:"."};function Dy(l,a){return new da(l,a)}class da{constructor(a,s={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof a!="string")throw new TypeError("ref must be a string, got: "+a);if(this.key=a.trim(),a==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===cu.context,this.isValue=this.key[0]===cu.value,this.isSibling=!this.isContext&&!this.isValue;let r=this.isContext?cu.context:this.isValue?cu.value:"";this.path=this.key.slice(r.length),this.getter=this.path&&Ua.getter(this.path,!0),this.map=s.map}getValue(a,s,r){let o=this.isContext?r:this.isValue?a:s;return this.getter&&(o=this.getter(o||{})),this.map&&(o=this.map(o)),o}cast(a,s){return this.getValue(a,s==null?void 0:s.parent,s==null?void 0:s.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(a){return a&&a.__isYupRef}}da.prototype.__isYupRef=!0;const Na=l=>l==null;function wi(l){function a({value:s,path:r="",options:o,originalValue:f,schema:d},h,p){const{name:m,test:g,params:S,message:x,skipAbsent:R}=l;let{parent:_,context:N,abortEarly:O=d.spec.abortEarly,disableStackTrace:E=d.spec.disableStackTrace}=o;function D(oe){return da.isRef(oe)?oe.getValue(s,_,N):oe}function U(oe={}){const ae=Object.assign({value:s,originalValue:f,label:d.spec.label,path:oe.path||r,spec:d.spec,disableStackTrace:oe.disableStackTrace||E},S,oe.params);for(const he of Object.keys(ae))ae[he]=D(ae[he]);const pe=new It(It.formatError(oe.message||x,ae),s,ae.path,oe.type||m,ae.disableStackTrace);return pe.params=ae,pe}const G=O?h:p;let k={path:r,parent:_,type:m,from:o.from,createError:U,resolve:D,options:o,originalValue:f,schema:d};const te=oe=>{It.isError(oe)?G(oe):oe?p(null):G(U())},W=oe=>{It.isError(oe)?G(oe):h(oe)};if(R&&Na(s))return te(!0);let ne;try{var xe;if(ne=g.call(k,s,k),typeof((xe=ne)==null?void 0:xe.then)=="function"){if(o.sync)throw new Error(`Validation test of type: "${k.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(ne).then(te,W)}}catch(oe){W(oe);return}te(ne)}return a.OPTIONS=l,a}function tS(l,a,s,r=s){let o,f,d;return a?(Ua.forEach(a,(h,p,m)=>{let g=p?h.slice(1,h.length-1):h;l=l.resolve({context:r,parent:o,value:s});let S=l.type==="tuple",x=m?parseInt(g,10):0;if(l.innerType||S){if(S&&!m)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${d}" must contain an index to the tuple element, e.g. "${d}[0]"`);if(s&&x>=s.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${h}, in the path: ${a}. because there is no value at that index. `);o=s,s=s&&s[x],l=S?l.spec.types[x]:l.innerType}if(!m){if(!l.fields||!l.fields[g])throw new Error(`The schema does not contain the path: ${a}. (failed at: ${d} which is a type: "${l.type}")`);o=s,s=s&&s[g],l=l.fields[g]}f=g,d=p?"["+h+"]":"."+h}),{schema:l,parent:o,parentPath:f}):{parent:o,parentPath:a,schema:l}}class Au extends Set{describe(){const a=[];for(const s of this.values())a.push(da.isRef(s)?s.describe():s);return a}resolveAll(a){let s=[];for(const r of this.values())s.push(a(r));return s}clone(){return new Au(this.values())}merge(a,s){const r=this.clone();return a.forEach(o=>r.add(o)),s.forEach(o=>r.delete(o)),r}}function Ai(l,a=new Map){if(If(l)||!l||typeof l!="object")return l;if(a.has(l))return a.get(l);let s;if(l instanceof Date)s=new Date(l.getTime()),a.set(l,s);else if(l instanceof RegExp)s=new RegExp(l),a.set(l,s);else if(Array.isArray(l)){s=new Array(l.length),a.set(l,s);for(let r=0;r<l.length;r++)s[r]=Ai(l[r],a)}else if(l instanceof Map){s=new Map,a.set(l,s);for(const[r,o]of l.entries())s.set(r,Ai(o,a))}else if(l instanceof Set){s=new Set,a.set(l,s);for(const r of l)s.add(Ai(r,a))}else if(l instanceof Object){s={},a.set(l,s);for(const[r,o]of Object.entries(l))s[r]=Ai(o,a)}else throw Error(`Unable to clone ${l}`);return s}class ul{constructor(a){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new Au,this._blacklist=new Au,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(il.notType)}),this.type=a.type,this._typeCheck=a.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},a==null?void 0:a.spec),this.withMutation(s=>{s.nonNullable()})}get _type(){return this.type}clone(a){if(this._mutate)return a&&Object.assign(this.spec,a),this;const s=Object.create(Object.getPrototypeOf(this));return s.type=this.type,s._typeCheck=this._typeCheck,s._whitelist=this._whitelist.clone(),s._blacklist=this._blacklist.clone(),s.internalTests=Object.assign({},this.internalTests),s.exclusiveTests=Object.assign({},this.exclusiveTests),s.deps=[...this.deps],s.conditions=[...this.conditions],s.tests=[...this.tests],s.transforms=[...this.transforms],s.spec=Ai(Object.assign({},this.spec,a)),s}label(a){let s=this.clone();return s.spec.label=a,s}meta(...a){if(a.length===0)return this.spec.meta;let s=this.clone();return s.spec.meta=Object.assign(s.spec.meta||{},a[0]),s}withMutation(a){let s=this._mutate;this._mutate=!0;let r=a(this);return this._mutate=s,r}concat(a){if(!a||a===this)return this;if(a.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${a.type}`);let s=this,r=a.clone();const o=Object.assign({},s.spec,r.spec);return r.spec=o,r.internalTests=Object.assign({},s.internalTests,r.internalTests),r._whitelist=s._whitelist.merge(a._whitelist,a._blacklist),r._blacklist=s._blacklist.merge(a._blacklist,a._whitelist),r.tests=s.tests,r.exclusiveTests=s.exclusiveTests,r.withMutation(f=>{a.tests.forEach(d=>{f.test(d.OPTIONS)})}),r.transforms=[...s.transforms,...r.transforms],r}isType(a){return a==null?!!(this.spec.nullable&&a===null||this.spec.optional&&a===void 0):this._typeCheck(a)}resolve(a){let s=this;if(s.conditions.length){let r=s.conditions;s=s.clone(),s.conditions=[],s=r.reduce((o,f)=>f.resolve(o,a),s),s=s.resolve(a)}return s}resolveOptions(a){var s,r,o,f;return Object.assign({},a,{from:a.from||[],strict:(s=a.strict)!=null?s:this.spec.strict,abortEarly:(r=a.abortEarly)!=null?r:this.spec.abortEarly,recursive:(o=a.recursive)!=null?o:this.spec.recursive,disableStackTrace:(f=a.disableStackTrace)!=null?f:this.spec.disableStackTrace})}cast(a,s={}){let r=this.resolve(Object.assign({value:a},s)),o=s.assert==="ignore-optionality",f=r._cast(a,s);if(s.assert!==!1&&!r.isType(f)){if(o&&Na(f))return f;let d=oa(a),h=oa(f);throw new TypeError(`The value of ${s.path||"field"} could not be cast to a value that satisfies the schema type: "${r.type}". 

attempted value: ${d} 
`+(h!==d?`result of cast: ${h}`:""))}return f}_cast(a,s){let r=a===void 0?a:this.transforms.reduce((o,f)=>f.call(this,o,a,this),a);return r===void 0&&(r=this.getDefault(s)),r}_validate(a,s={},r,o){let{path:f,originalValue:d=a,strict:h=this.spec.strict}=s,p=a;h||(p=this._cast(p,Object.assign({assert:!1},s)));let m=[];for(let g of Object.values(this.internalTests))g&&m.push(g);this.runTests({path:f,value:p,originalValue:d,options:s,tests:m},r,g=>{if(g.length)return o(g,p);this.runTests({path:f,value:p,originalValue:d,options:s,tests:this.tests},r,o)})}runTests(a,s,r){let o=!1,{tests:f,value:d,originalValue:h,path:p,options:m}=a,g=N=>{o||(o=!0,s(N,d))},S=N=>{o||(o=!0,r(N,d))},x=f.length,R=[];if(!x)return S([]);let _={value:d,originalValue:h,path:p,options:m,schema:this};for(let N=0;N<f.length;N++){const O=f[N];O(_,g,function(D){D&&(Array.isArray(D)?R.push(...D):R.push(D)),--x<=0&&S(R)})}}asNestedTest({key:a,index:s,parent:r,parentPath:o,originalParent:f,options:d}){const h=a??s;if(h==null)throw TypeError("Must include `key` or `index` for nested validations");const p=typeof h=="number";let m=r[h];const g=Object.assign({},d,{strict:!0,parent:r,value:m,originalValue:f[h],key:void 0,[p?"index":"key"]:h,path:p||h.includes(".")?`${o||""}[${p?h:`"${h}"`}]`:(o?`${o}.`:"")+a});return(S,x,R)=>this.resolve(g)._validate(m,g,x,R)}validate(a,s){var r;let o=this.resolve(Object.assign({},s,{value:a})),f=(r=s==null?void 0:s.disableStackTrace)!=null?r:o.spec.disableStackTrace;return new Promise((d,h)=>o._validate(a,s,(p,m)=>{It.isError(p)&&(p.value=m),h(p)},(p,m)=>{p.length?h(new It(p,m,void 0,void 0,f)):d(m)}))}validateSync(a,s){var r;let o=this.resolve(Object.assign({},s,{value:a})),f,d=(r=s==null?void 0:s.disableStackTrace)!=null?r:o.spec.disableStackTrace;return o._validate(a,Object.assign({},s,{sync:!0}),(h,p)=>{throw It.isError(h)&&(h.value=p),h},(h,p)=>{if(h.length)throw new It(h,a,void 0,void 0,d);f=p}),f}isValid(a,s){return this.validate(a,s).then(()=>!0,r=>{if(It.isError(r))return!1;throw r})}isValidSync(a,s){try{return this.validateSync(a,s),!0}catch(r){if(It.isError(r))return!1;throw r}}_getDefault(a){let s=this.spec.default;return s==null?s:typeof s=="function"?s.call(this,a):Ai(s)}getDefault(a){return this.resolve(a||{})._getDefault(a)}default(a){return arguments.length===0?this._getDefault():this.clone({default:a})}strict(a=!0){return this.clone({strict:a})}nullability(a,s){const r=this.clone({nullable:a});return r.internalTests.nullable=wi({message:s,name:"nullable",test(o){return o===null?this.schema.spec.nullable:!0}}),r}optionality(a,s){const r=this.clone({optional:a});return r.internalTests.optionality=wi({message:s,name:"optionality",test(o){return o===void 0?this.schema.spec.optional:!0}}),r}optional(){return this.optionality(!0)}defined(a=il.defined){return this.optionality(!1,a)}nullable(){return this.nullability(!0)}nonNullable(a=il.notNull){return this.nullability(!1,a)}required(a=il.required){return this.clone().withMutation(s=>s.nonNullable(a).defined(a))}notRequired(){return this.clone().withMutation(a=>a.nullable().optional())}transform(a){let s=this.clone();return s.transforms.push(a),s}test(...a){let s;if(a.length===1?typeof a[0]=="function"?s={test:a[0]}:s=a[0]:a.length===2?s={name:a[0],test:a[1]}:s={name:a[0],message:a[1],test:a[2]},s.message===void 0&&(s.message=il.default),typeof s.test!="function")throw new TypeError("`test` is a required parameters");let r=this.clone(),o=wi(s),f=s.exclusive||s.name&&r.exclusiveTests[s.name]===!0;if(s.exclusive&&!s.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return s.name&&(r.exclusiveTests[s.name]=!!s.exclusive),r.tests=r.tests.filter(d=>!(d.OPTIONS.name===s.name&&(f||d.OPTIONS.test===o.OPTIONS.test))),r.tests.push(o),r}when(a,s){!Array.isArray(a)&&typeof a!="string"&&(s=a,a=".");let r=this.clone(),o=Ty(a).map(f=>new da(f));return o.forEach(f=>{f.isSibling&&r.deps.push(f.key)}),r.conditions.push(typeof s=="function"?new _u(o,s):_u.fromOptions(o,s)),r}typeError(a){let s=this.clone();return s.internalTests.typeError=wi({message:a,name:"typeError",skipAbsent:!0,test(r){return this.schema._typeCheck(r)?!0:this.createError({params:{type:this.schema.type}})}}),s}oneOf(a,s=il.oneOf){let r=this.clone();return a.forEach(o=>{r._whitelist.add(o),r._blacklist.delete(o)}),r.internalTests.whiteList=wi({message:s,name:"oneOf",skipAbsent:!0,test(o){let f=this.schema._whitelist,d=f.resolveAll(this.resolve);return d.includes(o)?!0:this.createError({params:{values:Array.from(f).join(", "),resolved:d}})}}),r}notOneOf(a,s=il.notOneOf){let r=this.clone();return a.forEach(o=>{r._blacklist.add(o),r._whitelist.delete(o)}),r.internalTests.blacklist=wi({message:s,name:"notOneOf",test(o){let f=this.schema._blacklist,d=f.resolveAll(this.resolve);return d.includes(o)?this.createError({params:{values:Array.from(f).join(", "),resolved:d}}):!0}}),r}strip(a=!0){let s=this.clone();return s.spec.strip=a,s}describe(a){const s=(a?this.resolve(a):this).clone(),{label:r,meta:o,optional:f,nullable:d}=s.spec;return{meta:o,label:r,optional:f,nullable:d,default:s.getDefault(a),type:s.type,oneOf:s._whitelist.describe(),notOneOf:s._blacklist.describe(),tests:s.tests.map(p=>({name:p.OPTIONS.name,params:p.OPTIONS.params})).filter((p,m,g)=>g.findIndex(S=>S.name===p.name)===m)}}}ul.prototype.__isYupSchema__=!0;for(const l of["validate","validateSync"])ul.prototype[`${l}At`]=function(a,s,r={}){const{parent:o,parentPath:f,schema:d}=tS(this,a,s,r.context);return d[l](o&&o[f],Object.assign({},r,{parent:o,path:a}))};for(const l of["equals","is"])ul.prototype[l]=ul.prototype.oneOf;for(const l of["not","nope"])ul.prototype[l]=ul.prototype.notOneOf;const nS=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function lS(l){const a=Of(l);if(!a)return Date.parse?Date.parse(l):Number.NaN;if(a.z===void 0&&a.plusMinus===void 0)return new Date(a.year,a.month,a.day,a.hour,a.minute,a.second,a.millisecond).valueOf();let s=0;return a.z!=="Z"&&a.plusMinus!==void 0&&(s=a.hourOffset*60+a.minuteOffset,a.plusMinus==="+"&&(s=0-s)),Date.UTC(a.year,a.month,a.day,a.hour,a.minute+s,a.second,a.millisecond)}function Of(l){var a,s;const r=nS.exec(l);return r?{year:Ml(r[1]),month:Ml(r[2],1)-1,day:Ml(r[3],1),hour:Ml(r[4]),minute:Ml(r[5]),second:Ml(r[6]),millisecond:r[7]?Ml(r[7].substring(0,3)):0,precision:(a=(s=r[7])==null?void 0:s.length)!=null?a:void 0,z:r[8]||void 0,plusMinus:r[9]||void 0,hourOffset:Ml(r[10]),minuteOffset:Ml(r[11])}:null}function Ml(l,a=0){return Number(l)||a}let aS=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,iS=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,sS=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,rS="^\\d{4}-\\d{2}-\\d{2}",uS="\\d{2}:\\d{2}:\\d{2}",oS="(([+-]\\d{2}(:?\\d{2})?)|Z)",cS=new RegExp(`${rS}T${uS}(\\.\\d+)?${oS}$`),fS=l=>Na(l)||l===l.trim(),dS={}.toString();function Hn(){return new Ny}class Ny extends ul{constructor(){super({type:"string",check(a){return a instanceof String&&(a=a.valueOf()),typeof a=="string"}}),this.withMutation(()=>{this.transform((a,s,r)=>{if(!r.spec.coerce||r.isType(a)||Array.isArray(a))return a;const o=a!=null&&a.toString?a.toString():a;return o===dS?a:o})})}required(a){return super.required(a).withMutation(s=>s.test({message:a||il.required,name:"required",skipAbsent:!0,test:r=>!!r.length}))}notRequired(){return super.notRequired().withMutation(a=>(a.tests=a.tests.filter(s=>s.OPTIONS.name!=="required"),a))}length(a,s=Wt.length){return this.test({message:s,name:"length",exclusive:!0,params:{length:a},skipAbsent:!0,test(r){return r.length===this.resolve(a)}})}min(a,s=Wt.min){return this.test({message:s,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(r){return r.length>=this.resolve(a)}})}max(a,s=Wt.max){return this.test({name:"max",exclusive:!0,message:s,params:{max:a},skipAbsent:!0,test(r){return r.length<=this.resolve(a)}})}matches(a,s){let r=!1,o,f;return s&&(typeof s=="object"?{excludeEmptyString:r=!1,message:o,name:f}=s:o=s),this.test({name:f||"matches",message:o||Wt.matches,params:{regex:a},skipAbsent:!0,test:d=>d===""&&r||d.search(a)!==-1})}email(a=Wt.email){return this.matches(aS,{name:"email",message:a,excludeEmptyString:!0})}url(a=Wt.url){return this.matches(iS,{name:"url",message:a,excludeEmptyString:!0})}uuid(a=Wt.uuid){return this.matches(sS,{name:"uuid",message:a,excludeEmptyString:!1})}datetime(a){let s="",r,o;return a&&(typeof a=="object"?{message:s="",allowOffset:r=!1,precision:o=void 0}=a:s=a),this.matches(cS,{name:"datetime",message:s||Wt.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:s||Wt.datetime_offset,params:{allowOffset:r},skipAbsent:!0,test:f=>{if(!f||r)return!0;const d=Of(f);return d?!!d.z:!1}}).test({name:"datetime_precision",message:s||Wt.datetime_precision,params:{precision:o},skipAbsent:!0,test:f=>{if(!f||o==null)return!0;const d=Of(f);return d?d.precision===o:!1}})}ensure(){return this.default("").transform(a=>a===null?"":a)}trim(a=Wt.trim){return this.transform(s=>s!=null?s.trim():s).test({message:a,name:"trim",test:fS})}lowercase(a=Wt.lowercase){return this.transform(s=>Na(s)?s:s.toLowerCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:s=>Na(s)||s===s.toLowerCase()})}uppercase(a=Wt.uppercase){return this.transform(s=>Na(s)?s:s.toUpperCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:s=>Na(s)||s===s.toUpperCase()})}}Hn.prototype=Ny.prototype;let hS=new Date(""),mS=l=>Object.prototype.toString.call(l)==="[object Date]";class ed extends ul{constructor(){super({type:"date",check(a){return mS(a)&&!isNaN(a.getTime())}}),this.withMutation(()=>{this.transform((a,s,r)=>!r.spec.coerce||r.isType(a)||a===null?a:(a=lS(a),isNaN(a)?ed.INVALID_DATE:new Date(a)))})}prepareParam(a,s){let r;if(da.isRef(a))r=a;else{let o=this.cast(a);if(!this._typeCheck(o))throw new TypeError(`\`${s}\` must be a Date or a value that can be \`cast()\` to a Date`);r=o}return r}min(a,s=Rf.min){let r=this.prepareParam(a,"min");return this.test({message:s,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(o){return o>=this.resolve(r)}})}max(a,s=Rf.max){let r=this.prepareParam(a,"max");return this.test({message:s,name:"max",exclusive:!0,params:{max:a},skipAbsent:!0,test(o){return o<=this.resolve(r)}})}}ed.INVALID_DATE=hS;function pS(l,a=[]){let s=[],r=new Set,o=new Set(a.map(([d,h])=>`${d}-${h}`));function f(d,h){let p=Ua.split(d)[0];r.add(p),o.has(`${h}-${p}`)||s.push([h,p])}for(const d of Object.keys(l)){let h=l[d];r.add(d),da.isRef(h)&&h.isSibling?f(h.path,d):If(h)&&"deps"in h&&h.deps.forEach(p=>f(p,d))}return Y2.array(Array.from(r),s).reverse()}function wp(l,a){let s=1/0;return l.some((r,o)=>{var f;if((f=a.path)!=null&&f.includes(r))return s=o,!0}),s}function jy(l){return(a,s)=>wp(l,a)-wp(l,s)}const yS=(l,a,s)=>{if(typeof l!="string")return l;let r=l;try{r=JSON.parse(l)}catch{}return s.isType(r)?r:l};function yu(l){if("fields"in l){const a={};for(const[s,r]of Object.entries(l.fields))a[s]=yu(r);return l.setFields(a)}if(l.type==="array"){const a=l.optional();return a.innerType&&(a.innerType=yu(a.innerType)),a}return l.type==="tuple"?l.optional().clone({types:l.spec.types.map(yu)}):"optional"in l?l.optional():l}const gS=(l,a)=>{const s=[...Ua.normalizePath(a)];if(s.length===1)return s[0]in l;let r=s.pop(),o=Ua.getter(Ua.join(s),!0)(l);return!!(o&&r in o)};let _p=l=>Object.prototype.toString.call(l)==="[object Object]";function Ap(l,a){let s=Object.keys(l.fields);return Object.keys(a).filter(r=>s.indexOf(r)===-1)}const vS=jy([]);function Zs(l){return new Uy(l)}class Uy extends ul{constructor(a){super({type:"object",check(s){return _p(s)||typeof s=="function"}}),this.fields=Object.create(null),this._sortErrors=vS,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{a&&this.shape(a)})}_cast(a,s={}){var r;let o=super._cast(a,s);if(o===void 0)return this.getDefault(s);if(!this._typeCheck(o))return o;let f=this.fields,d=(r=s.stripUnknown)!=null?r:this.spec.noUnknown,h=[].concat(this._nodes,Object.keys(o).filter(S=>!this._nodes.includes(S))),p={},m=Object.assign({},s,{parent:p,__validating:s.__validating||!1}),g=!1;for(const S of h){let x=f[S],R=S in o;if(x){let _,N=o[S];m.path=(s.path?`${s.path}.`:"")+S,x=x.resolve({value:N,context:s.context,parent:p});let O=x instanceof ul?x.spec:void 0,E=O==null?void 0:O.strict;if(O!=null&&O.strip){g=g||S in o;continue}_=!s.__validating||!E?x.cast(o[S],m):o[S],_!==void 0&&(p[S]=_)}else R&&!d&&(p[S]=o[S]);(R!==S in p||p[S]!==o[S])&&(g=!0)}return g?p:o}_validate(a,s={},r,o){let{from:f=[],originalValue:d=a,recursive:h=this.spec.recursive}=s;s.from=[{schema:this,value:d},...f],s.__validating=!0,s.originalValue=d,super._validate(a,s,r,(p,m)=>{if(!h||!_p(m)){o(p,m);return}d=d||m;let g=[];for(let S of this._nodes){let x=this.fields[S];!x||da.isRef(x)||g.push(x.asNestedTest({options:s,key:S,parent:m,parentPath:s.path,originalParent:d}))}this.runTests({tests:g,value:m,originalValue:d,options:s},r,S=>{o(S.sort(this._sortErrors).concat(p),m)})})}clone(a){const s=super.clone(a);return s.fields=Object.assign({},this.fields),s._nodes=this._nodes,s._excludedEdges=this._excludedEdges,s._sortErrors=this._sortErrors,s}concat(a){let s=super.concat(a),r=s.fields;for(let[o,f]of Object.entries(this.fields)){const d=r[o];r[o]=d===void 0?f:d}return s.withMutation(o=>o.setFields(r,[...this._excludedEdges,...a._excludedEdges]))}_getDefault(a){if("default"in this.spec)return super._getDefault(a);if(!this._nodes.length)return;let s={};return this._nodes.forEach(r=>{var o;const f=this.fields[r];let d=a;(o=d)!=null&&o.value&&(d=Object.assign({},d,{parent:d.value,value:d.value[r]})),s[r]=f&&"getDefault"in f?f.getDefault(d):void 0}),s}setFields(a,s){let r=this.clone();return r.fields=a,r._nodes=pS(a,s),r._sortErrors=jy(Object.keys(a)),s&&(r._excludedEdges=s),r}shape(a,s=[]){return this.clone().withMutation(r=>{let o=r._excludedEdges;return s.length&&(Array.isArray(s[0])||(s=[s]),o=[...r._excludedEdges,...s]),r.setFields(Object.assign(r.fields,a),o)})}partial(){const a={};for(const[s,r]of Object.entries(this.fields))a[s]="optional"in r&&r.optional instanceof Function?r.optional():r;return this.setFields(a)}deepPartial(){return yu(this)}pick(a){const s={};for(const r of a)this.fields[r]&&(s[r]=this.fields[r]);return this.setFields(s,this._excludedEdges.filter(([r,o])=>a.includes(r)&&a.includes(o)))}omit(a){const s=[];for(const r of Object.keys(this.fields))a.includes(r)||s.push(r);return this.pick(s)}from(a,s,r){let o=Ua.getter(a,!0);return this.transform(f=>{if(!f)return f;let d=f;return gS(f,a)&&(d=Object.assign({},f),r||delete d[a],d[s]=o(f)),d})}json(){return this.transform(yS)}exact(a){return this.test({name:"exact",exclusive:!0,message:a||pu.exact,test(s){if(s==null)return!0;const r=Ap(this.schema,s);return r.length===0||this.createError({params:{properties:r.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(a=!0,s=pu.noUnknown){typeof a!="boolean"&&(s=a,a=!0);let r=this.test({name:"noUnknown",exclusive:!0,message:s,test(o){if(o==null)return!0;const f=Ap(this.schema,o);return!a||f.length===0||this.createError({params:{unknown:f.join(", ")}})}});return r.spec.noUnknown=a,r}unknown(a=!0,s=pu.noUnknown){return this.noUnknown(!a,s)}transformKeys(a){return this.transform(s=>{if(!s)return s;const r={};for(const o of Object.keys(s))r[a(o)]=s[o];return r})}camelCase(){return this.transformKeys(mf.camelCase)}snakeCase(){return this.transformKeys(mf.snakeCase)}constantCase(){return this.transformKeys(a=>mf.snakeCase(a).toUpperCase())}describe(a){const s=(a?this.resolve(a):this).clone(),r=super.describe(a);r.fields={};for(const[f,d]of Object.entries(s.fields)){var o;let h=a;(o=h)!=null&&o.value&&(h=Object.assign({},h,{parent:h.value,value:h.value[f]})),r.fields[f]=d.describe(h)}return r}}Zs.prototype=Uy.prototype;const Tp=(l,a,s)=>{if(l&&"reportValidity"in l){const r=de(s,a);l.setCustomValidity(r&&r.message||""),l.reportValidity()}},My=(l,a)=>{for(const s in a.fields){const r=a.fields[s];r&&r.ref&&"reportValidity"in r.ref?Tp(r.ref,s,l):r&&r.refs&&r.refs.forEach(o=>Tp(o,s,l))}},bS=(l,a)=>{a.shouldUseNativeValidation&&My(l,a);const s={};for(const r in l){const o=de(a.fields,r),f=Object.assign(l[r]||{},{ref:o&&o.ref});if(SS(a.names||Object.keys(l),r)){const d=Object.assign({},de(s,r));Ke(d,"root",f),Ke(s,r,d)}else Ke(s,r,f)}return s},SS=(l,a)=>{const s=Rp(a);return l.some(r=>Rp(r).match(`^${s}\\.\\d+`))};function Rp(l){return l.replace(/\]|\[/g,"")}function zu(l,a,s){return s===void 0&&(s={}),function(r,o,f){try{return Promise.resolve(function(d,h){try{var p=(a!=null&&a.context,Promise.resolve(l[s.mode==="sync"?"validateSync":"validate"](r,Object.assign({abortEarly:!1},a,{context:o}))).then(function(m){return f.shouldUseNativeValidation&&My({},f),{values:s.raw?Object.assign({},r):m,errors:{}}}))}catch(m){return h(m)}return p&&p.then?p.then(void 0,h):p}(0,function(d){if(!d.inner)throw d;return{values:{},errors:bS((h=d,p=!f.shouldUseNativeValidation&&f.criteriaMode==="all",(h.inner||[]).reduce(function(m,g){if(m[g.path]||(m[g.path]={message:g.message,type:g.type}),p){var S=m[g.path].types,x=S&&S[g.type];m[g.path]=by(g.path,p,m,g.type,x?[].concat(x,g.message):g.message)}return m},{})),f)};var h,p}))}catch(d){return Promise.reject(d)}}}const pf="http://127.0.0.1:8000/auth/",xS="http://127.0.0.1:8000/users/";class ES{async login(a,s){try{const r=await fetch(pf+"jwt/create/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:s})});if(!r.ok)return console.log("login failed!"),!1;const o=await r.json();if(o.access){localStorage.setItem("access_token",o.access),localStorage.setItem("refresh_token",o.refresh),console.log("token stored in the local storage!");const f=await this.getCurrentUser(),{date_created:d,...h}=f;sessionStorage.setItem("current_user",JSON.stringify(h)),sessionStorage.setItem("account_created_date",f.date_created)}else console.log("No access token in response!");return!0}catch(r){throw console.error("Login failed",r),r}}async resetPassword(a){try{const s=await fetch(pf+"users/reset_password/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a})});return s.ok||console.log("Reset password response:",s),s.status}catch(s){return console.log("Failed to reset password!",s),s}}async resetPasswordConfirm(a,s,r){try{const o=await fetch(pf+"users/reset_password_confirm/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({uid:a,token:s,new_password:r})});return o.ok||console.log("Reset password confirm response:",o.status),o.status}catch(o){return console.log("Failed to reset password confirm!",o),o}}async getCurrentUser(){try{const a=await fetch(xS+"me/",{method:"GET",headers:this.getAuthHeader()});if(!a.ok){const r=await a.json();return console.log("Error data: ",r),null}return await a.json()}catch(a){console.log("Failed to get the current user!",a)}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{"Content-Type":"application/json",Accept:"application/json",Authorization:a?`JWT ${a}`:""}}logout(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),sessionStorage.removeItem("current_user"),sessionStorage.removeItem("account_created_date")}}const On=new ES;function ca(){return v.jsxs("div",{className:"loading-ripple",children:[v.jsx("div",{className:"circle-1"}),v.jsx("div",{className:"circle-2"}),v.jsx("div",{className:"circle-3"}),v.jsx("div",{className:"circle-4"})]})}const ku="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%23f5f5f9'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",wS="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Check'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%2020%2020'%3e%3cpath%20d='M8.294%2016.998c-.435%200-.847-.203-1.111-.553L3.61%2011.724a1.392%201.392%200%200%201%20.27-1.951%201.392%201.392%200%200%201%201.953.27l2.351%203.104%205.911-9.492a1.396%201.396%200%200%201%201.921-.445c.653.406.854%201.266.446%201.92L9.478%2016.34a1.39%201.39%200%200%201-1.12.656c-.022.002-.042.002-.064.002z'%20fill='%23fcfcfc'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e";function qn({message:l,type:a}){let s=a==="success"?wS:ku;return v.jsxs("div",{className:`alert alert-${a}`,children:[v.jsx("img",{src:s,alt:"close-icon"}),v.jsx("p",{children:l})]})}function _S(){const l=$n(),[a,s]=w.useState(!1),[r,o]=w.useState(null);w.useEffect(()=>{r&&setTimeout(()=>{o(!1)},5e3)},[r]);const f=Zs().shape({email:Hn().required("Must not empty.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:Hn().required("Must not empty.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=Xs({resolver:zu(f),mode:"all"}),g=async S=>{s(!0);try{await On.login(S.email,S.password)?(console.log("Login successfully!"),l("/home")):o(!0)}catch(x){console.log("login failed!",x)}finally{s(!1)}};return v.jsxs(v.Fragment,{children:[r&&v.jsx(qn,{message:"Invalid credentials.",type:"danger"}),v.jsxs("main",{className:"login-page",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:g2,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("img",{src:v2,alt:"logo"}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),p.email&&v.jsx("span",{children:p.email.message}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email",...d("email")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password:"}),p.password&&v.jsx("span",{children:p.password.message}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password",...d("password")})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(ca,{}),a?"Verifying...":"Log In"]})]}),v.jsx("a",{onClick:()=>l("/reset-password"),children:"Forgot Password?"})]})]})]})}const Tu="data:image/svg+xml,%3csvg%20width='200'%20height='200'%20viewBox='0%200%20200%20200'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20x='30'%20y='29'%20width='147'%20height='147'%20rx='73.5'%20fill='%23DEDEDE'/%3e%3crect%20x='7'%20y='145'%20width='39'%20height='39'%20rx='19.5'%20fill='%23FFCEBC'/%3e%3cg%20clip-path='url(%23clip0_303_529)'%3e%3cpath%20d='M63.3333%20139.856C75.5553%20148.745%2087.778%20155.856%20100%20155.856C112.222%20155.856%20124.445%20148.745%20136.667%20139.856C126.592%20136.789%20121.481%20132.212%20121.333%20126.123C121.333%20125.037%20121.341%20123.496%20121.349%20120.602C121.349%20120.142%20121.35%20119.672%20121.352%20119.19C121.372%20111.43%20121.405%20101.19%20121.451%2090.79C131.005%2078.4054%20127.495%2063.886%20124.501%2064.248C120.831%2064.6947%2089.0287%2034.516%2082.9287%2032.9687C76.8287%2031.4214%2061.3333%2036.4167%2058.6667%2050.3334C56%2064.25%2054.8853%2099.3287%2065%20113.333C67.8782%20117.319%2072.4116%20118.878%2078.6%20118.01C78.6087%20120.923%2078.626%20122.557%2078.6667%20125.856C78.5873%20132.222%2073.4767%20136.793%2063.3333%20139.856Z'%20fill='url(%23paint0_linear_303_529)'/%3e%3cpath%20d='M78.6667%20118C93.3334%20116.333%20102.667%20110%20102.667%20110C102.667%20110%2092.0001%20123.333%2078.6667%20126V118Z'%20fill='%23FC9F6A'/%3e%3cpath%20d='M154%2091C163%2081.8752%20172.252%2055.2576%20167.5%2036.5C158%20-0.999866%2093.1667%200.500212%2073.5001%206.50021C60.2821%2010.5329%2047.0001%2021.5%2045%2025.0002C36.0559%2040.6526%2047.0108%2054.3335%2055.8001%2057.4555C63.7775%2060.2888%2077.2668%2063.1221%20100.204%2060.4555C104.301%2059.9788%20103.443%2072.5275%20105.633%2073.8842C108.917%2075.9195%20111.467%2063.1221%20120.472%2066.9648C129.477%2070.8075%20124.133%2088.6248%20114.467%2088.6248C111.133%2088.6248%20109.467%2097.7895%20118.467%20102.123C125%20105.333%20145.5%2098%20154%2091Z'%20fill='url(%23paint1_linear_303_529)'/%3e%3cpath%20d='M170%20160.205C176.347%20173.11%20180%20202.872%20180%20202.872H20C20%20202.872%2023.6547%20173.107%2030%20160.205C36.3453%20147.304%2073.4%20135.338%2073.4%20135.338C93.8753%20143.333%20106.405%20143.333%20126.581%20135.333C126.581%20135.333%20163.653%20147.301%20170%20160.205Z'%20fill='url(%23paint2_linear_303_529)'/%3e%3cpath%20d='M100%20136.661L93.3334%20148.667L72.6667%20136L78.6667%20127.333L100%20135.214L121.333%20127.333L127.333%20136L106.667%20148.667L100%20136.661Z'%20fill='white'/%3e%3c/g%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_303_529'%20x1='96.7478'%20y1='32.6981'%20x2='96.7478'%20y2='155.856'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F7B186'/%3e%3cstop%20offset='1'%20stop-color='%23FFC299'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_303_529'%20x1='92.9604'%20y1='22.2586'%20x2='92.9604'%20y2='102.912'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%231D0024'/%3e%3cstop%20offset='1'%20stop-color='%23100014'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_303_529'%20x1='100'%20y1='135.333'%20x2='100'%20y2='202.872'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%231D0024'/%3e%3cstop%20offset='1'%20stop-color='%23100014'/%3e%3c/linearGradient%3e%3cclipPath%20id='clip0_303_529'%3e%3crect%20width='200'%20height='200'%20rx='100'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",AS="data:image/svg+xml,%3csvg%20width='33'%20height='34'%20viewBox='0%200%2033%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='20.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M18.5%2012L21.5%2016'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M26.5%209.5L23.5%2015.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3ccircle%20cx='17'%20cy='10.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='23'%20cy='17.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='27'%20cy='7.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M5.5%2024L10%2021.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M13%2018.5L16%2012.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M27%2024.5L31.5326%2028.5793C31.7717%2028.7945%2031.7483%2029.1762%2031.4849%2029.3606L27%2032.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M30%2028.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M1%206.5L5.07934%201.9674C5.2945%201.72833%205.67616%201.75165%205.8606%202.01515L9%206.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M5%203.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3c/svg%3e",Ri=Math.min,Ma=Math.max,Ru=Math.round,fu=Math.floor,rl=l=>({x:l,y:l}),TS={left:"right",right:"left",bottom:"top",top:"bottom"},RS={start:"end",end:"start"};function Cf(l,a,s){return Ma(l,Ri(a,s))}function Qs(l,a){return typeof l=="function"?l(a):l}function ka(l){return l.split("-")[0]}function Ks(l){return l.split("-")[1]}function Ly(l){return l==="x"?"y":"x"}function td(l){return l==="y"?"height":"width"}function Oi(l){return["top","bottom"].includes(ka(l))?"y":"x"}function nd(l){return Ly(Oi(l))}function OS(l,a,s){s===void 0&&(s=!1);const r=Ks(l),o=nd(l),f=td(o);let d=o==="x"?r===(s?"end":"start")?"right":"left":r==="start"?"bottom":"top";return a.reference[f]>a.floating[f]&&(d=Ou(d)),[d,Ou(d)]}function CS(l){const a=Ou(l);return[Df(l),a,Df(a)]}function Df(l){return l.replace(/start|end/g,a=>RS[a])}function DS(l,a,s){const r=["left","right"],o=["right","left"],f=["top","bottom"],d=["bottom","top"];switch(l){case"top":case"bottom":return s?a?o:r:a?r:o;case"left":case"right":return a?f:d;default:return[]}}function NS(l,a,s,r){const o=Ks(l);let f=DS(ka(l),s==="start",r);return o&&(f=f.map(d=>d+"-"+o),a&&(f=f.concat(f.map(Df)))),f}function Ou(l){return l.replace(/left|right|bottom|top/g,a=>TS[a])}function jS(l){return{top:0,right:0,bottom:0,left:0,...l}}function zy(l){return typeof l!="number"?jS(l):{top:l,right:l,bottom:l,left:l}}function Cu(l){const{x:a,y:s,width:r,height:o}=l;return{width:r,height:o,top:s,left:a,right:a+r,bottom:s+o,x:a,y:s}}function Op(l,a,s){let{reference:r,floating:o}=l;const f=Oi(a),d=nd(a),h=td(d),p=ka(a),m=f==="y",g=r.x+r.width/2-o.width/2,S=r.y+r.height/2-o.height/2,x=r[h]/2-o[h]/2;let R;switch(p){case"top":R={x:g,y:r.y-o.height};break;case"bottom":R={x:g,y:r.y+r.height};break;case"right":R={x:r.x+r.width,y:S};break;case"left":R={x:r.x-o.width,y:S};break;default:R={x:r.x,y:r.y}}switch(Ks(a)){case"start":R[d]-=x*(s&&m?-1:1);break;case"end":R[d]+=x*(s&&m?-1:1);break}return R}const US=async(l,a,s)=>{const{placement:r="bottom",strategy:o="absolute",middleware:f=[],platform:d}=s,h=f.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(a));let m=await d.getElementRects({reference:l,floating:a,strategy:o}),{x:g,y:S}=Op(m,r,p),x=r,R={},_=0;for(let N=0;N<h.length;N++){const{name:O,fn:E}=h[N],{x:D,y:U,data:G,reset:k}=await E({x:g,y:S,initialPlacement:r,placement:x,strategy:o,middlewareData:R,rects:m,platform:d,elements:{reference:l,floating:a}});g=D??g,S=U??S,R={...R,[O]:{...R[O],...G}},k&&_<=50&&(_++,typeof k=="object"&&(k.placement&&(x=k.placement),k.rects&&(m=k.rects===!0?await d.getElementRects({reference:l,floating:a,strategy:o}):k.rects),{x:g,y:S}=Op(m,x,p)),N=-1)}return{x:g,y:S,placement:x,strategy:o,middlewareData:R}};async function ky(l,a){var s;a===void 0&&(a={});const{x:r,y:o,platform:f,rects:d,elements:h,strategy:p}=l,{boundary:m="clippingAncestors",rootBoundary:g="viewport",elementContext:S="floating",altBoundary:x=!1,padding:R=0}=Qs(a,l),_=zy(R),O=h[x?S==="floating"?"reference":"floating":S],E=Cu(await f.getClippingRect({element:(s=await(f.isElement==null?void 0:f.isElement(O)))==null||s?O:O.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:m,rootBoundary:g,strategy:p})),D=S==="floating"?{x:r,y:o,width:d.floating.width,height:d.floating.height}:d.reference,U=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),G=await(f.isElement==null?void 0:f.isElement(U))?await(f.getScale==null?void 0:f.getScale(U))||{x:1,y:1}:{x:1,y:1},k=Cu(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:D,offsetParent:U,strategy:p}):D);return{top:(E.top-k.top+_.top)/G.y,bottom:(k.bottom-E.bottom+_.bottom)/G.y,left:(E.left-k.left+_.left)/G.x,right:(k.right-E.right+_.right)/G.x}}const MS=l=>({name:"arrow",options:l,async fn(a){const{x:s,y:r,placement:o,rects:f,platform:d,elements:h,middlewareData:p}=a,{element:m,padding:g=0}=Qs(l,a)||{};if(m==null)return{};const S=zy(g),x={x:s,y:r},R=nd(o),_=td(R),N=await d.getDimensions(m),O=R==="y",E=O?"top":"left",D=O?"bottom":"right",U=O?"clientHeight":"clientWidth",G=f.reference[_]+f.reference[R]-x[R]-f.floating[_],k=x[R]-f.reference[R],te=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let W=te?te[U]:0;(!W||!await(d.isElement==null?void 0:d.isElement(te)))&&(W=h.floating[U]||f.floating[_]);const K=G/2-k/2,ne=W/2-N[_]/2-1,xe=Ri(S[E],ne),oe=Ri(S[D],ne),ae=xe,pe=W-N[_]-oe,he=W/2-N[_]/2+K,we=Cf(ae,he,pe),L=!p.arrow&&Ks(o)!=null&&he!==we&&f.reference[_]/2-(he<ae?xe:oe)-N[_]/2<0,X=L?he<ae?he-ae:he-pe:0;return{[R]:x[R]+X,data:{[R]:we,centerOffset:he-we-X,...L&&{alignmentOffset:X}},reset:L}}}),LS=function(l){return l===void 0&&(l={}),{name:"flip",options:l,async fn(a){var s,r;const{placement:o,middlewareData:f,rects:d,initialPlacement:h,platform:p,elements:m}=a,{mainAxis:g=!0,crossAxis:S=!0,fallbackPlacements:x,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:N=!0,...O}=Qs(l,a);if((s=f.arrow)!=null&&s.alignmentOffset)return{};const E=ka(o),D=Oi(h),U=ka(h)===h,G=await(p.isRTL==null?void 0:p.isRTL(m.floating)),k=x||(U||!N?[Ou(h)]:CS(h)),te=_!=="none";!x&&te&&k.push(...NS(h,N,_,G));const W=[h,...k],K=await ky(a,O),ne=[];let xe=((r=f.flip)==null?void 0:r.overflows)||[];if(g&&ne.push(K[E]),S){const he=OS(o,d,G);ne.push(K[he[0]],K[he[1]])}if(xe=[...xe,{placement:o,overflows:ne}],!ne.every(he=>he<=0)){var oe,ae;const he=(((oe=f.flip)==null?void 0:oe.index)||0)+1,we=W[he];if(we)return{data:{index:he,overflows:xe},reset:{placement:we}};let L=(ae=xe.filter(X=>X.overflows[0]<=0).sort((X,I)=>X.overflows[1]-I.overflows[1])[0])==null?void 0:ae.placement;if(!L)switch(R){case"bestFit":{var pe;const X=(pe=xe.filter(I=>{if(te){const me=Oi(I.placement);return me===D||me==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(me=>me>0).reduce((me,T)=>me+T,0)]).sort((I,me)=>I[1]-me[1])[0])==null?void 0:pe[0];X&&(L=X);break}case"initialPlacement":L=h;break}if(o!==L)return{reset:{placement:L}}}return{}}}};async function zS(l,a){const{placement:s,platform:r,elements:o}=l,f=await(r.isRTL==null?void 0:r.isRTL(o.floating)),d=ka(s),h=Ks(s),p=Oi(s)==="y",m=["left","top"].includes(d)?-1:1,g=f&&p?-1:1,S=Qs(a,l);let{mainAxis:x,crossAxis:R,alignmentAxis:_}=typeof S=="number"?{mainAxis:S,crossAxis:0,alignmentAxis:null}:{mainAxis:S.mainAxis||0,crossAxis:S.crossAxis||0,alignmentAxis:S.alignmentAxis};return h&&typeof _=="number"&&(R=h==="end"?_*-1:_),p?{x:R*g,y:x*m}:{x:x*m,y:R*g}}const kS=function(l){return l===void 0&&(l=0),{name:"offset",options:l,async fn(a){var s,r;const{x:o,y:f,placement:d,middlewareData:h}=a,p=await zS(a,l);return d===((s=h.offset)==null?void 0:s.placement)&&(r=h.arrow)!=null&&r.alignmentOffset?{}:{x:o+p.x,y:f+p.y,data:{...p,placement:d}}}}},BS=function(l){return l===void 0&&(l={}),{name:"shift",options:l,async fn(a){const{x:s,y:r,placement:o}=a,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:O=>{let{x:E,y:D}=O;return{x:E,y:D}}},...p}=Qs(l,a),m={x:s,y:r},g=await ky(a,p),S=Oi(ka(o)),x=Ly(S);let R=m[x],_=m[S];if(f){const O=x==="y"?"top":"left",E=x==="y"?"bottom":"right",D=R+g[O],U=R-g[E];R=Cf(D,R,U)}if(d){const O=S==="y"?"top":"left",E=S==="y"?"bottom":"right",D=_+g[O],U=_-g[E];_=Cf(D,_,U)}const N=h.fn({...a,[x]:R,[S]:_});return{...N,data:{x:N.x-s,y:N.y-r,enabled:{[x]:f,[S]:d}}}}}};function Bu(){return typeof window<"u"}function Ui(l){return By(l)?(l.nodeName||"").toLowerCase():"#document"}function mn(l){var a;return(l==null||(a=l.ownerDocument)==null?void 0:a.defaultView)||window}function fl(l){var a;return(a=(By(l)?l.ownerDocument:l.document)||window.document)==null?void 0:a.documentElement}function By(l){return Bu()?l instanceof Node||l instanceof mn(l).Node:!1}function Fn(l){return Bu()?l instanceof Element||l instanceof mn(l).Element:!1}function ol(l){return Bu()?l instanceof HTMLElement||l instanceof mn(l).HTMLElement:!1}function Cp(l){return!Bu()||typeof ShadowRoot>"u"?!1:l instanceof ShadowRoot||l instanceof mn(l).ShadowRoot}function Js(l){const{overflow:a,overflowX:s,overflowY:r,display:o}=Yn(l);return/auto|scroll|overlay|hidden|clip/.test(a+r+s)&&!["inline","contents"].includes(o)}function HS(l){return["table","td","th"].includes(Ui(l))}function Hu(l){return[":popover-open",":modal"].some(a=>{try{return l.matches(a)}catch{return!1}})}function ld(l){const a=ad(),s=Fn(l)?Yn(l):l;return["transform","translate","scale","rotate","perspective"].some(r=>s[r]?s[r]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(s.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(s.contain||"").includes(r))}function qS(l){let a=fa(l);for(;ol(a)&&!Ci(a);){if(ld(a))return a;if(Hu(a))return null;a=fa(a)}return null}function ad(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ci(l){return["html","body","#document"].includes(Ui(l))}function Yn(l){return mn(l).getComputedStyle(l)}function qu(l){return Fn(l)?{scrollLeft:l.scrollLeft,scrollTop:l.scrollTop}:{scrollLeft:l.scrollX,scrollTop:l.scrollY}}function fa(l){if(Ui(l)==="html")return l;const a=l.assignedSlot||l.parentNode||Cp(l)&&l.host||fl(l);return Cp(a)?a.host:a}function Hy(l){const a=fa(l);return Ci(a)?l.ownerDocument?l.ownerDocument.body:l.body:ol(a)&&Js(a)?a:Hy(a)}function Vs(l,a,s){var r;a===void 0&&(a=[]),s===void 0&&(s=!0);const o=Hy(l),f=o===((r=l.ownerDocument)==null?void 0:r.body),d=mn(o);if(f){const h=Nf(d);return a.concat(d,d.visualViewport||[],Js(o)?o:[],h&&s?Vs(h):[])}return a.concat(o,Vs(o,[],s))}function Nf(l){return l.parent&&Object.getPrototypeOf(l.parent)?l.frameElement:null}function qy(l){const a=Yn(l);let s=parseFloat(a.width)||0,r=parseFloat(a.height)||0;const o=ol(l),f=o?l.offsetWidth:s,d=o?l.offsetHeight:r,h=Ru(s)!==f||Ru(r)!==d;return h&&(s=f,r=d),{width:s,height:r,$:h}}function id(l){return Fn(l)?l:l.contextElement}function Ti(l){const a=id(l);if(!ol(a))return rl(1);const s=a.getBoundingClientRect(),{width:r,height:o,$:f}=qy(a);let d=(f?Ru(s.width):s.width)/r,h=(f?Ru(s.height):s.height)/o;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const VS=rl(0);function Vy(l){const a=mn(l);return!ad()||!a.visualViewport?VS:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function FS(l,a,s){return a===void 0&&(a=!1),!s||a&&s!==mn(l)?!1:a}function Ba(l,a,s,r){a===void 0&&(a=!1),s===void 0&&(s=!1);const o=l.getBoundingClientRect(),f=id(l);let d=rl(1);a&&(r?Fn(r)&&(d=Ti(r)):d=Ti(l));const h=FS(f,s,r)?Vy(f):rl(0);let p=(o.left+h.x)/d.x,m=(o.top+h.y)/d.y,g=o.width/d.x,S=o.height/d.y;if(f){const x=mn(f),R=r&&Fn(r)?mn(r):r;let _=x,N=Nf(_);for(;N&&r&&R!==_;){const O=Ti(N),E=N.getBoundingClientRect(),D=Yn(N),U=E.left+(N.clientLeft+parseFloat(D.paddingLeft))*O.x,G=E.top+(N.clientTop+parseFloat(D.paddingTop))*O.y;p*=O.x,m*=O.y,g*=O.x,S*=O.y,p+=U,m+=G,_=mn(N),N=Nf(_)}}return Cu({width:g,height:S,x:p,y:m})}function sd(l,a){const s=qu(l).scrollLeft;return a?a.left+s:Ba(fl(l)).left+s}function Fy(l,a,s){s===void 0&&(s=!1);const r=l.getBoundingClientRect(),o=r.left+a.scrollLeft-(s?0:sd(l,r)),f=r.top+a.scrollTop;return{x:o,y:f}}function YS(l){let{elements:a,rect:s,offsetParent:r,strategy:o}=l;const f=o==="fixed",d=fl(r),h=a?Hu(a.floating):!1;if(r===d||h&&f)return s;let p={scrollLeft:0,scrollTop:0},m=rl(1);const g=rl(0),S=ol(r);if((S||!S&&!f)&&((Ui(r)!=="body"||Js(d))&&(p=qu(r)),ol(r))){const R=Ba(r);m=Ti(r),g.x=R.x+r.clientLeft,g.y=R.y+r.clientTop}const x=d&&!S&&!f?Fy(d,p,!0):rl(0);return{width:s.width*m.x,height:s.height*m.y,x:s.x*m.x-p.scrollLeft*m.x+g.x+x.x,y:s.y*m.y-p.scrollTop*m.y+g.y+x.y}}function GS(l){return Array.from(l.getClientRects())}function $S(l){const a=fl(l),s=qu(l),r=l.ownerDocument.body,o=Ma(a.scrollWidth,a.clientWidth,r.scrollWidth,r.clientWidth),f=Ma(a.scrollHeight,a.clientHeight,r.scrollHeight,r.clientHeight);let d=-s.scrollLeft+sd(l);const h=-s.scrollTop;return Yn(r).direction==="rtl"&&(d+=Ma(a.clientWidth,r.clientWidth)-o),{width:o,height:f,x:d,y:h}}function XS(l,a){const s=mn(l),r=fl(l),o=s.visualViewport;let f=r.clientWidth,d=r.clientHeight,h=0,p=0;if(o){f=o.width,d=o.height;const m=ad();(!m||m&&a==="fixed")&&(h=o.offsetLeft,p=o.offsetTop)}return{width:f,height:d,x:h,y:p}}function ZS(l,a){const s=Ba(l,!0,a==="fixed"),r=s.top+l.clientTop,o=s.left+l.clientLeft,f=ol(l)?Ti(l):rl(1),d=l.clientWidth*f.x,h=l.clientHeight*f.y,p=o*f.x,m=r*f.y;return{width:d,height:h,x:p,y:m}}function Dp(l,a,s){let r;if(a==="viewport")r=XS(l,s);else if(a==="document")r=$S(fl(l));else if(Fn(a))r=ZS(a,s);else{const o=Vy(l);r={x:a.x-o.x,y:a.y-o.y,width:a.width,height:a.height}}return Cu(r)}function Yy(l,a){const s=fa(l);return s===a||!Fn(s)||Ci(s)?!1:Yn(s).position==="fixed"||Yy(s,a)}function QS(l,a){const s=a.get(l);if(s)return s;let r=Vs(l,[],!1).filter(h=>Fn(h)&&Ui(h)!=="body"),o=null;const f=Yn(l).position==="fixed";let d=f?fa(l):l;for(;Fn(d)&&!Ci(d);){const h=Yn(d),p=ld(d);!p&&h.position==="fixed"&&(o=null),(f?!p&&!o:!p&&h.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Js(d)&&!p&&Yy(l,d))?r=r.filter(g=>g!==d):o=h,d=fa(d)}return a.set(l,r),r}function KS(l){let{element:a,boundary:s,rootBoundary:r,strategy:o}=l;const d=[...s==="clippingAncestors"?Hu(a)?[]:QS(a,this._c):[].concat(s),r],h=d[0],p=d.reduce((m,g)=>{const S=Dp(a,g,o);return m.top=Ma(S.top,m.top),m.right=Ri(S.right,m.right),m.bottom=Ri(S.bottom,m.bottom),m.left=Ma(S.left,m.left),m},Dp(a,h,o));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function JS(l){const{width:a,height:s}=qy(l);return{width:a,height:s}}function PS(l,a,s){const r=ol(a),o=fl(a),f=s==="fixed",d=Ba(l,!0,f,a);let h={scrollLeft:0,scrollTop:0};const p=rl(0);if(r||!r&&!f)if((Ui(a)!=="body"||Js(o))&&(h=qu(a)),r){const x=Ba(a,!0,f,a);p.x=x.x+a.clientLeft,p.y=x.y+a.clientTop}else o&&(p.x=sd(o));const m=o&&!r&&!f?Fy(o,h):rl(0),g=d.left+h.scrollLeft-p.x-m.x,S=d.top+h.scrollTop-p.y-m.y;return{x:g,y:S,width:d.width,height:d.height}}function yf(l){return Yn(l).position==="static"}function Np(l,a){if(!ol(l)||Yn(l).position==="fixed")return null;if(a)return a(l);let s=l.offsetParent;return fl(l)===s&&(s=s.ownerDocument.body),s}function Gy(l,a){const s=mn(l);if(Hu(l))return s;if(!ol(l)){let o=fa(l);for(;o&&!Ci(o);){if(Fn(o)&&!yf(o))return o;o=fa(o)}return s}let r=Np(l,a);for(;r&&HS(r)&&yf(r);)r=Np(r,a);return r&&Ci(r)&&yf(r)&&!ld(r)?s:r||qS(l)||s}const WS=async function(l){const a=this.getOffsetParent||Gy,s=this.getDimensions,r=await s(l.floating);return{reference:PS(l.reference,await a(l.floating),l.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function IS(l){return Yn(l).direction==="rtl"}const ex={convertOffsetParentRelativeRectToViewportRelativeRect:YS,getDocumentElement:fl,getClippingRect:KS,getOffsetParent:Gy,getElementRects:WS,getClientRects:GS,getDimensions:JS,getScale:Ti,isElement:Fn,isRTL:IS};function $y(l,a){return l.x===a.x&&l.y===a.y&&l.width===a.width&&l.height===a.height}function tx(l,a){let s=null,r;const o=fl(l);function f(){var h;clearTimeout(r),(h=s)==null||h.disconnect(),s=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),f();const m=l.getBoundingClientRect(),{left:g,top:S,width:x,height:R}=m;if(h||a(),!x||!R)return;const _=fu(S),N=fu(o.clientWidth-(g+x)),O=fu(o.clientHeight-(S+R)),E=fu(g),U={rootMargin:-_+"px "+-N+"px "+-O+"px "+-E+"px",threshold:Ma(0,Ri(1,p))||1};let G=!0;function k(te){const W=te[0].intersectionRatio;if(W!==p){if(!G)return d();W?d(!1,W):r=setTimeout(()=>{d(!1,1e-7)},1e3)}W===1&&!$y(m,l.getBoundingClientRect())&&d(),G=!1}try{s=new IntersectionObserver(k,{...U,root:o.ownerDocument})}catch{s=new IntersectionObserver(k,U)}s.observe(l)}return d(!0),f}function nx(l,a,s,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=r,m=id(l),g=o||f?[...m?Vs(m):[],...Vs(a)]:[];g.forEach(E=>{o&&E.addEventListener("scroll",s,{passive:!0}),f&&E.addEventListener("resize",s)});const S=m&&h?tx(m,s):null;let x=-1,R=null;d&&(R=new ResizeObserver(E=>{let[D]=E;D&&D.target===m&&R&&(R.unobserve(a),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var U;(U=R)==null||U.observe(a)})),s()}),m&&!p&&R.observe(m),R.observe(a));let _,N=p?Ba(l):null;p&&O();function O(){const E=Ba(l);N&&!$y(N,E)&&s(),N=E,_=requestAnimationFrame(O)}return s(),()=>{var E;g.forEach(D=>{o&&D.removeEventListener("scroll",s),f&&D.removeEventListener("resize",s)}),S==null||S(),(E=R)==null||E.disconnect(),R=null,p&&cancelAnimationFrame(_)}}const lx=kS,ax=BS,ix=LS,sx=MS,jp=(l,a,s)=>{const r=new Map,o={platform:ex,...s},f={...o.platform,_c:r};return US(l,a,{...o,platform:f})};var gf={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Up;function rx(){return Up||(Up=1,function(l){(function(){var a={}.hasOwnProperty;function s(){for(var f="",d=0;d<arguments.length;d++){var h=arguments[d];h&&(f=o(f,r(h)))}return f}function r(f){if(typeof f=="string"||typeof f=="number")return f;if(typeof f!="object")return"";if(Array.isArray(f))return s.apply(null,f);if(f.toString!==Object.prototype.toString&&!f.toString.toString().includes("[native code]"))return f.toString();var d="";for(var h in f)a.call(f,h)&&f[h]&&(d=o(d,h));return d}function o(f,d){return d?f?f+" "+d:f+d:f}l.exports?(s.default=s,l.exports=s):window.classNames=s})()}(gf)),gf.exports}var ux=rx();const jf=qf(ux);var Mp={};const ox="react-tooltip-core-styles",cx="react-tooltip-base-styles",Lp={core:!1,base:!1};function zp({css:l,id:a=cx,type:s="base",ref:r}){var o,f;if(!l||typeof document>"u"||Lp[s]||s==="core"&&typeof process<"u"&&(!((o=process==null?void 0:Mp)===null||o===void 0)&&o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||s!=="base"&&typeof process<"u"&&(!((f=process==null?void 0:Mp)===null||f===void 0)&&f.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;s==="core"&&(a=ox),r||(r={});const{insertAt:d}=r;if(document.getElementById(a))return;const h=document.head||document.getElementsByTagName("head")[0],p=document.createElement("style");p.id=a,p.type="text/css",d==="top"&&h.firstChild?h.insertBefore(p,h.firstChild):h.appendChild(p),p.styleSheet?p.styleSheet.cssText=l:p.appendChild(document.createTextNode(l)),Lp[s]=!0}const kp=async({elementReference:l=null,tooltipReference:a=null,tooltipArrowReference:s=null,place:r="top",offset:o=10,strategy:f="absolute",middlewares:d=[lx(Number(o)),ix({fallbackAxisSideDirection:"start"}),ax({padding:5})],border:h})=>{if(!l)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};if(a===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};const p=d;return s?(p.push(sx({element:s,padding:5})),jp(l,a,{placement:r,strategy:f,middleware:p}).then(({x:m,y:g,placement:S,middlewareData:x})=>{var R,_;const N={left:`${m}px`,top:`${g}px`,border:h},{x:O,y:E}=(R=x.arrow)!==null&&R!==void 0?R:{x:0,y:0},D=(_={top:"bottom",right:"left",bottom:"top",left:"right"}[S.split("-")[0]])!==null&&_!==void 0?_:"bottom",U=h&&{borderBottom:h,borderRight:h};let G=0;if(h){const k=`${h}`.match(/(\d+)px/);G=k!=null&&k[1]?Number(k[1]):1}return{tooltipStyles:N,tooltipArrowStyles:{left:O!=null?`${O}px`:"",top:E!=null?`${E}px`:"",right:"",bottom:"",...U,[D]:`-${4+G}px`},place:S}})):jp(l,a,{placement:"bottom",strategy:f,middleware:p}).then(({x:m,y:g,placement:S})=>({tooltipStyles:{left:`${m}px`,top:`${g}px`},tooltipArrowStyles:{},place:S}))},Bp=(l,a)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(l,a),Hp=(l,a,s)=>{let r=null;const o=function(...f){const d=()=>{r=null};!r&&(l.apply(this,f),r=setTimeout(d,a))};return o.cancel=()=>{r&&(clearTimeout(r),r=null)},o},qp=l=>l!==null&&!Array.isArray(l)&&typeof l=="object",Uf=(l,a)=>{if(l===a)return!0;if(Array.isArray(l)&&Array.isArray(a))return l.length===a.length&&l.every((o,f)=>Uf(o,a[f]));if(Array.isArray(l)!==Array.isArray(a))return!1;if(!qp(l)||!qp(a))return l===a;const s=Object.keys(l),r=Object.keys(a);return s.length===r.length&&s.every(o=>Uf(l[o],a[o]))},fx=l=>{if(!(l instanceof HTMLElement||l instanceof SVGElement))return!1;const a=getComputedStyle(l);return["overflow","overflow-x","overflow-y"].some(s=>{const r=a.getPropertyValue(s);return r==="auto"||r==="scroll"})},Vp=l=>{if(!l)return null;let a=l.parentElement;for(;a;){if(fx(a))return a;a=a.parentElement}return document.scrollingElement||document.documentElement},dx=typeof window<"u"?w.useLayoutEffect:w.useEffect,Rn=l=>{l.current&&(clearTimeout(l.current),l.current=null)},hx="DEFAULT_TOOLTIP_ID",mx={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},px=w.createContext({getTooltipData:()=>mx});function Xy(l=hx){return w.useContext(px).getTooltipData(l)}var _i={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},vf={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const yx=({forwardRef:l,id:a,className:s,classNameArrow:r,variant:o="dark",anchorId:f,anchorSelect:d,place:h="top",offset:p=10,events:m=["hover"],openOnClick:g=!1,positionStrategy:S="absolute",middlewares:x,wrapper:R,delayShow:_=0,delayHide:N=0,float:O=!1,hidden:E=!1,noArrow:D=!1,clickable:U=!1,closeOnEsc:G=!1,closeOnScroll:k=!1,closeOnResize:te=!1,openEvents:W,closeEvents:K,globalCloseEvents:ne,imperativeModeOnly:xe,style:oe,position:ae,afterShow:pe,afterHide:he,disableTooltip:we,content:L,contentWrapperRef:X,isOpen:I,defaultIsOpen:me=!1,setIsOpen:T,activeAnchor:Y,setActiveAnchor:re,border:le,opacity:ye,arrowColor:De,role:_e="tooltip"})=>{var it;const Re=w.useRef(null),St=w.useRef(null),_t=w.useRef(null),Dt=w.useRef(null),pn=w.useRef(null),[$t,Zn]=w.useState({tooltipStyles:{},tooltipArrowStyles:{},place:h}),[Nt,dl]=w.useState(!1),[ut,Dn]=w.useState(!1),[Ge,hl]=w.useState(null),ln=w.useRef(!1),C=w.useRef(null),{anchorRefs:H,setActiveAnchor:$}=Xy(a),ue=w.useRef(!1),[ee,P]=w.useState([]),ie=w.useRef(!1),be=g||m.includes("click"),We=be||(W==null?void 0:W.click)||(W==null?void 0:W.dblclick)||(W==null?void 0:W.mousedown),tt=W?{...W}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!W&&be&&Object.assign(tt,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const Nn=K?{...K}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!K&&be&&Object.assign(Nn,{mouseleave:!1,blur:!1,mouseout:!1});const pt=ne?{...ne}:{escape:G||!1,scroll:k||!1,resize:te||!1,clickOutsideAnchor:We||!1};xe&&(Object.assign(tt,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Nn,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(pt,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),dx(()=>(ie.current=!0,()=>{ie.current=!1}),[]);const Ve=se=>{ie.current&&(se&&Dn(!0),setTimeout(()=>{ie.current&&(T==null||T(se),I===void 0&&dl(se))},10))};w.useEffect(()=>{if(I===void 0)return()=>null;I&&Dn(!0);const se=setTimeout(()=>{dl(I)},10);return()=>{clearTimeout(se)}},[I]),w.useEffect(()=>{if(Nt!==ln.current)if(Rn(pn),ln.current=Nt,Nt)pe==null||pe();else{const se=(ce=>{const ge=ce.match(/^([\d.]+)(ms|s)$/);if(!ge)return 0;const[,Ie,yt]=ge;return Number(Ie)*(yt==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));pn.current=setTimeout(()=>{Dn(!1),hl(null),he==null||he()},se+25)}},[Nt]);const Xt=se=>{Zn(ce=>Uf(ce,se)?ce:se)},an=(se=_)=>{Rn(_t),ut?Ve(!0):_t.current=setTimeout(()=>{Ve(!0)},se)},Zt=(se=N)=>{Rn(Dt),Dt.current=setTimeout(()=>{ue.current||Ve(!1)},se)},Qn=se=>{var ce;if(!se)return;const ge=(ce=se.currentTarget)!==null&&ce!==void 0?ce:se.target;if(!(ge!=null&&ge.isConnected))return re(null),void $({current:null});_?an():Ve(!0),re(ge),$({current:ge}),Rn(Dt)},ml=()=>{U?Zt(N||100):N?Zt():Ve(!1),Rn(_t)},pl=({x:se,y:ce})=>{var ge;const Ie={getBoundingClientRect:()=>({x:se,y:ce,width:0,height:0,top:ce,left:se,right:se,bottom:ce})};kp({place:(ge=Ge==null?void 0:Ge.place)!==null&&ge!==void 0?ge:h,offset:p,elementReference:Ie,tooltipReference:Re.current,tooltipArrowReference:St.current,strategy:S,middlewares:x,border:le}).then(yt=>{Xt(yt)})},Kn=se=>{if(!se)return;const ce=se,ge={x:ce.clientX,y:ce.clientY};pl(ge),C.current=ge},jn=se=>{var ce;if(!Nt)return;const ge=se.target;ge.isConnected&&(!((ce=Re.current)===null||ce===void 0)&&ce.contains(ge)||[document.querySelector(`[id='${f}']`),...ee].some(Ie=>Ie==null?void 0:Ie.contains(ge))||(Ve(!1),Rn(_t)))},qa=Hp(Qn,50),ot=Hp(ml,50),qt=se=>{ot.cancel(),qa(se)},Se=()=>{qa.cancel(),ot()},je=w.useCallback(()=>{var se,ce;const ge=(se=Ge==null?void 0:Ge.position)!==null&&se!==void 0?se:ae;ge?pl(ge):O?C.current&&pl(C.current):Y!=null&&Y.isConnected&&kp({place:(ce=Ge==null?void 0:Ge.place)!==null&&ce!==void 0?ce:h,offset:p,elementReference:Y,tooltipReference:Re.current,tooltipArrowReference:St.current,strategy:S,middlewares:x,border:le}).then(Ie=>{ie.current&&Xt(Ie)})},[Nt,Y,L,oe,h,Ge==null?void 0:Ge.place,p,S,ae,Ge==null?void 0:Ge.position,O]);w.useEffect(()=>{var se,ce;const ge=new Set(H);ee.forEach(ze=>{we!=null&&we(ze)||ge.add({current:ze})});const Ie=document.querySelector(`[id='${f}']`);Ie&&!(we!=null&&we(Ie))&&ge.add({current:Ie});const yt=()=>{Ve(!1)},yn=Vp(Y),gn=Vp(Re.current);pt.scroll&&(window.addEventListener("scroll",yt),yn==null||yn.addEventListener("scroll",yt),gn==null||gn.addEventListener("scroll",yt));let At=null;pt.resize?window.addEventListener("resize",yt):Y&&Re.current&&(At=nx(Y,Re.current,je,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const Tt=ze=>{ze.key==="Escape"&&Ve(!1)};pt.escape&&window.addEventListener("keydown",Tt),pt.clickOutsideAnchor&&window.addEventListener("click",jn);const $e=[],sn=ze=>!!(ze!=null&&ze.target&&(Y!=null&&Y.contains(ze.target))),kl=ze=>{Nt&&sn(ze)||Qn(ze)},ha=ze=>{Nt&&sn(ze)&&ml()},gl=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],ct=["click","dblclick","mousedown","mouseup"];Object.entries(tt).forEach(([ze,Un])=>{Un&&(gl.includes(ze)?$e.push({event:ze,listener:qt}):ct.includes(ze)&&$e.push({event:ze,listener:kl}))}),Object.entries(Nn).forEach(([ze,Un])=>{Un&&(gl.includes(ze)?$e.push({event:ze,listener:Se}):ct.includes(ze)&&$e.push({event:ze,listener:ha}))}),O&&$e.push({event:"pointermove",listener:Kn});const zi=()=>{ue.current=!0},ki=()=>{ue.current=!1,ml()},Jn=U&&(Nn.mouseout||Nn.mouseleave);return Jn&&((se=Re.current)===null||se===void 0||se.addEventListener("mouseover",zi),(ce=Re.current)===null||ce===void 0||ce.addEventListener("mouseout",ki)),$e.forEach(({event:ze,listener:Un})=>{ge.forEach(Va=>{var Bl;(Bl=Va.current)===null||Bl===void 0||Bl.addEventListener(ze,Un)})}),()=>{var ze,Un;pt.scroll&&(window.removeEventListener("scroll",yt),yn==null||yn.removeEventListener("scroll",yt),gn==null||gn.removeEventListener("scroll",yt)),pt.resize?window.removeEventListener("resize",yt):At==null||At(),pt.clickOutsideAnchor&&window.removeEventListener("click",jn),pt.escape&&window.removeEventListener("keydown",Tt),Jn&&((ze=Re.current)===null||ze===void 0||ze.removeEventListener("mouseover",zi),(Un=Re.current)===null||Un===void 0||Un.removeEventListener("mouseout",ki)),$e.forEach(({event:Va,listener:Bl})=>{ge.forEach(Qu=>{var Hl;(Hl=Qu.current)===null||Hl===void 0||Hl.removeEventListener(Va,Bl)})})}},[Y,je,ut,H,ee,W,K,ne,be,_,N]),w.useEffect(()=>{var se,ce;let ge=(ce=(se=Ge==null?void 0:Ge.anchorSelect)!==null&&se!==void 0?se:d)!==null&&ce!==void 0?ce:"";!ge&&a&&(ge=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`);const Ie=new MutationObserver(yt=>{const yn=[],gn=[];yt.forEach(At=>{if(At.type==="attributes"&&At.attributeName==="data-tooltip-id"&&(At.target.getAttribute("data-tooltip-id")===a?yn.push(At.target):At.oldValue===a&&gn.push(At.target)),At.type==="childList"){if(Y){const Tt=[...At.removedNodes].filter($e=>$e.nodeType===1);if(ge)try{gn.push(...Tt.filter($e=>$e.matches(ge))),gn.push(...Tt.flatMap($e=>[...$e.querySelectorAll(ge)]))}catch{}Tt.some($e=>{var sn;return!!(!((sn=$e==null?void 0:$e.contains)===null||sn===void 0)&&sn.call($e,Y))&&(Dn(!1),Ve(!1),re(null),Rn(_t),Rn(Dt),!0)})}if(ge)try{const Tt=[...At.addedNodes].filter($e=>$e.nodeType===1);yn.push(...Tt.filter($e=>$e.matches(ge))),yn.push(...Tt.flatMap($e=>[...$e.querySelectorAll(ge)]))}catch{}}}),(yn.length||gn.length)&&P(At=>[...At.filter(Tt=>!gn.includes(Tt)),...yn])});return Ie.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{Ie.disconnect()}},[a,d,Ge==null?void 0:Ge.anchorSelect,Y]),w.useEffect(()=>{je()},[je]),w.useEffect(()=>{if(!(X!=null&&X.current))return()=>null;const se=new ResizeObserver(()=>{setTimeout(()=>je())});return se.observe(X.current),()=>{se.disconnect()}},[L,X==null?void 0:X.current]),w.useEffect(()=>{var se;const ce=document.querySelector(`[id='${f}']`),ge=[...ee,ce];Y&&ge.includes(Y)||re((se=ee[0])!==null&&se!==void 0?se:ce)},[f,ee,Y]),w.useEffect(()=>(me&&Ve(!0),()=>{Rn(_t),Rn(Dt)}),[]),w.useEffect(()=>{var se;let ce=(se=Ge==null?void 0:Ge.anchorSelect)!==null&&se!==void 0?se:d;if(!ce&&a&&(ce=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`),ce)try{const ge=Array.from(document.querySelectorAll(ce));P(ge)}catch{P([])}},[a,d,Ge==null?void 0:Ge.anchorSelect]),w.useEffect(()=>{_t.current&&(Rn(_t),an(_))},[_]);const Lt=(it=Ge==null?void 0:Ge.content)!==null&&it!==void 0?it:L,yl=Nt&&Object.keys($t.tooltipStyles).length>0;return w.useImperativeHandle(l,()=>({open:se=>{if(se!=null&&se.anchorSelect)try{document.querySelector(se.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${se.anchorSelect}" is not a valid CSS selector`)}hl(se??null),se!=null&&se.delay?an(se.delay):Ve(!0)},close:se=>{se!=null&&se.delay?Zt(se.delay):Ve(!1)},activeAnchor:Y,place:$t.place,isOpen:!!(ut&&!E&&Lt&&yl)})),ut&&!E&&Lt?Pe.createElement(R,{id:a,role:_e,className:jf("react-tooltip",_i.tooltip,vf.tooltip,vf[o],s,`react-tooltip__place-${$t.place}`,_i[yl?"show":"closing"],yl?"react-tooltip__show":"react-tooltip__closing",S==="fixed"&&_i.fixed,U&&_i.clickable),onTransitionEnd:se=>{Rn(pn),Nt||se.propertyName!=="opacity"||(Dn(!1),hl(null),he==null||he())},style:{...oe,...$t.tooltipStyles,opacity:ye!==void 0&&yl?ye:void 0},ref:Re},Lt,Pe.createElement(R,{className:jf("react-tooltip-arrow",_i.arrow,vf.arrow,r,D&&_i.noArrow),style:{...$t.tooltipArrowStyles,background:De?`linear-gradient(to right bottom, transparent 50%, ${De} 50%)`:void 0},ref:St})):null},gx=({content:l})=>Pe.createElement("span",{dangerouslySetInnerHTML:{__html:l}}),Zy=Pe.forwardRef(({id:l,anchorId:a,anchorSelect:s,content:r,html:o,render:f,className:d,classNameArrow:h,variant:p="dark",place:m="top",offset:g=10,wrapper:S="div",children:x=null,events:R=["hover"],openOnClick:_=!1,positionStrategy:N="absolute",middlewares:O,delayShow:E=0,delayHide:D=0,float:U=!1,hidden:G=!1,noArrow:k=!1,clickable:te=!1,closeOnEsc:W=!1,closeOnScroll:K=!1,closeOnResize:ne=!1,openEvents:xe,closeEvents:oe,globalCloseEvents:ae,imperativeModeOnly:pe=!1,style:he,position:we,isOpen:L,defaultIsOpen:X=!1,disableStyleInjection:I=!1,border:me,opacity:T,arrowColor:Y,setIsOpen:re,afterShow:le,afterHide:ye,disableTooltip:De,role:_e="tooltip"},it)=>{const[Re,St]=w.useState(r),[_t,Dt]=w.useState(o),[pn,$t]=w.useState(m),[Zn,Nt]=w.useState(p),[dl,ut]=w.useState(g),[Dn,Ge]=w.useState(E),[hl,ln]=w.useState(D),[C,H]=w.useState(U),[$,ue]=w.useState(G),[ee,P]=w.useState(S),[ie,be]=w.useState(R),[We,tt]=w.useState(N),[Nn,pt]=w.useState(null),[Ve,Xt]=w.useState(null),an=w.useRef(I),{anchorRefs:Zt,activeAnchor:Qn}=Xy(l),ml=ot=>ot==null?void 0:ot.getAttributeNames().reduce((qt,Se)=>{var je;return Se.startsWith("data-tooltip-")&&(qt[Se.replace(/^data-tooltip-/,"")]=(je=ot==null?void 0:ot.getAttribute(Se))!==null&&je!==void 0?je:null),qt},{}),pl=ot=>{const qt={place:Se=>{var je;$t((je=Se)!==null&&je!==void 0?je:m)},content:Se=>{St(Se??r)},html:Se=>{Dt(Se??o)},variant:Se=>{var je;Nt((je=Se)!==null&&je!==void 0?je:p)},offset:Se=>{ut(Se===null?g:Number(Se))},wrapper:Se=>{var je;P((je=Se)!==null&&je!==void 0?je:S)},events:Se=>{const je=Se==null?void 0:Se.split(" ");be(je??R)},"position-strategy":Se=>{var je;tt((je=Se)!==null&&je!==void 0?je:N)},"delay-show":Se=>{Ge(Se===null?E:Number(Se))},"delay-hide":Se=>{ln(Se===null?D:Number(Se))},float:Se=>{H(Se===null?U:Se==="true")},hidden:Se=>{ue(Se===null?G:Se==="true")},"class-name":Se=>{pt(Se)}};Object.values(qt).forEach(Se=>Se(null)),Object.entries(ot).forEach(([Se,je])=>{var Lt;(Lt=qt[Se])===null||Lt===void 0||Lt.call(qt,je)})};w.useEffect(()=>{St(r)},[r]),w.useEffect(()=>{Dt(o)},[o]),w.useEffect(()=>{$t(m)},[m]),w.useEffect(()=>{Nt(p)},[p]),w.useEffect(()=>{ut(g)},[g]),w.useEffect(()=>{Ge(E)},[E]),w.useEffect(()=>{ln(D)},[D]),w.useEffect(()=>{H(U)},[U]),w.useEffect(()=>{ue(G)},[G]),w.useEffect(()=>{tt(N)},[N]),w.useEffect(()=>{an.current!==I&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[I]),w.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:I==="core",disableBase:I}}))},[]),w.useEffect(()=>{var ot;const qt=new Set(Zt);let Se=s;if(!Se&&l&&(Se=`[data-tooltip-id='${l.replace(/'/g,"\\'")}']`),Se)try{document.querySelectorAll(Se).forEach(ce=>{qt.add({current:ce})})}catch{console.warn(`[react-tooltip] "${Se}" is not a valid CSS selector`)}const je=document.querySelector(`[id='${a}']`);if(je&&qt.add({current:je}),!qt.size)return()=>null;const Lt=(ot=Ve??je)!==null&&ot!==void 0?ot:Qn.current,yl=new MutationObserver(ce=>{ce.forEach(ge=>{var Ie;if(!Lt||ge.type!=="attributes"||!(!((Ie=ge.attributeName)===null||Ie===void 0)&&Ie.startsWith("data-tooltip-")))return;const yt=ml(Lt);pl(yt)})}),se={attributes:!0,childList:!1,subtree:!1};if(Lt){const ce=ml(Lt);pl(ce),yl.observe(Lt,se)}return()=>{yl.disconnect()}},[Zt,Qn,Ve,a,s]),w.useEffect(()=>{he!=null&&he.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),me&&!Bp("border",`${me}`)&&console.warn(`[react-tooltip] "${me}" is not a valid \`border\`.`),he!=null&&he.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),T&&!Bp("opacity",`${T}`)&&console.warn(`[react-tooltip] "${T}" is not a valid \`opacity\`.`)},[]);let Kn=x;const jn=w.useRef(null);if(f){const ot=f({content:(Ve==null?void 0:Ve.getAttribute("data-tooltip-content"))||Re||null,activeAnchor:Ve});Kn=ot?Pe.createElement("div",{ref:jn,className:"react-tooltip-content-wrapper"},ot):null}else Re&&(Kn=Re);_t&&(Kn=Pe.createElement(gx,{content:_t}));const qa={forwardRef:it,id:l,anchorId:a,anchorSelect:s,className:jf(d,Nn),classNameArrow:h,content:Kn,contentWrapperRef:jn,place:pn,variant:Zn,offset:dl,wrapper:ee,events:ie,openOnClick:_,positionStrategy:We,middlewares:O,delayShow:Dn,delayHide:hl,float:C,hidden:$,noArrow:k,clickable:te,closeOnEsc:W,closeOnScroll:K,closeOnResize:ne,openEvents:xe,closeEvents:oe,globalCloseEvents:ae,imperativeModeOnly:pe,style:he,position:we,isOpen:L,defaultIsOpen:X,border:me,opacity:T,arrowColor:Y,setIsOpen:re,afterShow:le,afterHide:ye,disableTooltip:De,activeAnchor:Ve,setActiveAnchor:ot=>Xt(ot),role:_e};return Pe.createElement(yx,{...qa})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",l=>{l.detail.disableCore||zp({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),l.detail.disableBase||zp({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});class vx{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map,this.isConnecting=!1,this.isConnected=!1}connect(){if(!(this.isConnecting||this.isConnected)){this.isConnecting=!0;try{const s=`${window.location.protocol==="https:"?"wss:":"ws:"}//127.0.0.1:8000/ws/predictions/`;this.socket=new WebSocket(s),this.socket.onopen=r=>{console.log("WebSocket connected:",r),this.isConnected=!0,this.isConnecting=!1,this.reconnectAttempts=0,this.send({type:"ping",message:"Connection test"}),this.notifyListeners("connection_established",{message:"Connected to real-time updates"})},this.socket.onmessage=r=>{try{const o=JSON.parse(r.data);console.log("WebSocket message received:",o),this.notifyListeners(o.type,o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.socket.onclose=r=>{console.log("WebSocket disconnected:",r),this.isConnected=!1,this.isConnecting=!1,this.socket=null,this.notifyListeners("connection_lost",{message:"Connection to real-time updates lost"}),r.code!==1e3&&this.reconnectAttempts<this.maxReconnectAttempts&&this.scheduleReconnect()},this.socket.onerror=r=>{console.error("WebSocket error:",r),this.isConnecting=!1,this.notifyListeners("connection_error",{message:"Failed to connect to real-time updates",error:r})}}catch(a){console.error("Failed to create WebSocket connection:",a),this.isConnecting=!1}}}disconnect(){this.socket&&(this.socket.close(1e3,"Intentional disconnect"),this.socket=null),this.isConnected=!1,this.isConnecting=!1,this.reconnectAttempts=0}scheduleReconnect(){this.reconnectAttempts++,console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{!this.isConnected&&!this.isConnecting&&this.connect()},this.reconnectInterval)}send(a){this.socket&&this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(a)):console.warn("WebSocket is not connected. Message not sent:",a)}addEventListener(a,s){this.listeners.has(a)||this.listeners.set(a,[]),this.listeners.get(a).push(s)}removeEventListener(a,s){if(this.listeners.has(a)){const r=this.listeners.get(a),o=r.indexOf(s);o>-1&&r.splice(o,1)}}notifyListeners(a,s){this.listeners.has(a)&&this.listeners.get(a).forEach(r=>{try{r(s)}catch(o){console.error(`Error in WebSocket listener for ${a}:`,o)}})}getConnectionStatus(){return{isConnected:this.isConnected,isConnecting:this.isConnecting,reconnectAttempts:this.reconnectAttempts}}onDatasetUploaded(a){this.addEventListener("dataset_uploaded",a)}onPredictionStarted(a){this.addEventListener("prediction_started",a)}onPredictionCompleted(a){this.addEventListener("prediction_completed",a)}onPredictionDataUpdated(a){this.addEventListener("prediction_data_updated",a)}onPredictionError(a){this.addEventListener("prediction_error",a)}onConnectionEstablished(a){this.addEventListener("connection_established",a)}onConnectionLost(a){this.addEventListener("connection_lost",a)}onConnectionError(a){this.addEventListener("connection_error",a)}onUserCreated(a){this.addEventListener("user_created",a)}onUserUpdated(a){this.addEventListener("user_updated",a)}onUserStatusChanged(a){this.addEventListener("user_status_changed",a)}onUserDeleted(a){this.addEventListener("user_deleted",a)}onProfileUpdated(a){this.addEventListener("profile_updated",a)}}const ke=new vx;function rd({showModal:l}){const a=$n(),s=cl(),[r,o]=w.useState({profile:JSON.parse(sessionStorage.getItem("current_user"))?JSON.parse(sessionStorage.getItem("current_user")).profile_picture:null});w.useEffect(()=>{console.log("NavBar: Initial userProfile state:",r)},[]),w.useEffect(()=>{console.log("NavBar: userProfile state changed:",r)},[r]);const[f,d]=w.useState(sessionStorage.getItem("current_user")?JSON.parse(sessionStorage.getItem("current_user")).is_superuser?"Admin":"User":"Guest"),[h,p]=w.useState(".short-term"),[m,g]=w.useState(!1),S=()=>{console.log("NavBar: updateUserProfile called");const N=JSON.parse(sessionStorage.getItem("current_user"));console.log("NavBar: Current user from session storage:",N),N&&(console.log("NavBar: Updating profile state with:",N.profile_picture),o({profile:N.profile_picture}),d(N.is_superuser?"Admin":"User"))};w.useEffect(()=>{const N=JSON.parse(sessionStorage.getItem("current_user"));if(!N)return;const O=N.id;!ke.getConnectionStatus().isConnected&&!ke.getConnectionStatus().isConnecting&&(console.log("NavBar: Connecting to WebSocket..."),ke.connect());const E=k=>{console.log("NavBar: Profile updated via WebSocket:",k),k.user_id===O&&S()},D=k=>{console.log("NavBar: User updated via WebSocket:",k),k.user_id===O&&S()};ke.onProfileUpdated(E),ke.onUserUpdated(D);const U=k=>{console.log("NavBar: Storage change detected:",k.key),k.key==="current_user"&&(console.log("NavBar: current_user changed in storage, updating profile"),S())};window.addEventListener("storage",U);const G=()=>{console.log("NavBar: Custom storage change event received"),S()};return window.addEventListener("userProfileUpdated",G),()=>{ke.removeEventListener("profile_updated",E),ke.removeEventListener("user_updated",D),window.removeEventListener("storage",U),window.removeEventListener("userProfileUpdated",G)}},[]);const x=N=>{window.scrollTo({top:document.querySelector(N).offsetTop,behavior:"smooth"})},R=document.querySelectorAll("section.short-term, section.mid-term, section.long-term");window.addEventListener("scroll",()=>{let N="";R.forEach(O=>{const E=O.offsetTop,D=O.clientHeight;window.scrollY>=E-D/2&&(N=O.getAttribute("id"))}),p(`.${N}`)});const _=()=>{On.logout(),a("/")};return v.jsxs("nav",{children:[v.jsx(Zy,{id:"nav-menu-tooltip",place:"top",effect:"solid",className:"tooltip"}),v.jsxs("ul",{children:[v.jsx("li",{className:"system-icon",children:v.jsx("img",{src:AS,alt:"system-icon"})}),v.jsx("li",{className:h===".short-term"&&s.pathname.startsWith("/home")?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".short-term"),x("#short-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"1 year ahead","data-tooltip-offset":30,onClick:()=>{p(".short-term"),x("#short-term")},children:"Short-Term"})}),v.jsx("li",{className:h===".mid-term"?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".mid-term"),x("#mid-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"3 years ahead","data-tooltip-offset":30,onClick:()=>{p(".mid-term"),x("#mid-term")},children:"Mid-Term"})}),v.jsx("li",{className:h===".long-term"?"active":"",onClick:()=>{(s.pathname.startsWith("/user-management")||s.pathname.startsWith("/account"))&&a("/home"),p(".long-term"),x("#long-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"5 years ahead","data-tooltip-offset":30,onClick:()=>{p(".long-term"),x("#long-term")},children:"Long-Term"})}),f=="Admin"||f=="User"?v.jsxs(v.Fragment,{children:[v.jsx("li",{onClick:l,children:"Upload Dataset"}),v.jsxs("li",{children:[v.jsx("img",{src:r.profile?`http://127.0.0.1:8000${r.profile}`:Tu,alt:"sample-profile",onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),className:s.pathname.includes("/user-management")||s.pathname.includes("/account")?"active":""}),m&&v.jsx("div",{className:`profile-menu-container ${f}`,onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:v.jsxs("div",{className:"profile-menu",children:[v.jsx("li",{onClick:()=>{a("/account"),p("profile")},className:s.pathname.startsWith("/account")?"active":"",children:"Edit Account Details"}),JSON.parse(sessionStorage.getItem("current_user")).is_superuser&&v.jsx("li",{onClick:()=>{a("/user-management"),p("profile")},className:s.pathname.startsWith("/user-management")?"active":"",children:"User Management"}),v.jsx("li",{onClick:_,children:"Log Out"})]})})]})]}):v.jsx("button",{disabled:"disabled",className:"view-as-guest",children:"View as Guest"})]})]})}const Du="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%235a6576'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",bx="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2024%2024'%20id='Upload'%3e%3cpath%20d='M8.71,7.71,11,5.41V15a1,1,0,0,0,2,0V5.41l2.29,2.3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42l-4-4a1,1,0,0,0-.33-.21,1,1,0,0,0-.76,0,1,1,0,0,0-.33.21l-4,4A1,1,0,1,0,8.71,7.71ZM21,12a1,1,0,0,0-1,1v6a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V13a1,1,0,0,0-2,0v6a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V13A1,1,0,0,0,21,12Z'%20fill='%234f46e5'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",Sx="http://127.0.0.1:8000/dataset_validation/validate/";class xx{async validate(a){const s=new FormData;s.append("file",a);try{const r=await fetch(Sx,{method:"POST",body:s});return r.ok?await r.json():await r.json()}catch(r){console.log("Failed to validate dataset!",r)}}}const Ex=new xx,wx="http://127.0.0.1:8000/dataset/";class _x{async postDataset(a){const s=new FormData,r=await On.getCurrentUser();s.append("file",a),s.append("uploaded_by_user",r.id);try{const o=await fetch(wx,{method:"POST",headers:this.getAuthHeader(),body:s});return o.ok?await o.json():await o.json()}catch(o){console.log("Failed to validate dataset!",o)}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const Ax=new _x,bf="http://127.0.0.1:8000/predictions/";class Tx{async generatePredictions(a){try{const s=await fetch(`${bf}generate/`,{method:"POST",headers:{...this.getAuthHeader(),"Content-Type":"application/json"},body:JSON.stringify({dataset_id:a})});if(!s.ok){const o=await s.json();throw new Error(o.error||"Failed to generate predictions")}return await s.json()}catch(s){throw console.error("Failed to generate predictions:",s),s}}async getPredictionsByDataset(a){try{const s=await fetch(`${bf}by_dataset/?dataset_id=${a}`,{method:"GET",headers:this.getAuthHeader()});if(!s.ok){const o=await s.json();throw new Error(o.error||"Failed to fetch predictions")}return await s.json()}catch(s){throw console.error("Failed to fetch predictions:",s),s}}async getLatestTrends(){try{let a=[],s=[],r=[];const o=await fetch(`${bf}latest_trends/`,{method:"GET"});if(!o.ok){const h=await o.json();throw new Error(h.error||"Failed to fetch latest trends")}const f=await o.json();f.forEach(h=>{h.category=="growth_rate"?a.push(h):h.category=="revenue"?s.push(h):h.category=="least_crowded"&&r.push(h)});const d=[...new Set(f.map(h=>h.prediction_result.year))];return[a,s,r,d.sort((h,p)=>h-p)]}catch(a){throw console.error("Failed to fetch latest trends:",a),a}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const Qy=new Tx,Rx="/assets/analysis-chart-DyDYoyz_.gif";function Ox({showModal:l,onPredictionComplete:a}){const[s,r]=w.useState(!1),[o,f]=w.useState(null),[d,h]=w.useState(String),[p,m]=w.useState(""),[g,S]=w.useState(!1),[x,R]=w.useState([]),[_,N]=w.useState(!1);w.useEffect(()=>{const G=K=>{console.log("Dataset uploaded via WebSocket:",K),R(ne=>[...ne,`Dataset uploaded: ${K.message}`])},k=K=>{console.log("Prediction started via WebSocket:",K),m(K.message),R(ne=>[...ne,`Progress: ${K.message}`])},te=K=>{console.log("Prediction completed via WebSocket:",K),S(!0),m(K.message),R(ne=>[...ne,`Completed: ${K.message}`]),setTimeout(()=>{S(!1),l()},2e3)},W=K=>{console.log("Prediction error via WebSocket:",K),m(`Error: ${K.message}`),R(ne=>[...ne,`Error: ${K.error}`]),N(!1)};return ke.onDatasetUploaded(G),ke.onPredictionStarted(k),ke.onPredictionCompleted(te),ke.onPredictionError(W),()=>{ke.removeEventListener("dataset_uploaded",G),ke.removeEventListener("prediction_started",k),ke.removeEventListener("prediction_completed",te),ke.removeEventListener("prediction_error",W)}},[l]);const O=v.jsxs("div",{className:"dataset-requirements",children:["Must include exactly the following columns and data types:",v.jsx("br",{}),v.jsx("br",{}),v.jsxs("table",{children:[v.jsxs("tr",{children:[v.jsx("th",{children:"Column Name"}),v.jsx("th",{children:"Data Type"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Year"}),v.jsx("td",{children:"Integer"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Industry Sector"}),v.jsx("td",{children:"String"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Number of Businesses"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Revenue (PHP Millions)"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Growth Rate (%)"}),v.jsx("td",{children:"Number"})]})]})]}),E=G=>{if(G.target.files.length>0){const k=G.target.files[0];f(k)}else f(null)},D=G=>{h(null),G.preventDefault(),G.stopPropagation(),document.getElementById("file").value="",f(null)},U=async()=>{S(!1),N(!0);try{const G=await Ex.validate(o);if(h(G),console.log(G),G.valid){m("Uploading dataset...");const k=await Ax.postDataset(o);if(k.id){m("Generating predictions... This may take a few moments.");const te=await Qy.generatePredictions(k.id);if(te.success)S(!0),m(`Successfully generated ${te.predictions_count} predictions for your dataset!`),h({success:!0,valid:!0,message:`Successfully generated ${te.predictions_count} predictions for your dataset!`}),a&&a(k.id,te),setTimeout(()=>{S(!1),l()},2e3);else throw new Error("Failed to generate predictions")}else throw new Error("Failed to upload dataset")}}catch(G){console.error("Error in submission process:",G),h({valid:!1,message:`Error: ${G.message||"An unexpected error occurred"}`}),m("")}finally{N(!1)}};return v.jsxs("div",{className:"upload-dataset-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsx(Zy,{id:"dataset-required-info",place:"bottom",effect:"solid",className:"tooltip",children:O}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),onClick:l,children:v.jsx("img",{src:s?ku:Du,alt:"close-icon"})}),v.jsx("h2",{children:"Upload Dataset"}),v.jsxs("p",{className:"reminder",children:["Please make sure the dataset is correct and complete to avoid inaccurate result. ","",v.jsx("a",{"data-tooltip-id":"dataset-required-info","data-tooltip-offset":10,children:"Hover to see Dataset Requirements."})]}),p&&v.jsxs("div",{className:`current-step ${g?"success":"processing"}`,children:[v.jsx("img",{src:Rx,alt:""}),v.jsx("p",{children:p})]}),d&&d.valid===!1&&v.jsx("div",{className:`response-message ${d.valid?"success":"error"}`,children:d.message}),p===""&&v.jsxs("section",{children:[v.jsx("input",{type:"file",name:"file",id:"file",accept:".csv",style:{display:"none"},onChange:E}),v.jsxs("label",{htmlFor:"file",children:[v.jsx("img",{src:bx,alt:"upload-icon",className:"upload-icon"}),v.jsxs("p",{children:[v.jsx("span",{children:"Click here"})," to upload your file."]}),v.jsx("p",{className:"supported-format",children:"Supported Format: csv"}),o&&v.jsxs("div",{className:"selected-file",children:[v.jsx("p",{children:o.name}),v.jsx("img",{src:Du,alt:"remove",className:"remove-icon",onClick:D})]})]})]}),v.jsxs("button",{disabled:o===null||_,className:"submit-button",onClick:U,children:[_&&v.jsx(ca,{}),_?"Processing...":"Make Prediction"]})]})]})}function Ft({color:l=null,data:a,topNumber:s,type:r,filterResult:o}){const[f,d]=w.useState(!1);w.useEffect(()=>{s!=1&&f?document.getElementById(`card-top-1-${r}`).classList.remove("active"):s!=1&&!f&&document.getElementById(`card-top-1-${r}`).classList.add("active")},[f]);const h=p=>p>=1e9?(p/1e9).toFixed(2)+" billiion":p>=1e6?(p/1e6).toFixed(2)+" million":p.toLocaleString();return v.jsxs("article",{id:`card-top-${s}-${r}`,className:`card-top-${s} ${s==1?"active":""} ${l??""}`,onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),children:[v.jsx("div",{className:`circle ${s==1?"active":""} ${l??""}`}),v.jsxs("div",{className:`info-container ${s==1?"active":""} ${l??""}`,children:[v.jsx("h1",{children:s}),v.jsxs("div",{className:"paragraph-container",children:[o=="Growing Industry Sector"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to achieve a growth rate of"," ",a?a.prediction_result.predicted_growth_rate:"___","%, based on current historical trend analysis."]}),o=="Industry Sector Revenue"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is expected to generate approximately ₱",a?h(Number(a.prediction_result.predicted_revenue)):"___"," ","in revenue, based on the current historical trend analysis."]})]}),o=="Least Crowded"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to be the least crowded industry, with an estimated"," ",a?h(a.prediction_result.predicted_least_crowded):"___"," ","businesses in operation, based on the current historical trend analysis."]}),v.jsx("h1",{className:`industry ${s==1?"active":""}`,children:a?a.prediction_result.industry_sector:"___"})]})]})}function Ky(l,a){return function(){return l.apply(a,arguments)}}const{toString:Cx}=Object.prototype,{getPrototypeOf:ud}=Object,{iterator:Vu,toStringTag:Jy}=Symbol,Fu=(l=>a=>{const s=Cx.call(a);return l[s]||(l[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Xn=l=>(l=l.toLowerCase(),a=>Fu(a)===l),Yu=l=>a=>typeof a===l,{isArray:Mi}=Array,Fs=Yu("undefined");function Dx(l){return l!==null&&!Fs(l)&&l.constructor!==null&&!Fs(l.constructor)&&tn(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const Py=Xn("ArrayBuffer");function Nx(l){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(l):a=l&&l.buffer&&Py(l.buffer),a}const jx=Yu("string"),tn=Yu("function"),Wy=Yu("number"),Gu=l=>l!==null&&typeof l=="object",Ux=l=>l===!0||l===!1,gu=l=>{if(Fu(l)!=="object")return!1;const a=ud(l);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Jy in l)&&!(Vu in l)},Mx=Xn("Date"),Lx=Xn("File"),zx=Xn("Blob"),kx=Xn("FileList"),Bx=l=>Gu(l)&&tn(l.pipe),Hx=l=>{let a;return l&&(typeof FormData=="function"&&l instanceof FormData||tn(l.append)&&((a=Fu(l))==="formdata"||a==="object"&&tn(l.toString)&&l.toString()==="[object FormData]"))},qx=Xn("URLSearchParams"),[Vx,Fx,Yx,Gx]=["ReadableStream","Request","Response","Headers"].map(Xn),$x=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ps(l,a,{allOwnKeys:s=!1}={}){if(l===null||typeof l>"u")return;let r,o;if(typeof l!="object"&&(l=[l]),Mi(l))for(r=0,o=l.length;r<o;r++)a.call(null,l[r],r,l);else{const f=s?Object.getOwnPropertyNames(l):Object.keys(l),d=f.length;let h;for(r=0;r<d;r++)h=f[r],a.call(null,l[h],h,l)}}function Iy(l,a){a=a.toLowerCase();const s=Object.keys(l);let r=s.length,o;for(;r-- >0;)if(o=s[r],a===o.toLowerCase())return o;return null}const ja=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,eg=l=>!Fs(l)&&l!==ja;function Mf(){const{caseless:l}=eg(this)&&this||{},a={},s=(r,o)=>{const f=l&&Iy(a,o)||o;gu(a[f])&&gu(r)?a[f]=Mf(a[f],r):gu(r)?a[f]=Mf({},r):Mi(r)?a[f]=r.slice():a[f]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Ps(arguments[r],s);return a}const Xx=(l,a,s,{allOwnKeys:r}={})=>(Ps(a,(o,f)=>{s&&tn(o)?l[f]=Ky(o,s):l[f]=o},{allOwnKeys:r}),l),Zx=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),Qx=(l,a,s,r)=>{l.prototype=Object.create(a.prototype,r),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:a.prototype}),s&&Object.assign(l.prototype,s)},Kx=(l,a,s,r)=>{let o,f,d;const h={};if(a=a||{},l==null)return a;do{for(o=Object.getOwnPropertyNames(l),f=o.length;f-- >0;)d=o[f],(!r||r(d,l,a))&&!h[d]&&(a[d]=l[d],h[d]=!0);l=s!==!1&&ud(l)}while(l&&(!s||s(l,a))&&l!==Object.prototype);return a},Jx=(l,a,s)=>{l=String(l),(s===void 0||s>l.length)&&(s=l.length),s-=a.length;const r=l.indexOf(a,s);return r!==-1&&r===s},Px=l=>{if(!l)return null;if(Mi(l))return l;let a=l.length;if(!Wy(a))return null;const s=new Array(a);for(;a-- >0;)s[a]=l[a];return s},Wx=(l=>a=>l&&a instanceof l)(typeof Uint8Array<"u"&&ud(Uint8Array)),Ix=(l,a)=>{const r=(l&&l[Vu]).call(l);let o;for(;(o=r.next())&&!o.done;){const f=o.value;a.call(l,f[0],f[1])}},eE=(l,a)=>{let s;const r=[];for(;(s=l.exec(a))!==null;)r.push(s);return r},tE=Xn("HTMLFormElement"),nE=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,o){return r.toUpperCase()+o}),Fp=(({hasOwnProperty:l})=>(a,s)=>l.call(a,s))(Object.prototype),lE=Xn("RegExp"),tg=(l,a)=>{const s=Object.getOwnPropertyDescriptors(l),r={};Ps(s,(o,f)=>{let d;(d=a(o,f,l))!==!1&&(r[f]=d||o)}),Object.defineProperties(l,r)},aE=l=>{tg(l,(a,s)=>{if(tn(l)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=l[s];if(tn(r)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},iE=(l,a)=>{const s={},r=o=>{o.forEach(f=>{s[f]=!0})};return Mi(l)?r(l):r(String(l).split(a)),s},sE=()=>{},rE=(l,a)=>l!=null&&Number.isFinite(l=+l)?l:a;function uE(l){return!!(l&&tn(l.append)&&l[Jy]==="FormData"&&l[Vu])}const oE=l=>{const a=new Array(10),s=(r,o)=>{if(Gu(r)){if(a.indexOf(r)>=0)return;if(!("toJSON"in r)){a[o]=r;const f=Mi(r)?[]:{};return Ps(r,(d,h)=>{const p=s(d,o+1);!Fs(p)&&(f[h]=p)}),a[o]=void 0,f}}return r};return s(l,0)},cE=Xn("AsyncFunction"),fE=l=>l&&(Gu(l)||tn(l))&&tn(l.then)&&tn(l.catch),ng=((l,a)=>l?setImmediate:a?((s,r)=>(ja.addEventListener("message",({source:o,data:f})=>{o===ja&&f===s&&r.length&&r.shift()()},!1),o=>{r.push(o),ja.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",tn(ja.postMessage)),dE=typeof queueMicrotask<"u"?queueMicrotask.bind(ja):typeof process<"u"&&process.nextTick||ng,hE=l=>l!=null&&tn(l[Vu]),F={isArray:Mi,isArrayBuffer:Py,isBuffer:Dx,isFormData:Hx,isArrayBufferView:Nx,isString:jx,isNumber:Wy,isBoolean:Ux,isObject:Gu,isPlainObject:gu,isReadableStream:Vx,isRequest:Fx,isResponse:Yx,isHeaders:Gx,isUndefined:Fs,isDate:Mx,isFile:Lx,isBlob:zx,isRegExp:lE,isFunction:tn,isStream:Bx,isURLSearchParams:qx,isTypedArray:Wx,isFileList:kx,forEach:Ps,merge:Mf,extend:Xx,trim:$x,stripBOM:Zx,inherits:Qx,toFlatObject:Kx,kindOf:Fu,kindOfTest:Xn,endsWith:Jx,toArray:Px,forEachEntry:Ix,matchAll:eE,isHTMLForm:tE,hasOwnProperty:Fp,hasOwnProp:Fp,reduceDescriptors:tg,freezeMethods:aE,toObjectSet:iE,toCamelCase:nE,noop:sE,toFiniteNumber:rE,findKey:Iy,global:ja,isContextDefined:eg,isSpecCompliantForm:uE,toJSONObject:oE,isAsyncFn:cE,isThenable:fE,setImmediate:ng,asap:dE,isIterable:hE};function Oe(l,a,s,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",a&&(this.code=a),s&&(this.config=s),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}F.inherits(Oe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:F.toJSONObject(this.config),code:this.code,status:this.status}}});const lg=Oe.prototype,ag={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{ag[l]={value:l}});Object.defineProperties(Oe,ag);Object.defineProperty(lg,"isAxiosError",{value:!0});Oe.from=(l,a,s,r,o,f)=>{const d=Object.create(lg);return F.toFlatObject(l,d,function(p){return p!==Error.prototype},h=>h!=="isAxiosError"),Oe.call(d,l.message,a,s,r,o),d.cause=l,d.name=l.name,f&&Object.assign(d,f),d};const mE=null;function Lf(l){return F.isPlainObject(l)||F.isArray(l)}function ig(l){return F.endsWith(l,"[]")?l.slice(0,-2):l}function Yp(l,a,s){return l?l.concat(a).map(function(o,f){return o=ig(o),!s&&f?"["+o+"]":o}).join(s?".":""):a}function pE(l){return F.isArray(l)&&!l.some(Lf)}const yE=F.toFlatObject(F,{},null,function(a){return/^is[A-Z]/.test(a)});function $u(l,a,s){if(!F.isObject(l))throw new TypeError("target must be an object");a=a||new FormData,s=F.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(N,O){return!F.isUndefined(O[N])});const r=s.metaTokens,o=s.visitor||g,f=s.dots,d=s.indexes,p=(s.Blob||typeof Blob<"u"&&Blob)&&F.isSpecCompliantForm(a);if(!F.isFunction(o))throw new TypeError("visitor must be a function");function m(_){if(_===null)return"";if(F.isDate(_))return _.toISOString();if(!p&&F.isBlob(_))throw new Oe("Blob is not supported. Use a Buffer instead.");return F.isArrayBuffer(_)||F.isTypedArray(_)?p&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function g(_,N,O){let E=_;if(_&&!O&&typeof _=="object"){if(F.endsWith(N,"{}"))N=r?N:N.slice(0,-2),_=JSON.stringify(_);else if(F.isArray(_)&&pE(_)||(F.isFileList(_)||F.endsWith(N,"[]"))&&(E=F.toArray(_)))return N=ig(N),E.forEach(function(U,G){!(F.isUndefined(U)||U===null)&&a.append(d===!0?Yp([N],G,f):d===null?N:N+"[]",m(U))}),!1}return Lf(_)?!0:(a.append(Yp(O,N,f),m(_)),!1)}const S=[],x=Object.assign(yE,{defaultVisitor:g,convertValue:m,isVisitable:Lf});function R(_,N){if(!F.isUndefined(_)){if(S.indexOf(_)!==-1)throw Error("Circular reference detected in "+N.join("."));S.push(_),F.forEach(_,function(E,D){(!(F.isUndefined(E)||E===null)&&o.call(a,E,F.isString(D)?D.trim():D,N,x))===!0&&R(E,N?N.concat(D):[D])}),S.pop()}}if(!F.isObject(l))throw new TypeError("data must be an object");return R(l),a}function Gp(l){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(r){return a[r]})}function od(l,a){this._pairs=[],l&&$u(l,this,a)}const sg=od.prototype;sg.append=function(a,s){this._pairs.push([a,s])};sg.toString=function(a){const s=a?function(r){return a.call(this,r,Gp)}:Gp;return this._pairs.map(function(o){return s(o[0])+"="+s(o[1])},"").join("&")};function gE(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rg(l,a,s){if(!a)return l;const r=s&&s.encode||gE;F.isFunction(s)&&(s={serialize:s});const o=s&&s.serialize;let f;if(o?f=o(a,s):f=F.isURLSearchParams(a)?a.toString():new od(a,s).toString(r),f){const d=l.indexOf("#");d!==-1&&(l=l.slice(0,d)),l+=(l.indexOf("?")===-1?"?":"&")+f}return l}class $p{constructor(){this.handlers=[]}use(a,s,r){return this.handlers.push({fulfilled:a,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){F.forEach(this.handlers,function(r){r!==null&&a(r)})}}const ug={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},vE=typeof URLSearchParams<"u"?URLSearchParams:od,bE=typeof FormData<"u"?FormData:null,SE=typeof Blob<"u"?Blob:null,xE={isBrowser:!0,classes:{URLSearchParams:vE,FormData:bE,Blob:SE},protocols:["http","https","file","blob","url","data"]},cd=typeof window<"u"&&typeof document<"u",zf=typeof navigator=="object"&&navigator||void 0,EE=cd&&(!zf||["ReactNative","NativeScript","NS"].indexOf(zf.product)<0),wE=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",_E=cd&&window.location.href||"http://localhost",AE=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:cd,hasStandardBrowserEnv:EE,hasStandardBrowserWebWorkerEnv:wE,navigator:zf,origin:_E},Symbol.toStringTag,{value:"Module"})),Ht={...AE,...xE};function TE(l,a){return $u(l,new Ht.classes.URLSearchParams,Object.assign({visitor:function(s,r,o,f){return Ht.isNode&&F.isBuffer(s)?(this.append(r,s.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},a))}function RE(l){return F.matchAll(/\w+|\[(\w*)]/g,l).map(a=>a[0]==="[]"?"":a[1]||a[0])}function OE(l){const a={},s=Object.keys(l);let r;const o=s.length;let f;for(r=0;r<o;r++)f=s[r],a[f]=l[f];return a}function og(l){function a(s,r,o,f){let d=s[f++];if(d==="__proto__")return!0;const h=Number.isFinite(+d),p=f>=s.length;return d=!d&&F.isArray(o)?o.length:d,p?(F.hasOwnProp(o,d)?o[d]=[o[d],r]:o[d]=r,!h):((!o[d]||!F.isObject(o[d]))&&(o[d]=[]),a(s,r,o[d],f)&&F.isArray(o[d])&&(o[d]=OE(o[d])),!h)}if(F.isFormData(l)&&F.isFunction(l.entries)){const s={};return F.forEachEntry(l,(r,o)=>{a(RE(r),o,s,0)}),s}return null}function CE(l,a,s){if(F.isString(l))try{return(a||JSON.parse)(l),F.trim(l)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(l)}const Ws={transitional:ug,adapter:["xhr","http","fetch"],transformRequest:[function(a,s){const r=s.getContentType()||"",o=r.indexOf("application/json")>-1,f=F.isObject(a);if(f&&F.isHTMLForm(a)&&(a=new FormData(a)),F.isFormData(a))return o?JSON.stringify(og(a)):a;if(F.isArrayBuffer(a)||F.isBuffer(a)||F.isStream(a)||F.isFile(a)||F.isBlob(a)||F.isReadableStream(a))return a;if(F.isArrayBufferView(a))return a.buffer;if(F.isURLSearchParams(a))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let h;if(f){if(r.indexOf("application/x-www-form-urlencoded")>-1)return TE(a,this.formSerializer).toString();if((h=F.isFileList(a))||r.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return $u(h?{"files[]":a}:a,p&&new p,this.formSerializer)}}return f||o?(s.setContentType("application/json",!1),CE(a)):a}],transformResponse:[function(a){const s=this.transitional||Ws.transitional,r=s&&s.forcedJSONParsing,o=this.responseType==="json";if(F.isResponse(a)||F.isReadableStream(a))return a;if(a&&F.isString(a)&&(r&&!this.responseType||o)){const d=!(s&&s.silentJSONParsing)&&o;try{return JSON.parse(a)}catch(h){if(d)throw h.name==="SyntaxError"?Oe.from(h,Oe.ERR_BAD_RESPONSE,this,null,this.response):h}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ht.classes.FormData,Blob:Ht.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};F.forEach(["delete","get","head","post","put","patch"],l=>{Ws.headers[l]={}});const DE=F.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),NE=l=>{const a={};let s,r,o;return l&&l.split(`
`).forEach(function(d){o=d.indexOf(":"),s=d.substring(0,o).trim().toLowerCase(),r=d.substring(o+1).trim(),!(!s||a[s]&&DE[s])&&(s==="set-cookie"?a[s]?a[s].push(r):a[s]=[r]:a[s]=a[s]?a[s]+", "+r:r)}),a},Xp=Symbol("internals");function zs(l){return l&&String(l).trim().toLowerCase()}function vu(l){return l===!1||l==null?l:F.isArray(l)?l.map(vu):String(l)}function jE(l){const a=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(l);)a[r[1]]=r[2];return a}const UE=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function Sf(l,a,s,r,o){if(F.isFunction(r))return r.call(this,a,s);if(o&&(a=s),!!F.isString(a)){if(F.isString(r))return a.indexOf(r)!==-1;if(F.isRegExp(r))return r.test(a)}}function ME(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,s,r)=>s.toUpperCase()+r)}function LE(l,a){const s=F.toCamelCase(" "+a);["get","set","has"].forEach(r=>{Object.defineProperty(l,r+s,{value:function(o,f,d){return this[r].call(this,a,o,f,d)},configurable:!0})})}let nn=class{constructor(a){a&&this.set(a)}set(a,s,r){const o=this;function f(h,p,m){const g=zs(p);if(!g)throw new Error("header name must be a non-empty string");const S=F.findKey(o,g);(!S||o[S]===void 0||m===!0||m===void 0&&o[S]!==!1)&&(o[S||p]=vu(h))}const d=(h,p)=>F.forEach(h,(m,g)=>f(m,g,p));if(F.isPlainObject(a)||a instanceof this.constructor)d(a,s);else if(F.isString(a)&&(a=a.trim())&&!UE(a))d(NE(a),s);else if(F.isObject(a)&&F.isIterable(a)){let h={},p,m;for(const g of a){if(!F.isArray(g))throw TypeError("Object iterator must return a key-value pair");h[m=g[0]]=(p=h[m])?F.isArray(p)?[...p,g[1]]:[p,g[1]]:g[1]}d(h,s)}else a!=null&&f(s,a,r);return this}get(a,s){if(a=zs(a),a){const r=F.findKey(this,a);if(r){const o=this[r];if(!s)return o;if(s===!0)return jE(o);if(F.isFunction(s))return s.call(this,o,r);if(F.isRegExp(s))return s.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,s){if(a=zs(a),a){const r=F.findKey(this,a);return!!(r&&this[r]!==void 0&&(!s||Sf(this,this[r],r,s)))}return!1}delete(a,s){const r=this;let o=!1;function f(d){if(d=zs(d),d){const h=F.findKey(r,d);h&&(!s||Sf(r,r[h],h,s))&&(delete r[h],o=!0)}}return F.isArray(a)?a.forEach(f):f(a),o}clear(a){const s=Object.keys(this);let r=s.length,o=!1;for(;r--;){const f=s[r];(!a||Sf(this,this[f],f,a,!0))&&(delete this[f],o=!0)}return o}normalize(a){const s=this,r={};return F.forEach(this,(o,f)=>{const d=F.findKey(r,f);if(d){s[d]=vu(o),delete s[f];return}const h=a?ME(f):String(f).trim();h!==f&&delete s[f],s[h]=vu(o),r[h]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const s=Object.create(null);return F.forEach(this,(r,o)=>{r!=null&&r!==!1&&(s[o]=a&&F.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,s])=>a+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...s){const r=new this(a);return s.forEach(o=>r.set(o)),r}static accessor(a){const r=(this[Xp]=this[Xp]={accessors:{}}).accessors,o=this.prototype;function f(d){const h=zs(d);r[h]||(LE(o,d),r[h]=!0)}return F.isArray(a)?a.forEach(f):f(a),this}};nn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);F.reduceDescriptors(nn.prototype,({value:l},a)=>{let s=a[0].toUpperCase()+a.slice(1);return{get:()=>l,set(r){this[s]=r}}});F.freezeMethods(nn);function xf(l,a){const s=this||Ws,r=a||s,o=nn.from(r.headers);let f=r.data;return F.forEach(l,function(h){f=h.call(s,f,o.normalize(),a?a.status:void 0)}),o.normalize(),f}function cg(l){return!!(l&&l.__CANCEL__)}function Li(l,a,s){Oe.call(this,l??"canceled",Oe.ERR_CANCELED,a,s),this.name="CanceledError"}F.inherits(Li,Oe,{__CANCEL__:!0});function fg(l,a,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?l(s):a(new Oe("Request failed with status code "+s.status,[Oe.ERR_BAD_REQUEST,Oe.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function zE(l){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return a&&a[1]||""}function kE(l,a){l=l||10;const s=new Array(l),r=new Array(l);let o=0,f=0,d;return a=a!==void 0?a:1e3,function(p){const m=Date.now(),g=r[f];d||(d=m),s[o]=p,r[o]=m;let S=f,x=0;for(;S!==o;)x+=s[S++],S=S%l;if(o=(o+1)%l,o===f&&(f=(f+1)%l),m-d<a)return;const R=g&&m-g;return R?Math.round(x*1e3/R):void 0}}function BE(l,a){let s=0,r=1e3/a,o,f;const d=(m,g=Date.now())=>{s=g,o=null,f&&(clearTimeout(f),f=null),l.apply(null,m)};return[(...m)=>{const g=Date.now(),S=g-s;S>=r?d(m,g):(o=m,f||(f=setTimeout(()=>{f=null,d(o)},r-S)))},()=>o&&d(o)]}const Nu=(l,a,s=3)=>{let r=0;const o=kE(50,250);return BE(f=>{const d=f.loaded,h=f.lengthComputable?f.total:void 0,p=d-r,m=o(p),g=d<=h;r=d;const S={loaded:d,total:h,progress:h?d/h:void 0,bytes:p,rate:m||void 0,estimated:m&&h&&g?(h-d)/m:void 0,event:f,lengthComputable:h!=null,[a?"download":"upload"]:!0};l(S)},s)},Zp=(l,a)=>{const s=l!=null;return[r=>a[0]({lengthComputable:s,total:l,loaded:r}),a[1]]},Qp=l=>(...a)=>F.asap(()=>l(...a)),HE=Ht.hasStandardBrowserEnv?((l,a)=>s=>(s=new URL(s,Ht.origin),l.protocol===s.protocol&&l.host===s.host&&(a||l.port===s.port)))(new URL(Ht.origin),Ht.navigator&&/(msie|trident)/i.test(Ht.navigator.userAgent)):()=>!0,qE=Ht.hasStandardBrowserEnv?{write(l,a,s,r,o,f){const d=[l+"="+encodeURIComponent(a)];F.isNumber(s)&&d.push("expires="+new Date(s).toGMTString()),F.isString(r)&&d.push("path="+r),F.isString(o)&&d.push("domain="+o),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(l){const a=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function VE(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function FE(l,a){return a?l.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):l}function dg(l,a,s){let r=!VE(a);return l&&(r||s==!1)?FE(l,a):a}const Kp=l=>l instanceof nn?{...l}:l;function Ha(l,a){a=a||{};const s={};function r(m,g,S,x){return F.isPlainObject(m)&&F.isPlainObject(g)?F.merge.call({caseless:x},m,g):F.isPlainObject(g)?F.merge({},g):F.isArray(g)?g.slice():g}function o(m,g,S,x){if(F.isUndefined(g)){if(!F.isUndefined(m))return r(void 0,m,S,x)}else return r(m,g,S,x)}function f(m,g){if(!F.isUndefined(g))return r(void 0,g)}function d(m,g){if(F.isUndefined(g)){if(!F.isUndefined(m))return r(void 0,m)}else return r(void 0,g)}function h(m,g,S){if(S in a)return r(m,g);if(S in l)return r(void 0,m)}const p={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:h,headers:(m,g,S)=>o(Kp(m),Kp(g),S,!0)};return F.forEach(Object.keys(Object.assign({},l,a)),function(g){const S=p[g]||o,x=S(l[g],a[g],g);F.isUndefined(x)&&S!==h||(s[g]=x)}),s}const hg=l=>{const a=Ha({},l);let{data:s,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:f,headers:d,auth:h}=a;a.headers=d=nn.from(d),a.url=rg(dg(a.baseURL,a.url,a.allowAbsoluteUrls),l.params,l.paramsSerializer),h&&d.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let p;if(F.isFormData(s)){if(Ht.hasStandardBrowserEnv||Ht.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((p=d.getContentType())!==!1){const[m,...g]=p?p.split(";").map(S=>S.trim()).filter(Boolean):[];d.setContentType([m||"multipart/form-data",...g].join("; "))}}if(Ht.hasStandardBrowserEnv&&(r&&F.isFunction(r)&&(r=r(a)),r||r!==!1&&HE(a.url))){const m=o&&f&&qE.read(f);m&&d.set(o,m)}return a},YE=typeof XMLHttpRequest<"u",GE=YE&&function(l){return new Promise(function(s,r){const o=hg(l);let f=o.data;const d=nn.from(o.headers).normalize();let{responseType:h,onUploadProgress:p,onDownloadProgress:m}=o,g,S,x,R,_;function N(){R&&R(),_&&_(),o.cancelToken&&o.cancelToken.unsubscribe(g),o.signal&&o.signal.removeEventListener("abort",g)}let O=new XMLHttpRequest;O.open(o.method.toUpperCase(),o.url,!0),O.timeout=o.timeout;function E(){if(!O)return;const U=nn.from("getAllResponseHeaders"in O&&O.getAllResponseHeaders()),k={data:!h||h==="text"||h==="json"?O.responseText:O.response,status:O.status,statusText:O.statusText,headers:U,config:l,request:O};fg(function(W){s(W),N()},function(W){r(W),N()},k),O=null}"onloadend"in O?O.onloadend=E:O.onreadystatechange=function(){!O||O.readyState!==4||O.status===0&&!(O.responseURL&&O.responseURL.indexOf("file:")===0)||setTimeout(E)},O.onabort=function(){O&&(r(new Oe("Request aborted",Oe.ECONNABORTED,l,O)),O=null)},O.onerror=function(){r(new Oe("Network Error",Oe.ERR_NETWORK,l,O)),O=null},O.ontimeout=function(){let G=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const k=o.transitional||ug;o.timeoutErrorMessage&&(G=o.timeoutErrorMessage),r(new Oe(G,k.clarifyTimeoutError?Oe.ETIMEDOUT:Oe.ECONNABORTED,l,O)),O=null},f===void 0&&d.setContentType(null),"setRequestHeader"in O&&F.forEach(d.toJSON(),function(G,k){O.setRequestHeader(k,G)}),F.isUndefined(o.withCredentials)||(O.withCredentials=!!o.withCredentials),h&&h!=="json"&&(O.responseType=o.responseType),m&&([x,_]=Nu(m,!0),O.addEventListener("progress",x)),p&&O.upload&&([S,R]=Nu(p),O.upload.addEventListener("progress",S),O.upload.addEventListener("loadend",R)),(o.cancelToken||o.signal)&&(g=U=>{O&&(r(!U||U.type?new Li(null,l,O):U),O.abort(),O=null)},o.cancelToken&&o.cancelToken.subscribe(g),o.signal&&(o.signal.aborted?g():o.signal.addEventListener("abort",g)));const D=zE(o.url);if(D&&Ht.protocols.indexOf(D)===-1){r(new Oe("Unsupported protocol "+D+":",Oe.ERR_BAD_REQUEST,l));return}O.send(f||null)})},$E=(l,a)=>{const{length:s}=l=l?l.filter(Boolean):[];if(a||s){let r=new AbortController,o;const f=function(m){if(!o){o=!0,h();const g=m instanceof Error?m:this.reason;r.abort(g instanceof Oe?g:new Li(g instanceof Error?g.message:g))}};let d=a&&setTimeout(()=>{d=null,f(new Oe(`timeout ${a} of ms exceeded`,Oe.ETIMEDOUT))},a);const h=()=>{l&&(d&&clearTimeout(d),d=null,l.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),l=null)};l.forEach(m=>m.addEventListener("abort",f));const{signal:p}=r;return p.unsubscribe=()=>F.asap(h),p}},XE=function*(l,a){let s=l.byteLength;if(s<a){yield l;return}let r=0,o;for(;r<s;)o=r+a,yield l.slice(r,o),r=o},ZE=async function*(l,a){for await(const s of QE(l))yield*XE(s,a)},QE=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const a=l.getReader();try{for(;;){const{done:s,value:r}=await a.read();if(s)break;yield r}}finally{await a.cancel()}},Jp=(l,a,s,r)=>{const o=ZE(l,a);let f=0,d,h=p=>{d||(d=!0,r&&r(p))};return new ReadableStream({async pull(p){try{const{done:m,value:g}=await o.next();if(m){h(),p.close();return}let S=g.byteLength;if(s){let x=f+=S;s(x)}p.enqueue(new Uint8Array(g))}catch(m){throw h(m),m}},cancel(p){return h(p),o.return()}},{highWaterMark:2})},Xu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",mg=Xu&&typeof ReadableStream=="function",KE=Xu&&(typeof TextEncoder=="function"?(l=>a=>l.encode(a))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),pg=(l,...a)=>{try{return!!l(...a)}catch{return!1}},JE=mg&&pg(()=>{let l=!1;const a=new Request(Ht.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!a}),Pp=64*1024,kf=mg&&pg(()=>F.isReadableStream(new Response("").body)),ju={stream:kf&&(l=>l.body)};Xu&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!ju[a]&&(ju[a]=F.isFunction(l[a])?s=>s[a]():(s,r)=>{throw new Oe(`Response type '${a}' is not supported`,Oe.ERR_NOT_SUPPORT,r)})})})(new Response);const PE=async l=>{if(l==null)return 0;if(F.isBlob(l))return l.size;if(F.isSpecCompliantForm(l))return(await new Request(Ht.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(F.isArrayBufferView(l)||F.isArrayBuffer(l))return l.byteLength;if(F.isURLSearchParams(l)&&(l=l+""),F.isString(l))return(await KE(l)).byteLength},WE=async(l,a)=>{const s=F.toFiniteNumber(l.getContentLength());return s??PE(a)},IE=Xu&&(async l=>{let{url:a,method:s,data:r,signal:o,cancelToken:f,timeout:d,onDownloadProgress:h,onUploadProgress:p,responseType:m,headers:g,withCredentials:S="same-origin",fetchOptions:x}=hg(l);m=m?(m+"").toLowerCase():"text";let R=$E([o,f&&f.toAbortSignal()],d),_;const N=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let O;try{if(p&&JE&&s!=="get"&&s!=="head"&&(O=await WE(g,r))!==0){let k=new Request(a,{method:"POST",body:r,duplex:"half"}),te;if(F.isFormData(r)&&(te=k.headers.get("content-type"))&&g.setContentType(te),k.body){const[W,K]=Zp(O,Nu(Qp(p)));r=Jp(k.body,Pp,W,K)}}F.isString(S)||(S=S?"include":"omit");const E="credentials"in Request.prototype;_=new Request(a,{...x,signal:R,method:s.toUpperCase(),headers:g.normalize().toJSON(),body:r,duplex:"half",credentials:E?S:void 0});let D=await fetch(_);const U=kf&&(m==="stream"||m==="response");if(kf&&(h||U&&N)){const k={};["status","statusText","headers"].forEach(ne=>{k[ne]=D[ne]});const te=F.toFiniteNumber(D.headers.get("content-length")),[W,K]=h&&Zp(te,Nu(Qp(h),!0))||[];D=new Response(Jp(D.body,Pp,W,()=>{K&&K(),N&&N()}),k)}m=m||"text";let G=await ju[F.findKey(ju,m)||"text"](D,l);return!U&&N&&N(),await new Promise((k,te)=>{fg(k,te,{data:G,headers:nn.from(D.headers),status:D.status,statusText:D.statusText,config:l,request:_})})}catch(E){throw N&&N(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new Oe("Network Error",Oe.ERR_NETWORK,l,_),{cause:E.cause||E}):Oe.from(E,E&&E.code,l,_)}}),Bf={http:mE,xhr:GE,fetch:IE};F.forEach(Bf,(l,a)=>{if(l){try{Object.defineProperty(l,"name",{value:a})}catch{}Object.defineProperty(l,"adapterName",{value:a})}});const Wp=l=>`- ${l}`,ew=l=>F.isFunction(l)||l===null||l===!1,yg={getAdapter:l=>{l=F.isArray(l)?l:[l];const{length:a}=l;let s,r;const o={};for(let f=0;f<a;f++){s=l[f];let d;if(r=s,!ew(s)&&(r=Bf[(d=String(s)).toLowerCase()],r===void 0))throw new Oe(`Unknown adapter '${d}'`);if(r)break;o[d||"#"+f]=r}if(!r){const f=Object.entries(o).map(([h,p])=>`adapter ${h} `+(p===!1?"is not supported by the environment":"is not available in the build"));let d=a?f.length>1?`since :
`+f.map(Wp).join(`
`):" "+Wp(f[0]):"as no adapter specified";throw new Oe("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return r},adapters:Bf};function Ef(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new Li(null,l)}function Ip(l){return Ef(l),l.headers=nn.from(l.headers),l.data=xf.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),yg.getAdapter(l.adapter||Ws.adapter)(l).then(function(r){return Ef(l),r.data=xf.call(l,l.transformResponse,r),r.headers=nn.from(r.headers),r},function(r){return cg(r)||(Ef(l),r&&r.response&&(r.response.data=xf.call(l,l.transformResponse,r.response),r.response.headers=nn.from(r.response.headers))),Promise.reject(r)})}const gg="1.9.0",Zu={};["object","boolean","number","function","string","symbol"].forEach((l,a)=>{Zu[l]=function(r){return typeof r===l||"a"+(a<1?"n ":" ")+l}});const ey={};Zu.transitional=function(a,s,r){function o(f,d){return"[Axios v"+gg+"] Transitional option '"+f+"'"+d+(r?". "+r:"")}return(f,d,h)=>{if(a===!1)throw new Oe(o(d," has been removed"+(s?" in "+s:"")),Oe.ERR_DEPRECATED);return s&&!ey[d]&&(ey[d]=!0,console.warn(o(d," has been deprecated since v"+s+" and will be removed in the near future"))),a?a(f,d,h):!0}};Zu.spelling=function(a){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${a}`),!0)};function tw(l,a,s){if(typeof l!="object")throw new Oe("options must be an object",Oe.ERR_BAD_OPTION_VALUE);const r=Object.keys(l);let o=r.length;for(;o-- >0;){const f=r[o],d=a[f];if(d){const h=l[f],p=h===void 0||d(h,f,l);if(p!==!0)throw new Oe("option "+f+" must be "+p,Oe.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new Oe("Unknown option "+f,Oe.ERR_BAD_OPTION)}}const bu={assertOptions:tw,validators:Zu},nl=bu.validators;let La=class{constructor(a){this.defaults=a||{},this.interceptors={request:new $p,response:new $p}}async request(a,s){try{return await this._request(a,s)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?f&&!String(r.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+f):r.stack=f}catch{}}throw r}}_request(a,s){typeof a=="string"?(s=s||{},s.url=a):s=a||{},s=Ha(this.defaults,s);const{transitional:r,paramsSerializer:o,headers:f}=s;r!==void 0&&bu.assertOptions(r,{silentJSONParsing:nl.transitional(nl.boolean),forcedJSONParsing:nl.transitional(nl.boolean),clarifyTimeoutError:nl.transitional(nl.boolean)},!1),o!=null&&(F.isFunction(o)?s.paramsSerializer={serialize:o}:bu.assertOptions(o,{encode:nl.function,serialize:nl.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),bu.assertOptions(s,{baseUrl:nl.spelling("baseURL"),withXsrfToken:nl.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let d=f&&F.merge(f.common,f[s.method]);f&&F.forEach(["delete","get","head","post","put","patch","common"],_=>{delete f[_]}),s.headers=nn.concat(d,f);const h=[];let p=!0;this.interceptors.request.forEach(function(N){typeof N.runWhen=="function"&&N.runWhen(s)===!1||(p=p&&N.synchronous,h.unshift(N.fulfilled,N.rejected))});const m=[];this.interceptors.response.forEach(function(N){m.push(N.fulfilled,N.rejected)});let g,S=0,x;if(!p){const _=[Ip.bind(this),void 0];for(_.unshift.apply(_,h),_.push.apply(_,m),x=_.length,g=Promise.resolve(s);S<x;)g=g.then(_[S++],_[S++]);return g}x=h.length;let R=s;for(S=0;S<x;){const _=h[S++],N=h[S++];try{R=_(R)}catch(O){N.call(this,O);break}}try{g=Ip.call(this,R)}catch(_){return Promise.reject(_)}for(S=0,x=m.length;S<x;)g=g.then(m[S++],m[S++]);return g}getUri(a){a=Ha(this.defaults,a);const s=dg(a.baseURL,a.url,a.allowAbsoluteUrls);return rg(s,a.params,a.paramsSerializer)}};F.forEach(["delete","get","head","options"],function(a){La.prototype[a]=function(s,r){return this.request(Ha(r||{},{method:a,url:s,data:(r||{}).data}))}});F.forEach(["post","put","patch"],function(a){function s(r){return function(f,d,h){return this.request(Ha(h||{},{method:a,headers:r?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}La.prototype[a]=s(),La.prototype[a+"Form"]=s(!0)});let nw=class vg{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(f){s=f});const r=this;this.promise.then(o=>{if(!r._listeners)return;let f=r._listeners.length;for(;f-- >0;)r._listeners[f](o);r._listeners=null}),this.promise.then=o=>{let f;const d=new Promise(h=>{r.subscribe(h),f=h}).then(o);return d.cancel=function(){r.unsubscribe(f)},d},a(function(f,d,h){r.reason||(r.reason=new Li(f,d,h),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const s=this._listeners.indexOf(a);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const a=new AbortController,s=r=>{a.abort(r)};return this.subscribe(s),a.signal.unsubscribe=()=>this.unsubscribe(s),a.signal}static source(){let a;return{token:new vg(function(o){a=o}),cancel:a}}};function lw(l){return function(s){return l.apply(null,s)}}function aw(l){return F.isObject(l)&&l.isAxiosError===!0}const Hf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Hf).forEach(([l,a])=>{Hf[a]=l});function bg(l){const a=new La(l),s=Ky(La.prototype.request,a);return F.extend(s,La.prototype,a,{allOwnKeys:!0}),F.extend(s,a,null,{allOwnKeys:!0}),s.create=function(o){return bg(Ha(l,o))},s}const mt=bg(Ws);mt.Axios=La;mt.CanceledError=Li;mt.CancelToken=nw;mt.isCancel=cg;mt.VERSION=gg;mt.toFormData=$u;mt.AxiosError=Oe;mt.Cancel=mt.CanceledError;mt.all=function(a){return Promise.all(a)};mt.spread=lw;mt.isAxiosError=aw;mt.mergeConfig=Ha;mt.AxiosHeaders=nn;mt.formToJSON=l=>og(F.isHTMLForm(l)?new FormData(l):l);mt.getAdapter=yg.getAdapter;mt.HttpStatusCode=Hf;mt.default=mt;const{Axios:Aw,AxiosError:Tw,CanceledError:Rw,isCancel:Ow,CancelToken:Cw,VERSION:Dw,all:Nw,Cancel:jw,isAxiosError:Uw,spread:Mw,toFormData:Lw,AxiosHeaders:zw,HttpStatusCode:kw,formToJSON:Bw,getAdapter:Hw,mergeConfig:qw}=mt,iw="http://127.0.0.1:8000/",sw=mt.create({baseURL:iw,timeout:5e3,headers:{"Content-Type":"application/json",accept:"application/json"}});sw.interceptors.request.use(l=>{const a=localStorage.getItem("access_token");return a&&(l.headers.Authorization=`JWT ${a}`),l},l=>Promise.reject(l));const rw=Pe.createContext({}),Sg=!0;function uw({baseColor:l,highlightColor:a,width:s,height:r,borderRadius:o,circle:f,direction:d,duration:h,enableAnimation:p=Sg,customHighlightBackground:m}){const g={};return d==="rtl"&&(g["--animation-direction"]="reverse"),typeof h=="number"&&(g["--animation-duration"]=`${h}s`),p||(g["--pseudo-element-display"]="none"),(typeof s=="string"||typeof s=="number")&&(g.width=s),(typeof r=="string"||typeof r=="number")&&(g.height=r),(typeof o=="string"||typeof o=="number")&&(g.borderRadius=o),f&&(g.borderRadius="50%"),typeof l<"u"&&(g["--base-color"]=l),typeof a<"u"&&(g["--highlight-color"]=a),typeof m=="string"&&(g["--custom-highlight-background"]=m),g}function en({count:l=1,wrapper:a,className:s,containerClassName:r,containerTestId:o,circle:f=!1,style:d,...h}){var p,m,g;const S=Pe.useContext(rw),x={...h};for(const[U,G]of Object.entries(h))typeof G>"u"&&delete x[U];const R={...S,...x,circle:f},_={...d,...uw(R)};let N="react-loading-skeleton";s&&(N+=` ${s}`);const O=(p=R.inline)!==null&&p!==void 0?p:!1,E=[],D=Math.ceil(l);for(let U=0;U<D;U++){let G=_;if(D>l&&U===D-1){const te=(m=G.width)!==null&&m!==void 0?m:"100%",W=l%1,K=typeof te=="number"?te*W:`calc(${te} * ${W})`;G={...G,width:K}}const k=Pe.createElement("span",{className:N,style:G,key:U},"‌");O?E.push(k):E.push(Pe.createElement(Pe.Fragment,{key:U},k,Pe.createElement("br",null)))}return Pe.createElement("span",{className:r,"data-testid":o,"aria-live":"polite","aria-busy":(g=R.enableAnimation)!==null&&g!==void 0?g:Sg},a?E.map((U,G)=>Pe.createElement(a,{key:G},U)):E)}function wf(){return v.jsxs(v.Fragment,{children:[v.jsx(en,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(en,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(en,{height:500,width:350,borderRadius:40,baseColor:"#ffd700",highlightColor:"#909098"}),v.jsx(en,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(en,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"})]})}function ow(){const[l,a]=w.useState(!1),[s,r]=w.useState("Growing Industry Sector"),[o,f]=w.useState([]),[d,h]=w.useState([]),[p,m]=w.useState([]),[g,S]=w.useState([]),[x,R]=w.useState(null),[_,N]=w.useState(null),[O,E]=w.useState(!0),[D,U]=w.useState(!1),[G,k]=w.useState(null),te=async()=>{try{const K=await Qy.getLatestTrends();console.log("Latest trends:",K),f(K[0]),h(K[1]),m(K[2]),S(K[3]),E(!1),k(new Date().toLocaleTimeString())}catch(K){console.error("Failed to fetch latest trends:",K),E(!1)}};w.useEffect(()=>{(async()=>{const ne=await On.getCurrentUser();N(ne)})()},[]),w.useEffect(()=>{ke.connect();const K=ae=>{console.log("WebSocket connected:",ae.message),U(!0)},ne=ae=>{console.log("WebSocket disconnected:",ae.message),U(!1)},xe=ae=>{console.log("Prediction completed:",ae),te()},oe=ae=>{console.log("Dataset uploaded:",ae)};return ke.onConnectionEstablished(K),ke.onConnectionLost(ne),ke.onPredictionCompleted(xe),ke.onDatasetUploaded(oe),te(),()=>{ke.removeEventListener("connection_established",K),ke.removeEventListener("connection_lost",ne),ke.removeEventListener("prediction_completed",xe),ke.removeEventListener("dataset_uploaded",oe),ke.disconnect()}},[]),w.useEffect(()=>{s=="Growing Industry Sector"?R("Top 5 Growing Industry Sectors in"):s=="Industry Sector Revenue"?R("Top 5 Industry Sectors by Revenue in"):R("Top 5 Least Crowded Industry Sectors in")},[s]),l?document.body.style.overflow="hidden":document.body.style.overflow="auto";const W=(K,ne,xe)=>{if(xe=="Growing Industry Sector")return o.find(oe=>oe.type===K&&oe.rank===ne);if(xe=="Industry Sector Revenue")return d.find(oe=>oe.type===K&&oe.rank===ne);if(xe=="Least Crowded")return p.find(oe=>oe.type===K&&oe.rank===ne)};return v.jsxs(v.Fragment,{children:[l&&v.jsx(Ox,{showModal:()=>a(!1)}),v.jsx("nav",{children:v.jsx(rd,{showModal:()=>a(!0)})}),v.jsxs("main",{className:"home",children:[v.jsxs("section",{className:"short-term",id:"short-term",children:[v.jsxs("div",{className:"filter",children:[v.jsx("p",{children:"Filter Results"}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Growing Industry Sector"?"active":""}`,onClick:()=>r("Growing Industry Sector"),children:v.jsx("span",{children:"Growing Industry Sector"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Industry Sector Revenue"?"active":""}`,onClick:()=>r("Industry Sector Revenue"),children:v.jsx("span",{children:"Industry Sector Revenue"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${s=="Least Crowded"?"active":""}`,onClick:()=>r("Least Crowded"),children:v.jsx("span",{children:"Least Crowded"})})})]}),v.jsxs("h1",{children:["Short-Term Outlook: ",x," ",g.length>0?g[0]:"🔮"]}),v.jsxs("section",{className:"short-term-contents",children:[O&&v.jsx(wf,{}),!O&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"short-term",data:W("short-term",4,s),filterResult:s}),v.jsx(Ft,{topNumber:2,type:"short-term",data:W("short-term",2,s),filterResult:s}),v.jsx(Ft,{topNumber:1,type:"short-term",data:W("short-term",1,s),filterResult:s}),v.jsx(Ft,{topNumber:3,type:"short-term",data:W("short-term",3,s),filterResult:s}),v.jsx(Ft,{topNumber:5,type:"short-term",data:W("short-term",5,s),filterResult:s})]})]})]}),v.jsxs("section",{className:"mid-term",id:"mid-term",children:[v.jsxs("h1",{children:["Mid-Term Outlook: ",x," ",g.length>0?g[1]:"🔮"]}),v.jsxs("section",{className:"mid-term-contents",children:[O&&v.jsx(wf,{}),!O&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"mid-term",data:W("mid-term",4,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:2,type:"mid-term",data:W("mid-term",2,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:1,type:"mid-term",data:W("mid-term",1,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:3,type:"mid-term",data:W("mid-term",3,s),filterResult:s,color:"dark"}),v.jsx(Ft,{topNumber:5,type:"mid-term",data:W("mid-term",5,s),filterResult:s,color:"dark"})]})]})]}),v.jsxs("section",{className:"long-term",id:"long-term",children:[v.jsxs("h1",{children:["Long-Term Outlook: ",x," ",g.length>0?g[2]:"🔮"," ","(Based on Business Count)"]}),v.jsxs("section",{className:"long-term-contents",children:[O&&v.jsx(wf,{}),!O&&v.jsxs(v.Fragment,{children:[v.jsx(Ft,{topNumber:4,type:"long-term",data:W("long-term",4,s),filterResult:s}),v.jsx(Ft,{topNumber:2,type:"long-term",data:W("long-term",2,s),filterResult:s}),v.jsx(Ft,{topNumber:1,type:"long-term",data:W("long-term",1,s),filterResult:s}),v.jsx(Ft,{topNumber:3,type:"long-term",data:W("long-term",3,s),filterResult:s}),v.jsx(Ft,{topNumber:5,type:"long-term",data:W("long-term",5,s),filterResult:s})]})]})]})]})]})}const ks="http://127.0.0.1:8000/",cw="http://127.0.0.1:8000/users/list_by_status/?is_active=";class fw{async register(a,s,r,o,f){try{const d=await fetch(ks+"register/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:s,first_name:r,last_name:o,is_superuser:f})});if(!d.ok){const h=await d.json();return{status:d.status,message:h.email[0]}}return d}catch(d){console.log("Failed to register!",d)}}async updateUserInfo(a,s,r){try{const o=await fetch(ks+"users/"+a+"/",{method:"PUT",headers:On.getAuthHeader(),body:JSON.stringify({first_name:s,last_name:r})});if(!o.ok){const d=await o.json();return console.log("failed to update user info",d),o.status}const f=await o.json();return o.status}catch(o){console.log("Failed to update user info!",o)}}async updateProfile(a,s){const r=new FormData;r.append("profile_picture",s);try{const o=On.getAccessToken(),f=await fetch(ks+"users/"+a+"/",{method:"PATCH",headers:{Authorization:o?`JWT ${o}`:""},body:r});if(!f.ok){const d=await f.json();return console.log("failed to update user profile picture",d),f.status}return f.status}catch(o){return console.log("Failed to update user profile picture!",o),o}}async getAllUsers(){try{const a=await fetch(ks+"users/",{method:"GET",headers:On.getAuthHeader()});if(!a.ok){const r=await a.json();return console.log("failed to fetch all users"),r}const s=await a.json();return Array.from(s)}catch(a){console.log("Failed to get all users!",a)}}async getUsersByisActive(a){try{const s=await fetch(cw+a,{method:"GET",headers:On.getAuthHeader()});if(!s.ok){const o=await s.json();return console.log("failed to fetch is_active users"),o}const r=await s.json();return Array.from(r)}catch(s){console.log("Failed to get users by status!",s)}}async changeUserStatus(a){try{const s=await fetch(ks+"users/change_status/",{method:"PATCH",headers:On.getAuthHeader(),body:JSON.stringify({user_id:a})});if(!s.ok){const r=await s.json();return console.log("failed to change user status",r),!1}return!0}catch(s){console.log("Failed to change user status!",s)}}}const za=new fw;function dw({showRegisterUserModal:l}){const a=$n(),[s,r]=w.useState(!1),[o,f]=w.useState([]),[d,h]=w.useState(!1),[p,m]=w.useState(null),g=Zs().shape({firstName:Hn().required("First name is required."),lastName:Hn().required("Last name is required."),email:Hn().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:Hn().required("Password is required.").matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:Hn().required("Confirm password is required.").oneOf([Dy("password"),null],"Password don't match.")}),{register:S,handleSubmit:x,formState:{errors:R,isValid:_},watch:N}=Xs({resolver:zu(g),mode:"all"}),O=async D=>{h(!0);try{const U=await za.register(D.email,D.password,D.firstName,D.lastName,D.isAdmin);console.log("response received:",U),U.ok?(a("/user-management",{state:{registrationSuccess:!0}}),l()):m(U)}catch(U){console.error("Error during registration:",U)}finally{h(!1)}},E=N("password","");return w.useEffect(()=>{let D=[];E.length<12&&D.push("- At least 12 characters."),E.length>16&&D.push("- Not exceed 16 characters."),/[0-9]/.test(E)||D.push("- At least one number."),/[a-z]/.test(E)||D.push("- At least one lowercase letter."),/[A-Z]/.test(E)||D.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(E)||D.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(E)&&D.push("Must not contain spaces"),f(D)},[E]),w.useEffect(()=>{p!=null&&setTimeout(()=>{m(null)},5e3)},[p]),v.jsxs(v.Fragment,{children:[p!=null&&p.status!=201&&v.jsx(qn,{message:p.message,type:"danger"}),v.jsxs("div",{className:"register-user-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsxs("header",{children:[v.jsx("button",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),onClick:l,children:v.jsx("img",{src:s?ku:Du,alt:"close-icon"})}),v.jsx("h2",{children:"Register New User"})]}),v.jsxs("form",{onSubmit:x(O),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First Name *"}),v.jsx("input",{type:"text",name:"first-name",id:"first-name",required:!0,placeholder:"Enter first name...",...S("firstName")}),R.firstName&&v.jsx("span",{children:R.firstName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last Name *"}),v.jsx("input",{type:"text",name:"last-name",id:"last-name",required:!0,placeholder:"Enter last name...",...S("lastName")}),R.lastName&&v.jsx("span",{children:R.lastName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email *"}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email...",...S("email")}),R.email&&v.jsx("span",{children:R.email.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password *"}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password...",...S("password")}),o.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),o.map((D,U)=>v.jsx("span",{children:D},U))]})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password *"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",required:!0,placeholder:"Enter confirm password...",...S("confirmPassword")}),R.confirmPassword&&v.jsx("span",{children:R.confirmPassword.message})]}),v.jsxs("fieldset",{children:[v.jsx("input",{type:"checkbox",name:"is-admin",id:"is-admin",...S("isAdmin")}),v.jsx("label",{htmlFor:"is-admin",children:"Click the checkbox if the new user is an admin."})]}),v.jsxs("button",{className:"submit-button",disabled:!_||d,children:[d&&v.jsx(ca,{}),d?"Registering...":"Register"]})]})]})]})]})}const hw="/assets/active-user-Bd-Ue2Qb.svg",mw="/assets/delete-account-BwubX78G.svg";function pw({isShow:l,action:a,userId:s,userName:r}){let o;const f=$n(),[d,h]=w.useState(!1),[p,m]=w.useState(!1);a==="Activate"?o=hw:o=mw;const g=async()=>{m(!0);try{if(await za.changeUserStatus(s))f("/user-management",{state:{changeStatusSuccess:!0}}),l();else return}catch(S){console.log("Failed to change user status!",S)}finally{m(!1)}};return v.jsxs("section",{className:"confirm-status-change-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onClick:l,className:"close-btn",children:v.jsx("img",{src:d?ku:Du,alt:"close-icon"})}),v.jsx("img",{src:o,alt:"icon","data-action":a}),v.jsxs("h2",{children:[a," Account"]}),v.jsxs("p",{children:["Are you sure you want to ",a.toLowerCase()," ",s!=JSON.parse(sessionStorage.getItem("current_user")).id?`${r}'s`:"your"," ","account?"]}),v.jsxs("div",{className:"group-buttons",children:[v.jsxs("button",{className:"submit-button",onClick:l,children:["No, keep ",a.toLowerCase()]}),v.jsxs("button",{className:"submit-button",onClick:g,disabled:p,children:[p&&v.jsx(ca,{}),p?"Processing...":`Yes, ${a.toLowerCase()}`]})]})]})]})}function yw(){var te,W;const a=cl(),s=$n(),[r,o]=w.useState("All"),[f,d]=w.useState(!1),[h,p]=w.useState(!1),[m,g]=w.useState([]),[S,x]=w.useState(!0),[R,_]=w.useState(!1),[N,O]=w.useState({userId:null,userName:null,action:null}),[E,D]=w.useState(!1),U=(te=a.state)==null?void 0:te.registrationSuccess,G=(W=a.state)==null?void 0:W.changeStatusSuccess,k=async()=>{g([]),x(!0);try{let K;r==="All"?K=await za.getAllUsers():r==="Active"?K=await za.getUsersByisActive(1):r==="Inactive"&&(K=await za.getUsersByisActive(0)),g(K||[]),console.log(`Users ${r}:`,K)}catch(K){console.error("Failed to fetch users:",K),g([])}finally{x(!1)}};return w.useEffect(()=>{U&&(p(!0),setTimeout(()=>{p(!1),s(a.pathname,{state:{registrationSuccess:!1}})},5e3)),G&&(D(!0),setTimeout(()=>{D(!1),s(a.pathname,{state:{changeStatusSuccess:!1}})},5e3))},[U,G]),w.useEffect(()=>{const K=oe=>{console.log("User created via WebSocket:",oe),p(!0),setTimeout(()=>p(!1),5e3),k()},ne=oe=>{console.log("User updated via WebSocket:",oe),k()},xe=oe=>{console.log("User status changed via WebSocket:",oe),D(!0),setTimeout(()=>D(!1),5e3),k()};return ke.onUserCreated(K),ke.onUserUpdated(ne),ke.onUserStatusChanged(xe),()=>{ke.removeEventListener("user_created",K),ke.removeEventListener("user_updated",ne),ke.removeEventListener("user_status_changed",xe)}},[]),w.useEffect(()=>{k()},[r]),v.jsxs(v.Fragment,{children:[f&&v.jsx(dw,{showRegisterUserModal:()=>d(!1)}),h&&v.jsx(qn,{message:"Registration successful!",type:"success"}),R&&v.jsx(pw,{isShow:()=>_(!1),action:N.action,userId:N.userId,userName:N.userName}),E&&v.jsx(qn,{message:"User status changed successfully!",type:"success"}),v.jsx("nav",{children:v.jsx(rd,{})}),v.jsxs("main",{className:"user-management",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"User Management"}),v.jsx("button",{onClick:()=>d(!0),children:"Add User"})]})}),v.jsx("section",{children:v.jsxs("div",{className:"table-header",children:[v.jsxs("h2",{children:[r," Users ",!S&&v.jsxs("span",{children:["(",m.length,")"]})]}),v.jsxs("ul",{children:[v.jsx("li",{className:r==="All"?"active":"",onClick:()=>o("All"),children:"All"}),v.jsx("li",{className:r==="Active"?"active":"",onClick:()=>o("Active"),children:"Active"}),v.jsx("li",{className:r==="Inactive"?"active":"",onClick:()=>o("Inactive"),children:"Inactive"})]})]})}),S&&v.jsx("section",{className:"grid-container",children:Array.from({length:8}).map((K,ne)=>v.jsxs("article",{className:"user-container",children:[v.jsx(en,{height:40,width:40,circle:!0}),v.jsx(en,{height:20,width:100}),v.jsxs("section",{className:"status-and-role",children:[v.jsx(en,{height:20,width:60}),v.jsx(en,{height:20,width:60})]}),v.jsx(en,{height:40,width:200,borderRadius:40})]},ne))}),!S&&m.length==0&&v.jsx("p",{children:"No users found."}),m.length>0&&v.jsx("section",{className:"grid-container",children:m.map((K,ne)=>v.jsxs("article",{className:"user-container",children:[v.jsx("img",{src:K.profile_picture?`http://127.0.0.1:8000${K.profile_picture}`:Tu,alt:"Profile"}),v.jsxs("h2",{children:[K.first_name," ",K.last_name]}),v.jsxs("section",{className:"status-and-role",children:[v.jsxs("span",{className:"status","data-status":K.is_active===!0?"active":"inactive",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:K.is_active===!0?"Active":"Inactive"})]}),K.is_superuser===!0&&v.jsxs("span",{className:"role","data-role":"admin",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:"Admin"})]})]}),v.jsx("button",{onClick:()=>{_(!0),O({userId:K.id,userName:`${K.first_name} ${K.last_name}`,action:K.is_active===!0?"Deactivate":"Activate"})},children:K.is_active===!0?"Deactivate Account":"Activate Account"})]},ne))})]})]})}class gw{formatDate(a){const s=new Date(a),r={timeZone:"Asia/Manila",year:"numeric",month:"long",day:"2-digit"};return new Intl.DateTimeFormat("en-CA",r).format(s)}formatDateWithTime(a){const s=new Date(a),r={timeZone:"Asia/Manila",year:"numeric",month:"long",day:"2-digit",hour:"2-digit",minute:"2-digit"};return new Intl.DateTimeFormat("en-CA",r).format(s)}}const vw=new gw;function bw(){const l=JSON.parse(sessionStorage.getItem("current_user"))||null,a=sessionStorage.getItem("account_created_date")||null,[s,r]=w.useState({id:(l==null?void 0:l.id)||null,firstName:(l==null?void 0:l.first_name)||"",lastName:(l==null?void 0:l.last_name)||"",email:(l==null?void 0:l.email)||"",is_superuser:(l==null?void 0:l.is_superuser)||null,profile:(l==null?void 0:l.profile_picture)||null}),[o,f]=w.useState(null),[d,h]=w.useState(!1),[p,m]=w.useState(!1),[g,S]=w.useState(!1),[x,R]=w.useState(null),[_,N]=w.useState(null),[O,E]=w.useState(!1),[D,U]=w.useState(null),[G,k]=w.useState(!1),te=L=>{const[X,I]=L.split("@");return`${X[0]+"*".repeat(X.length-1)}@${I}`},W=L=>{if(L.target.files.length>0){const X=L.target.files[0];R(X);const I=new FileReader;I.onload=me=>{U(me.target.result)},I.readAsDataURL(X)}else R(null),U(null)},{register:K,handleSubmit:ne,watch:xe}=Xs({defaultValues:{firstName:s.firstName,lastName:s.lastName},mode:"all"}),oe=xe("firstName",""),ae=xe("lastName","");w.useEffect(()=>{const L=oe.trim(),X=ae.trim(),I=L!=="",me=X!=="",T=L!==s.firstName,Y=X!==s.lastName;h(I&&me&&(T||Y))},[oe,ae,s]);const pe=async L=>{S(!0),m(!0);try{const X=await za.updateUserInfo(JSON.parse(sessionStorage.getItem("current_user")).id,L.firstName,L.lastName);console.log("response received:",X),!X===200?(console.log("failed to update user info",X),f(X)):(console.log("going to update user info..."),we(),S(!1),f(X))}catch(X){console.log("Failed to update user info!",X)}finally{m(!1)}},he=async(L,X)=>{E(!0);const I=await za.updateProfile(L,X);!I===200?(console.log("failed to update user profile picture",I),N(I)):(console.log("going to update user profile picture..."),we(),N(I),E(!1),R(null),U(null))},we=async()=>{k(!0);const L=await On.getCurrentUser(),{date_created:X,...I}=L;sessionStorage.setItem("current_user",JSON.stringify(I)),console.log("updating user info in session storage!"),r({id:L.id,firstName:L.first_name,lastName:L.last_name,email:L.email,is_superuser:L.is_superuser,profile:L.profile_picture}),console.log("Successfully updated the user info in the session storage!");const me=new CustomEvent("userProfileUpdated",{detail:{user:I,timestamp:new Date().toISOString()}});window.dispatchEvent(me),console.log("Dispatched userProfileUpdated event"),k(!1)};return w.useEffect(()=>{o!=null&&setTimeout(()=>{f(null)},5e3)},[o]),w.useEffect(()=>{const X=JSON.parse(sessionStorage.getItem("current_user"))?JSON.parse(sessionStorage.getItem("current_user")).id:null,I=T=>{console.log("Profile updated via WebSocket:",T),T.user_id===X&&(we(),f(200))},me=T=>{console.log("User updated via WebSocket:",T),T.user_id===X&&(we(),f(200))};return ke.onProfileUpdated(I),ke.onUserUpdated(me),()=>{ke.removeEventListener("profile_updated",I),ke.removeEventListener("user_updated",me)}},[]),v.jsxs(v.Fragment,{children:[o===200&&v.jsx(qn,{message:"Successfully Updated!",type:"success"}),o!=200&&o!=null&&v.jsx(qn,{message:"Unable to update information. Please try again!",type:"danger"}),_===200&&v.jsx(qn,{message:"Successfully changed profile picture!",type:"success"}),v.jsx(rd,{}),v.jsxs("main",{className:"account-details",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"Account Profile"}),v.jsxs("div",{className:"profile-container",children:[v.jsx("h4",{children:s.firstName+" "+s.lastName}),v.jsx("img",{src:s.profile?`http://127.0.0.1:8000${s.profile}`:Tu,alt:"profile-picture"})]})]})}),v.jsxs("section",{children:[v.jsxs("section",{className:"left-panel",children:[G?v.jsx(en,{height:100,width:100,circle:!0,style:{borderRadius:"50%"}}):v.jsx("img",{src:D||(s.profile?`http://127.0.0.1:8000${s.profile}`:Tu),alt:""}),v.jsx("input",{type:"file",name:"profile",id:"profile",style:{display:"none"},accept:"image/*",onChange:W}),x===null&&v.jsx("label",{htmlFor:"profile",children:"Change Profile Picture"}),x!=null&&v.jsxs("button",{onClick:()=>he(s.id,x),disabled:O,className:"submit-button",children:[O&&v.jsx(ca,{}),O?"Saving...":"Save Profile Change"]}),v.jsxs("form",{action:ne(pe),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First name"}),G?v.jsx("div",{style:{width:"100%"},children:v.jsx(en,{height:44,style:{borderRadius:"40px",width:"100%"}})}):v.jsx("input",{type:"text",name:"first-name",id:"first-name",defaultValue:s.firstName,...K("firstName")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last name"}),G?v.jsx("div",{style:{width:"100%"},children:v.jsx(en,{height:44,style:{borderRadius:"40px",width:"100%"}})}):v.jsx("input",{type:"text",name:"last-name",id:"last-name",defaultValue:s.lastName,...K("lastName")})]}),G?v.jsx("div",{style:{width:"100%"},children:v.jsx(en,{height:44,style:{borderRadius:"40px",width:"100%"}})}):v.jsxs("button",{type:"submit",className:"submit-button",disabled:!d||p,children:[p&&v.jsx(ca,{}),"Save Change"]})]})]}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h2",{children:"Account Overview"}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"text",name:"email",id:"email",value:s.email!=""?te(s.email):"",disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"role",children:"Role:"}),v.jsx("input",{type:"text",name:"role",id:"role",value:s.is_superuser?"Admin":"Operator",disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"date-created",children:"Date Created:"}),v.jsx("input",{type:"text",name:"date-created",id:"date-created",value:a!=null?vw.formatDateWithTime(a):"",disabled:!0})]})]})]})]})]})}const xg="/assets/forgot_password-jtzYLH6S.png";function Sw(){const l=$n(),[a,s]=w.useState(!1),[r,o]=w.useState(null),f=Zs().shape({email:Hn().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=Xs({resolver:zu(f),mode:"all"}),g=async S=>{s(!0);const x=await On.resetPassword(S.email);o(x),s(null)};return w.useEffect(()=>{r!=null&&setTimeout(()=>{o(null)},5e3)},[r]),console.log("responseStatus:",r),v.jsxs(v.Fragment,{children:[r===204&&v.jsx(qn,{message:`If that email address is in our database, \r
          we will send you an email to reset your password.`,type:"success"}),r!=204&&r!=null&&v.jsx(qn,{message:"Unable to sent reset password. Please try again.",type:"danger"}),v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:xg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Password Reset"}),v.jsx("p",{children:"Enter your email address and we will send you a link to reset your password."}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"email",name:"email",id:"email",placeholder:"Enter email...",...d("email")}),p.email&&v.jsx("span",{children:p.email.message})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(ca,{}),a?"Submitting...":"Sent Reset Link"]}),v.jsx("a",{onClick:()=>l("/login"),children:"Back to Log In"})]})]})]})]})}function xw(){const l=$n(),{uid:a,token:s}=pb(),[r,o]=w.useState(!1),[f,d]=w.useState([]),[h,p]=w.useState(null),m=Zs().shape({password:Hn().required("Password is required.").min(12).max(64).matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,64}$/),confirmPassword:Hn().oneOf([Dy("password"),null],"Password don't match.").required("Confirm Password is required.")}),{register:g,handleSubmit:S,formState:{errors:x,isValid:R},watch:_}=Xs({resolver:zu(m),mode:"all"}),N=async E=>{o(!0);const D=await On.resetPasswordConfirm(a,s,E.password);console.log("response response:",D),p(D),o(!1),console.log(E)};w.useEffect(()=>{h!=null&&setTimeout(()=>{p(null)},5e3)},[h]);const O=_("password","");return w.useEffect(()=>{let E=[];O.length<12&&E.push("- At least 12 characters."),O.length>64&&E.push("- Not exceed 64 characters."),/[0-9]/.test(O)||E.push("- At least one number."),/[a-z]/.test(O)||E.push("- At least one lowercase letter."),/[A-Z]/.test(O)||E.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(O)||E.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(O)&&E.push("Must not contain spaces"),d(E)},[O]),v.jsxs(v.Fragment,{children:[h===204&&v.jsx(qn,{message:"Password changed successfully!",type:"success"}),h!=204&&h!=null&&v.jsx(qn,{message:"Unable to change password. Please try again.",type:"danger"}),v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:xg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Set-Up New Password"}),v.jsx("p",{children:"Set a new password for your account."}),v.jsxs("form",{onSubmit:S(N),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"New Password:"}),v.jsx("input",{type:"password",name:"password",id:"password",placeholder:"Enter new password...",...g("password")})]}),f.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),f.map((E,D)=>v.jsx("span",{children:E},D))]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password:"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",placeholder:"Enter confirm password...",...g("confirmPassword")}),x.confirmPassword&&v.jsx("span",{children:x.confirmPassword.message})]}),v.jsxs("button",{type:"submit",disabled:!R||r,className:"submit-button",children:[r&&v.jsx(ca,{}),r?"Submitting...":"Submit"]}),v.jsx("a",{onClick:()=>l("/login"),children:"Back to Log In"})]})]})]})]})}function ty({roles:l}){const a=$n(),s=localStorage.getItem("access_token"),r=JSON.parse(sessionStorage.getItem("current_user")),o=r?r.is_superuser?"admin":"operator":"guest",f=!!s;return w.useEffect(()=>{f&&!l.includes(o)&&a(-1)},[f,l,o,a]),f?l.includes(o)?v.jsx(Db,{}):null:v.jsx(Cb,{to:"/login"})}function Ew(){return v.jsx(n2,{children:v.jsxs(jb,{children:[v.jsx(ll,{path:"/",element:v.jsx(y2,{})}),v.jsx(ll,{path:"/login",element:v.jsx(_S,{})}),v.jsx(ll,{path:"/home",element:v.jsx(ow,{})}),v.jsx(ll,{path:"/reset-password",element:v.jsx(Sw,{})}),v.jsx(ll,{path:"/password/reset/confirm/:uid/:token",element:v.jsx(xw,{})}),v.jsx(ll,{element:v.jsx(ty,{roles:["admin"]}),children:v.jsx(ll,{path:"/user-management",element:v.jsx(yw,{})})}),v.jsx(ll,{element:v.jsx(ty,{roles:["admin","operator"]}),children:v.jsx(ll,{path:"/account",element:v.jsx(bw,{})})})]})})}k1.createRoot(document.getElementById("root")).render(v.jsx(w.StrictMode,{children:v.jsx(Ew,{})}));
