.submit-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  width: 100%;

  border-radius: 40px;
  background-color: var(--btn-color);
  color: var(--primary-color);

  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;

  transition: 0.5s ease;
  gap: 5px;
}

.submit-button:disabled {
  cursor: default;
  background-color: #d3d3d3;
  color: #808080;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--btn-hover-color);

  transform: scale(1.05);
  transition: 0.5s ease;
}

.password-errors-container {
  display: flex;
  flex-direction: column;
}

form {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 25px;
}

fieldset {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
}

label {
  font-size: 1rem;
  font-weight: 400;
  color: var(--secondary-dark-color);
}

fieldset input {
  display: flex;
  height: 44px;
  padding: 20px;
  background-color: var(--secondary-color);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

input:focus:not(input[type="checkbox"]) {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

.loading-ripple {
  height: 20px;
  position: relative;
  width: 20px;

  > div {
    animation: growAndFade 2s infinite ease-out;
    background-color: #808080;
    /* border: 2px solid #808080; */
    border-radius: 50%;
    height: 100%;
    opacity: 0;
    position: absolute;
    width: 100%;
  }

  .circle-1 {
    animation-delay: 0s;
  }
  .circle-2 {
    animation-delay: 0.5s;
  }
  .circle-3 {
    animation-delay: 1.5s;
  }
  .circle-4 {
    animation-delay: 2s;
  }
}

@keyframes growAndFade {
  0% {
    opacity: 1;
    transform: scale(0);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}
