# Minimal Dockerfile for Railway
FROM python:3.11

WORKDIR /app

# Copy and install requirements
COPY ../requirements.txt .
RUN pip install --upgrade pip
RUN pip install -r requirements.txt

# Copy project
COPY . .

# Create directories
RUN mkdir -p media/datasets media/profile_pictures

# Collect static files
RUN python manage.py collectstatic --noinput

# Start script
RUN echo '#!/bin/bash\nPORT=${PORT:-8000}\npython manage.py migrate --noinput\nexec daphne -b 0.0.0.0 -p $PORT auth.asgi:application' > /start.sh
RUN chmod +x /start.sh

EXPOSE 8000
CMD ["/start.sh"]
